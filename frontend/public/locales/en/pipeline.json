{"delete": "Delete", "filter": "Filter", "update": "Update", "create": "Create", "reorder": "Reorder", "cancel": "Cancel", "learn_more": "Learn more about pipelines", "reorder_pipeline": "Do you want to reorder pipeline?", "reorder_pipeline_description": "Logs are processed sequentially in processors and pipelines. Reordering it may change how data is processed by them.", "delete_pipeline": "Do you want to delete pipeline", "delete_pipeline_description": "Logs are processed sequentially in processors and pipelines. Deleting a pipeline may change content of data processed by other pipelines & processors", "add_new_pipeline": "Add a New Pipeline", "new_pipeline": "New Pipeline", "enter_edit_mode": "Enter Edit Mode", "save_configuration": "Save Configuration", "edit_pipeline": "<PERSON>", "create_pipeline": "Create New Pipeline", "add_new_processor": "Add Processor", "edit_processor": "Edit Processor", "create_processor": "Create New Processor", "processor_type": "Select Processor Type", "reorder_processor": "Do you want to reorder processor?", "reorder_processor_description": "Logs are processed sequentially in processors. Reordering it may change how data is processed by them.", "delete_processor": "Do you want to delete processor", "delete_processor_description": "Logs are processed sequentially in processors. Deleting a processor may change content of data processed by other processors", "search_pipeline_placeholder": "Filter Pi<PERSON>ines", "pipeline_name_placeholder": "Name", "pipeline_filter_placeholder": "Filter for selecting logs to be processed by this pipeline. Example: service_name = billing", "pipeline_tags_placeholder": "Tags", "pipeline_description_placeholder": "Enter description for your pipeline", "processor_name_placeholder": "Name", "processor_regex_placeholder": "Regex", "processor_parsefrom_placeholder": "Parse From", "processor_parseto_placeholder": "Parse To", "processor_onerror_placeholder": "on Error", "processor_pattern_placeholder": "Pattern", "processor_field_placeholder": "Field", "processor_value_placeholder": "Value", "processor_description_placeholder": "example rule: %{word:first}", "processor_trace_id_placeholder": "Parse Trace ID from", "processor_span_id_placeholder": "Parse Span ID from", "processor_trace_flags_placeholder": "Parse Trace flags from", "processor_from_placeholder": "From", "processor_to_placeholder": "To"}