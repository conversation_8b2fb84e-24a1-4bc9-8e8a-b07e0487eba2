{"SIGN_UP": "SigNoz | Sign Up", "LOGIN": "SigNoz | Login", "HOME": "SigNoz | Home", "SERVICE_METRICS": "SigNoz | Service Metrics", "SERVICE_MAP": "SigNoz | Service Map", "GET_STARTED_OLD": "SigNoz | Get Started", "ONBOARDING": "SigNoz | Get Started", "GET_STARTED_APPLICATION_MONITORING": "SigNoz | Get Started | APM", "GET_STARTED_LOGS_MANAGEMENT": "SigNoz | Get Started | Logs", "GET_STARTED_INFRASTRUCTURE_MONITORING": "SigNoz | Get Started | Infrastructure", "GET_STARTED_AWS_MONITORING": "SigNoz | Get Started | AWS", "GET_STARTED_AZURE_MONITORING": "SigNoz | Get Started | AZURE", "GET_STARTED": "SigNoz | Get Started with SigNoz Cloud", "GET_STARTED_WITH_CLOUD": "SigNoz | Get Started with SigNoz Cloud", "TRACE": "SigNoz | Trace", "TRACE_DETAIL": "SigNoz | Trace Detail", "TRACES_EXPLORER": "SigNoz | Traces Explorer", "SETTINGS": "SigNoz | Settings", "USAGE_EXPLORER": "SigNoz | Usage Explorer", "APPLICATION": "SigNoz | Home", "BILLING": "SigNoz | Billing", "ALL_DASHBOARD": "SigNoz | All Dashboards", "DASHBOARD": "SigNoz | Dashboard", "DASHBOARD_WIDGET": "SigNoz | Dashboard Widget", "EDIT_ALERTS": "SigNoz | Edit <PERSON>", "LIST_ALL_ALERT": "SigNoz | All Alerts", "ALERTS_NEW": "SigNoz | New Alert", "ALL_CHANNELS": "SigNoz | All Channels", "CHANNELS_NEW": "SigNoz | New Channel", "CHANNELS_EDIT": "SigNoz | Edit Channel", "ALL_ERROR": "SigNoz | All Errors", "ERROR_DETAIL": "SigNoz | Error Detail", "VERSION": "SigNoz | Version", "MY_SETTINGS": "SigNoz | My Settings", "ORG_SETTINGS": "SigNoz | Organization Settings", "INGESTION_SETTINGS": "SigNoz | Ingestion Settings", "API_KEYS": "SigNoz | API Keys", "CUSTOM_DOMAIN_SETTINGS": "SigNoz | Custom Domain Settings", "SOMETHING_WENT_WRONG": "SigNoz | Something Went Wrong", "UN_AUTHORIZED": "SigNoz | Unauthorized", "NOT_FOUND": "SigNoz | Page Not Found", "LOGS": "SigNoz | Logs", "LOGS_EXPLORER": "SigNoz | Logs Explorer", "OLD_LOGS_EXPLORER": "SigNoz | Old Logs Explorer", "LIVE_LOGS": "SigNoz | Live Logs", "LOGS_PIPELINES": "SigNoz | Logs Pipelines", "HOME_PAGE": "Open source Observability Platform | SigNoz", "PASSWORD_RESET": "SigNoz | Password Reset", "LIST_LICENSES": "SigNoz | List of Licenses", "WORKSPACE_LOCKED": "SigNoz | Workspace Locked", "WORKSPACE_SUSPENDED": "SigNoz | Workspace Suspended", "WORKSPACE_ACCESS_RESTRICTED": "SigNoz | Workspace Access Restricted", "SUPPORT": "SigNoz | Support", "LOGS_SAVE_VIEWS": "SigNoz | Logs Saved Views", "TRACES_SAVE_VIEWS": "SigNoz | Traces Saved Views", "TRACES_FUNNELS": "SigNoz | Traces Funnels", "DEFAULT": "Open source Observability Platform | SigNoz", "SHORTCUTS": "SigNoz | Shortcuts", "INTEGRATIONS": "SigNoz | Integrations", "ALERT_HISTORY": "SigNoz | Alert <PERSON> History", "ALERT_OVERVIEW": "SigNoz | Alert Rule Overview", "MESSAGING_QUEUES_OVERVIEW": "SigNoz | Messaging Queues", "MESSAGING_QUEUES_KAFKA": "SigNoz | Messaging Queues | Kafka", "MESSAGING_QUEUES_KAFKA_DETAIL": "SigNoz | Messaging Queues | Kafka", "MESSAGING_QUEUES_CELERY_TASK": "SigNoz | Messaging Queues | Celery", "INFRASTRUCTURE_MONITORING_HOSTS": "SigNoz | Infra Monitoring", "INFRASTRUCTURE_MONITORING_KUBERNETES": "SigNoz | Infra Monitoring", "METRICS_EXPLORER": "SigNoz | Metrics Explorer", "METRICS_EXPLORER_EXPLORER": "SigNoz | Metrics Explorer", "METRICS_EXPLORER_VIEWS": "SigNoz | Metrics Explorer", "API_MONITORING": "SigNoz | API Monitoring"}