{"preview_chart_unexpected_error": "An unexpeced error occurred updating the chart, please check your query.", "preview_chart_threshold_label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder_label_key_pair": "Click here to enter a label (key value pairs)", "button_yes": "Yes", "button_no": "No", "remove_label_confirm": "This action will remove all the labels. Do you want to proceed?", "remove_label_success": "Labels cleared", "alert_form_step1": "Step 1 - Define the metric", "alert_form_step2": "Step 2 - Define <PERSON>ert Conditions", "alert_form_step3": "Step 3 - <PERSON>ert Configuration", "metric_query_max_limit": "Can not create query. You can create maximum of 5 queries", "confirm_save_title": "Save Changes", "confirm_save_content_part1": "Your alert built with", "confirm_save_content_part2": "query will be saved. Press OK to confirm.", "unexpected_error": "Sorry, an unexpected error occurred. Please contact your admin", "rule_created": "Rule created successfully", "rule_edited": "Rule edited successfully", "expression_missing": "expression is missing in {{where}}", "metricname_missing": "metric name is missing in {{where}}", "condition_required": "at least one metric condition is required", "alertname_required": "alert name is required", "promql_required": "promql expression is required when query format is set to PromQL", "button_savechanges": "Save Rule", "button_createrule": "Create Rule", "button_returntorules": "Return to rules", "button_cancelchanges": "Cancel", "button_discard": "Discard", "text_condition1": "Send a notification when", "text_condition2": "the threshold", "text_condition3": "during the last", "option_5min": "5 mins", "option_10min": "10 mins", "option_15min": "15 mins", "option_60min": "60 mins", "option_4hours": "4 hours", "option_24hours": "24 hours", "field_threshold": "<PERSON><PERSON>", "option_allthetimes": "all the times", "option_atleastonce": "at least once", "option_onaverage": "on average", "option_intotal": "in total", "option_last": "last", "option_above": "above", "option_below": "below", "option_above_below": "above/below", "option_equal": "is equal to", "option_notequal": "not equal to", "button_query": "Query", "button_formula": "Formula", "tab_qb": "Query Builder", "tab_promql": "PromQL", "title_confirm": "Confirm", "button_ok": "Yes", "button_cancel": "No", "field_promql_expr": "PromQL Expression", "field_alert_name": "Alert <PERSON>", "field_alert_desc": "<PERSON><PERSON>", "field_notification_channel": "Notification Channel", "field_labels": "Labels", "field_severity": "Severity", "option_critical": "Critical", "option_error": "Error", "option_warning": "Warning", "option_info": "Info", "user_guide_headline": "Steps to create an Alert", "user_guide_qb_step1": "Step 1 - Define the metric", "user_guide_qb_step1a": "Choose a metric which you want to create an alert on", "user_guide_qb_step1b": "Filter it based on WHERE field or GROUPBY if needed", "user_guide_qb_step1c": "Apply an aggregatiion function like COUNT, SUM, etc. or choose NOOP to plot the raw metric", "user_guide_qb_step1d": "Create a formula based on Queries if needed", "user_guide_qb_step2": "Step 2 - Define <PERSON>ert Conditions", "user_guide_qb_step2a": "Select the evaluation interval, threshold type and whether you want to alert above/below a value", "user_guide_qb_step2b": "Enter the Alert threshold", "user_guide_qb_step3": "Step 3 -<PERSON>ert Configuration", "user_guide_qb_step3a": "Set alert severity, name and descriptions", "user_guide_qb_step3b": "Add tags to the alert in the Label field if needed", "user_guide_pql_step1": "Step 1 - Define the metric", "user_guide_pql_step1a": "Write a PromQL query for the metric", "user_guide_pql_step1b": "Format the legends based on labels you want to highlight", "user_guide_pql_step2": "Step 2 - Define <PERSON>ert Conditions", "user_guide_pql_step2a": "Select the threshold type and whether you want to alert above/below a value", "user_guide_pql_step2b": "Enter the Alert threshold", "user_guide_pql_step3": "Step 3 -<PERSON>ert Configuration", "user_guide_pql_step3a": "Set alert severity, name and descriptions", "user_guide_pql_step3b": "Add tags to the alert in the Label field if needed", "user_tooltip_more_help": "More details on how to create alerts"}