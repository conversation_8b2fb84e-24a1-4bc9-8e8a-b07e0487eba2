{"channel_delete_unexp_error": "Something went wrong", "channel_delete_success": "Channel Deleted Successfully", "column_channel_name": "Name", "column_channel_type": "Type", "column_channel_action": "Action", "column_channel_edit": "Edit", "button_new_channel": "New Alert Channel", "tooltip_notification_channels": "More details on how to setting notification channels", "sending_channels_note": "The alerts will be sent to all the configured channels.", "loading_channels_message": "Loading Channels..", "page_title_create": "New Notification Channels", "page_title_edit": "Edit Notification Channels", "button_save_channel": "Save", "button_test_channel": "Test", "button_return": "Back", "field_channel_name": "Name", "field_send_resolved": "Send resolved alerts", "field_channel_type": "Type", "field_webhook_url": "Webhook URL", "tooltip_webhook_url": "The URL of the webhook to send alerts to. Learn more about webhook integration in the docs [here](https://signoz.io/docs/alerts-management/notification-channel/webhook/). Integrates with [Incident.io](https://signoz.io/docs/alerts-management/notification-channel/incident-io/), [Rootly](https://signoz.io/docs/alerts-management/notification-channel/rootly/), [<PERSON>duty](https://signoz.io/docs/alerts-management/notification-channel/zenduty/) and [more](https://signoz.io/docs/alerts-management/notification-channel/webhook/#my-incident-management-tool-is-not-listed-can-i-still-integrate).", "tooltip_slack_url": "The URL of the slack [incoming webhook](https://docs.slack.dev/messaging/sending-messages-using-incoming-webhooks/) to send alerts to. Learn more about slack integration in the docs [here](https://signoz.io/docs/alerts-management/notification-channel/slack/).", "tooltip_pager_routing_key": "Learn how to obtain the routing key from your PagerDuty account [here](https://signoz.io/docs/alerts-management/notification-channel/pagerduty/#obtaining-integration-or-routing-key).", "tooltip_opsgenie_api_key": "Learn how to obtain the API key from your OpsGenie account [here](https://support.atlassian.com/opsgenie/docs/************************prometheus/).", "tooltip_email_to": "Enter email addresses separated by commas.", "tooltip_ms_teams_url": "The URL of the Microsoft Teams [webhook](https://support.microsoft.com/en-us/office/create-incoming-webhooks-with-workflows-for-microsoft-teams-8ae491c7-0394-4861-ba59-055e33f75498) to send alerts to. Learn more about Microsoft Teams integration in the docs [here](https://signoz.io/docs/alerts-management/notification-channel/ms-teams/).", "field_slack_recipient": "Recipient", "field_slack_title": "Title", "field_slack_description": "Description", "field_webhook_username": "User Name (optional)", "field_webhook_password": "Password (optional)", "field_pager_routing_key": "Routing Key", "field_pager_description": "Description", "field_pager_severity": "Severity", "field_pager_details": "Additional Information", "field_pager_component": "Component", "field_pager_group": "Group", "field_pager_class": "Class", "field_pager_client": "Client", "field_pager_client_url": "Client URL", "placeholder_slack_description": "Description", "placeholder_pager_description": "Description", "help_pager_client": "Shows up as event source in Pagerduty", "help_pager_client_url": "Shows up as event source link in Pagerduty", "help_pager_class": "The class/type of the event", "help_pager_details": "Specify a key-value format (must be a valid json)", "help_pager_group": "A cluster or grouping of sources", "help_pager_component": "The part or component of the affected system that is broke", "help_pager_severity": "Severity of the incident, must be one of: must be one of the following: 'critical', 'warning', 'error' or 'info'", "help_webhook_username": "Leave empty for bearer auth or when authentication is not necessary.", "help_webhook_password": "Specify a password or bearer token", "help_pager_description": "Shows up as description in pagerduty", "channel_creation_done": "Successfully created the channel", "channel_creation_failed": "An unexpected error occurred while creating this channel", "channel_edit_done": "Channels Edited Successfully", "channel_edit_failed": "An unexpected error occurred while updating this channel", "selected_channel_invalid": "Channel type selected is invalid", "username_no_password": "A Password must be provided with user name", "test_unsupported": "Sorry, this channel type does not support test yet", "channel_test_done": "An alert has been sent to this channel", "channel_test_failed": "Failed to send a test message to this channel, please confirm that the parameters are set correctly", "channel_test_unexpected": "An unexpected error occurred while sending a message to this channel, please try again", "webhook_url_required": "Webhook URL is mandatory", "slack_channel_help": "Specify channel or user, use #channel-name, @username (has to be all lowercase, no whitespace)"}