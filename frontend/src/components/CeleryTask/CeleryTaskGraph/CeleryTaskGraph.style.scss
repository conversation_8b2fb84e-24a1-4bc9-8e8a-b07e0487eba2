.celery-task-graph-grid-container {
	width: 100%;
	display: grid;
	grid-template-rows: 1fr;
	gap: 10px;
	padding-bottom: 16px;
	margin-bottom: 16px;

	.celery-task-graph-bar,
	.celery-task-graph-task-latency {
		height: 380px !important;
		width: 100%;
		box-sizing: border-box;

		.celery-task-graph-grid-content {
			padding: 6px;
			height: 100%;
		}

		.ant-card-body {
			height: calc(100% - 18px);

			.widget-graph-container {
				&.bar {
					height: calc(100% - 110px);
				}

				&.graph {
					height: calc(100% - 80px);
				}
			}
		}
	}

	.celery-task-graph-worker-count {
		height: 100%;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		border: 1px solid var(--bg-slate-500);
		background: linear-gradient(
				0deg,
				rgba(171, 189, 255, 0) 0%,
				rgba(171, 189, 255, 0) 100%
			),
			#0b0c0e;

		.ant-card-body {
			height: 100%;
			width: 100%;
		}

		.worker-count-text-container {
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;

			.celery-task-graph-worker-count-text {
				font-size: 2.5vw;
				text-align: center;
			}
		}

		.worker-count-header {
			display: flex;
			justify-content: start;
			align-items: start;
			position: absolute;
			height: 100%;
			width: 100%;
		}
	}

	.celery-task-graph {
		height: 380px !important;
		padding: 6px;
		width: 100%;
		box-sizing: border-box;

		.ant-card-body {
			height: calc(100% - 18px);

			.widget-graph-container {
				&.bar {
					height: calc(100% - 110px);
				}
			}
		}
	}

	.celery-task-graph-grid-bottom {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		align-items: flex-start;
		gap: 10px;
		width: 100%;

		.celery-task-graph {
			height: 380px !important;
			padding: 10px;
			width: 100%;
			box-sizing: border-box;

			.ant-card-body {
				height: calc(100% - 18px);
			}
		}
	}

	.metric-based-graphs,
	.trace-based-graphs {
		display: flex;
		flex-direction: column;
		gap: 10px;

		.metric-page-grid {
			display: flex;
			flex-direction: row;
			gap: 10px;
			width: 100%;

			.celery-task-graph {
				height: 280px !important;
			}
		}
	}

	.trace-based-graphs {
		.trace-based-graphs-header {
			display: grid;
			grid-template-columns: 50% 50%;
			align-items: center;
			gap: 24px;
			width: 100%;
		}
	}

	.configure-option-Info {
		display: grid;
		grid-template-columns: max-content 1fr;
		gap: 16px;
		align-items: center;

		border: 1px dashed var(--bg-slate-50);
		border-radius: 4px;
		padding: 6px 24px 6px 12px;
		width: max-content;

		.configure-option-Info-text {
			color: var(--bg-vanilla-400);
			font-size: 12px;
			font-weight: 400;
		}
	}

	.row-panel {
		border-radius: 4px;
		background: rgba(18, 19, 23, 0.4);
		padding: 8px;
		display: flex;
		gap: 6px;
		align-items: center;
		height: 48px !important;
		width: 100%;

		.ant-typography {
			font-size: 14px;
			font-weight: 500;
		}

		.row-panel-section {
			display: flex;
			gap: 6px;
			align-items: center;

			.row-icon {
				color: var(--bg-vanilla-400);
				cursor: pointer;
			}

			.section-title {
				color: var(--bg-vanilla-400);
				font-family: Inter;
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 20px;
				letter-spacing: -0.07px;
			}
		}
	}
}

.celery-task-states {
	border-bottom: 1px solid var(--bg-ink-200);

	&__tab {
		min-width: 140px;
		padding: 12px 13px 12px 12px;
		cursor: pointer;
		position: relative;

		&:not([data-last-tab='true']) {
			border-right: 1px solid var(--bg-ink-200);
		}

		&--selected {
			background-color: rgba(38, 38, 38, 0.5);
		}
	}

	&__label-wrapper {
		margin-bottom: 4px;
		display: flex;
		flex-direction: column;
		gap: 8px;
	}

	&__label {
		font-family: 'Inter';
		font-size: 14px;
		color: var(--bg-vanilla-400);
		line-height: 20px;
		font-weight: 500;
	}

	&__value {
		font-family: 'Geist Mono';
		font-size: 24px;
		color: var(--bg-vanilla-100);
		line-height: 32px;
	}

	&__indicator {
		position: absolute;
		width: 100%;
		height: 2px;
		bottom: 0;
		left: 0;
		background-color: var(--bg-vanilla-100);
	}
}

.lightMode {
	.celery-task-graph-grid-container {
		.celery-task-graph-worker-count {
			border: 1px solid var(--bg-vanilla-300);
			background: unset;
		}

		.row-panel .row-panel-section .section-title {
			color: var(--bg-ink-400);
		}
	}

	.celery-task-states {
		border-bottom: 1px solid var(--bg-vanilla-300);

		&__tab {
			&:not([data-last-tab='true']) {
				border-right: 1px solid var(--bg-vanilla-300);
			}

			&--selected {
				background-color: var(--bg-vanilla-200);
			}
		}

		&__label {
			color: var(--bg-ink-500);
		}

		&__value {
			color: var(--bg-slate-100);
		}

		&__indicator {
			background-color: var(--bg-ink-400);
		}
	}

	.configure-option-Info {
		border: 1px dashed var(--bg-robin-400);
	}
}
