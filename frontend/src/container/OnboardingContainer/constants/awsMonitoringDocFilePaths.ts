/* eslint-disable simple-import-sort/imports */

// Application Logs Start

// LINUX AMD 64
import AwsMonitoring_awsEc2ApplicationLogs_linuxAMD64_setupOtelCollector from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/LinuxAMD64/appplicationLogs-linuxamd64-installOtelCollector.md';
import AwsMonitoring_awsEc2ApplicationLogs_linuxAMD64_configureReceiver from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/LinuxAMD64/appplicationLogs-linuxamd64-configureReceiver.md';
import AwsMonitoring_awsEc2ApplicationLogs_linuxAMD64_restartOtelCollector from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/LinuxAMD64/appplicationLogs-linuxamd64-runOtelCollector.md';

// LINUX ARM 64
import AwsMonitoring_awsEc2ApplicationLogs_linuxARM64_setupOtelCollector from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/LinuxARM64/appplicationLogs-linuxarm64-installOtelCollector.md';
import AwsMonitoring_awsEc2ApplicationLogs_linuxARM64_configureReceiver from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/LinuxARM64/appplicationLogs-linuxarm64-configureReceiver.md';
import AwsMonitoring_awsEc2ApplicationLogs_linuxARM64_restartOtelCollector from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/LinuxARM64/appplicationLogs-linuxarm64-runOtelCollector.md';

// MacOS AMD 64
import AwsMonitoring_awsEc2ApplicationLogs_macOsAMD64_setupOtelCollector from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/MacOsAMD64/appplicationLogs-macosamd64-installOtelCollector.md';
import AwsMonitoring_awsEc2ApplicationLogs_macOsAMD64_configureReceiver from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/MacOsAMD64/appplicationLogs-macosamd64-configureReceiver.md';
import AwsMonitoring_awsEc2ApplicationLogs_macOsAMD64_restartOtelCollector from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/MacOsAMD64/appplicationLogs-macosamd64-runOtelCollector.md';

// MacOS ARM 64
import AwsMonitoring_awsEc2ApplicationLogs_macOsARM64_setupOtelCollector from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/MacOsARM64/appplicationLogs-macosarm64-installOtelCollector.md';
import AwsMonitoring_awsEc2ApplicationLogs_macOsARM64_configureReceiver from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/MacOsARM64/appplicationLogs-macosarm64-configureReceiver.md';
import AwsMonitoring_awsEc2ApplicationLogs_macOsARM64_restartOtelCollector from '../Modules/AwsMonitoring/EC2ApplicationLogs/md-docs/MacOsARM64/appplicationLogs-macosarm64-runOtelCollector.md';
// Application Logs End

// Hostmetrics Start
// LINUX AMD 64
import AwsMonitoring_awsEc2InfrastructureMetrics_linuxAMD64_setupOtelCollector from '../Modules/AwsMonitoring/EC2InfrastructureMetrics/md-docs/LinuxAMD64/hostmetrics-setupOtelCollector.md';
import AwsMonitoring_awsEc2InfrastructureMetrics_linuxAMD64_configureHostmetricsJson from '../Modules/AwsMonitoring/EC2InfrastructureMetrics/md-docs/LinuxAMD64/hostmetrics-configureHostmetricsJson.md';

// LINUX ARM 64
import AwsMonitoring_awsEc2InfrastructureMetrics_linuxARM64_setupOtelCollector from '../Modules/AwsMonitoring/EC2InfrastructureMetrics/md-docs/LinuxARM64/hostmetrics-setupOtelCollector.md';
import AwsMonitoring_awsEc2InfrastructureMetrics_linuxARM64_configureHostmetricsJson from '../Modules/AwsMonitoring/EC2InfrastructureMetrics/md-docs/LinuxARM64/hostmetrics-configureHostmetricsJson.md';

// MacOS AMD 64
import AwsMonitoring_awsEc2InfrastructureMetrics_macOsAMD64_setupOtelCollector from '../Modules/AwsMonitoring/EC2InfrastructureMetrics/md-docs/MacOsAMD64/hostmetrics-setupOtelCollector.md';
import AwsMonitoring_awsEc2InfrastructureMetrics_macOsAMD64_configureHostmetricsJson from '../Modules/AwsMonitoring/EC2InfrastructureMetrics/md-docs/MacOsAMD64/hostmetrics-configureHostmetricsJson.md';

// MacOS ARM 64
import AwsMonitoring_awsEc2InfrastructureMetrics_macOsARM64_setupOtelCollector from '../Modules/AwsMonitoring/EC2InfrastructureMetrics/md-docs/MacOsARM64/hostmetrics-setupOtelCollector.md';
import AwsMonitoring_awsEc2InfrastructureMetrics_macOsARM64_configureHostmetricsJson from '../Modules/AwsMonitoring/EC2InfrastructureMetrics/md-docs/MacOsARM64/hostmetrics-configureHostmetricsJson.md';

// Hostmetrics End
// AwsMonitoring_awsEc2ApplicationLogs_linuxAMD64_restartOtelCollector,

import AwsMonitoring_awsEcsEc2_setupDaemonService from '../Modules/AwsMonitoring/ECSEc2/md-docs/ecsEc2-setupDeamonService.md';
import AwsMonitoring_awsEcsEc2_createOtelConfig from '../Modules/AwsMonitoring/ECSEc2/md-docs/ecsEc2-createOtelConfig.md';
import AwsMonitoring_awsEcsEc2_createDaemonService from '../Modules/AwsMonitoring/ECSEc2/md-docs/ecsEc2-createDaemonService.md';
import AwsMonitoring_awsEcsEc2_ecsSendData from '../Modules/AwsMonitoring/ECSEc2/md-docs/ecsEc2-sendData.md';

import AwsMonitoring_awsEcsExternal_setupDaemonService from '../Modules/AwsMonitoring/ECSExternal/md-docs/ecsExternal-setupDeamonService.md';
import AwsMonitoring_awsEcsExternal_createOtelConfig from '../Modules/AwsMonitoring/ECSExternal/md-docs/ecsExternal-createOtelConfig.md';
import AwsMonitoring_awsEcsExternal_createDaemonService from '../Modules/AwsMonitoring/ECSExternal/md-docs/ecsExternal-createDaemonService.md';
import AwsMonitoring_awsEcsExternal_ecsSendData from '../Modules/AwsMonitoring/ECSExternal/md-docs/ecsExternal-sendData.md';

import AwsMonitoring_awsEcsFargate_createOtelConfig from '../Modules/AwsMonitoring/ECSFargate/md-docs/ecsFargate-createOtelConfig.md';
import AwsMonitoring_awsEcsFargate_createSidecarCollectorContainer from '../Modules/AwsMonitoring/ECSFargate/md-docs/ecsFargate-createSidecarCollectorContainer.md';
import AwsMonitoring_awsEcsFargate_deployTaskDefinition from '../Modules/AwsMonitoring/ECSFargate/md-docs/ecsFargate-deployTaskDefinition.md';
import AwsMonitoring_awsEcsFargate_ecsSendData from '../Modules/AwsMonitoring/ECSFargate/md-docs/ecsFargate-sendData.md';
import AwsMonitoring_awsEcsFargate_ecsSendLogsData from '../Modules/AwsMonitoring/ECSFargate/md-docs/ecsFargate-sendLogs.md';

// AWS EKS

import AwsMonitoring_awsEks_setupOtelCollector from '../Modules/AwsMonitoring/EKS/eks-installOtelCollector.md';
import AwsMonitoring_awsEks_monitorDashboard from '../Modules/AwsMonitoring/EKS/eks-monitorUsingDashboard.md';

export const AwsMonitoringDocFilePaths = {
	/// /// AWS EC2 Application Logs

	// Linux AMD64
	AwsMonitoring_awsEc2ApplicationLogs_linuxAMD64_setupOtelCollector,
	AwsMonitoring_awsEc2ApplicationLogs_linuxAMD64_configureReceiver,
	AwsMonitoring_awsEc2ApplicationLogs_linuxAMD64_restartOtelCollector,
	// LINUX ARM 64
	AwsMonitoring_awsEc2ApplicationLogs_linuxARM64_setupOtelCollector,
	AwsMonitoring_awsEc2ApplicationLogs_linuxARM64_configureReceiver,
	AwsMonitoring_awsEc2ApplicationLogs_linuxARM64_restartOtelCollector,
	// MacOS AMD 64
	AwsMonitoring_awsEc2ApplicationLogs_macOsAMD64_setupOtelCollector,
	AwsMonitoring_awsEc2ApplicationLogs_macOsAMD64_configureReceiver,
	AwsMonitoring_awsEc2ApplicationLogs_macOsAMD64_restartOtelCollector,
	// MacOS ARM 64
	AwsMonitoring_awsEc2ApplicationLogs_macOsARM64_setupOtelCollector,
	AwsMonitoring_awsEc2ApplicationLogs_macOsARM64_configureReceiver,
	AwsMonitoring_awsEc2ApplicationLogs_macOsARM64_restartOtelCollector,

	/// /// AWS EC2 Infrastructure Metrics

	// Linux AMD64
	AwsMonitoring_awsEc2InfrastructureMetrics_linuxAMD64_setupOtelCollector,
	AwsMonitoring_awsEc2InfrastructureMetrics_linuxAMD64_configureHostmetricsJson,

	// Linux ARM64
	AwsMonitoring_awsEc2InfrastructureMetrics_linuxARM64_setupOtelCollector,
	AwsMonitoring_awsEc2InfrastructureMetrics_linuxARM64_configureHostmetricsJson,

	// MacOS AMD64
	AwsMonitoring_awsEc2InfrastructureMetrics_macOsAMD64_setupOtelCollector,
	AwsMonitoring_awsEc2InfrastructureMetrics_macOsAMD64_configureHostmetricsJson,

	// MacOS ARM64
	AwsMonitoring_awsEc2InfrastructureMetrics_macOsARM64_setupOtelCollector,
	AwsMonitoring_awsEc2InfrastructureMetrics_macOsARM64_configureHostmetricsJson,

	/// //// AWS ECS EC2
	AwsMonitoring_awsEcsEc2_setupDaemonService,
	AwsMonitoring_awsEcsEc2_createOtelConfig,
	AwsMonitoring_awsEcsEc2_createDaemonService,
	AwsMonitoring_awsEcsEc2_ecsSendData,

	/// //// AWS ECS External
	AwsMonitoring_awsEcsExternal_setupDaemonService,
	AwsMonitoring_awsEcsExternal_createOtelConfig,
	AwsMonitoring_awsEcsExternal_createDaemonService,
	AwsMonitoring_awsEcsExternal_ecsSendData,

	/// //// AWS ECS Fargate
	AwsMonitoring_awsEcsFargate_createOtelConfig,
	AwsMonitoring_awsEcsFargate_createSidecarCollectorContainer,
	AwsMonitoring_awsEcsFargate_deployTaskDefinition,
	AwsMonitoring_awsEcsFargate_ecsSendData,
	AwsMonitoring_awsEcsFargate_ecsSendLogsData,

	/// /// AWS EKS
	AwsMonitoring_awsEks_setupOtelCollector,
	AwsMonitoring_awsEks_monitorDashboard,
};
