/// ///////// APM

/// // Java Start

// SpringBoot-Kubernetes
// ----------------------------------------------------------------------------
// //// Windows
// dotnet-windows-instrument-app
/// ////// JavaScript Done
/// ///// Go Start
// Go-Kubernetes
/// /// ROR Done
/// /// .NET Start
// dotnet Docker
import APM_dotnet_docker_quickStart_instrumentApplication from '../Modules/APM/Dotnet/md-docs/Docker/QuickStart/dotnet-docker-quickStart-instrumentApplication.md';
import APM_dotnet_docker_quickStart_runApplication from '../Modules/APM/Dotnet/md-docs/Docker/QuickStart/dotnet-docker-quickStart-runApplication.md';
// dotnet-LinuxAMD64-recommended
import APM_dotnet_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Dotnet/md-docs/Docker/Recommended/dotnet-docker-recommended-installOtelCollector.md';
import APM_dotnet_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Dotnet/md-docs/Docker/Recommended/dotnet-docker-recommended-instrumentApplication.md';
import APM_dotnet_docker_recommendedSteps_runApplication from '../Modules/APM/Dotnet/md-docs/Docker/Recommended/dotnet-docker-recommended-runApplication.md';
// dotnet-Kubernetes
import APM_dotnet_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Dotnet/md-docs/Kubernetes/dotnet-kubernetes-installOtelCollector.md';
import APM_dotnet_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Dotnet/md-docs/Kubernetes/dotnet-kubernetes-instrumentApplication.md';
import APM_dotnet_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Dotnet/md-docs/Kubernetes/dotnet-kubernetes-runApplication.md';
// dotnet-LinuxAMD64-quickstart
import APM_dotnet_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Dotnet/md-docs/LinuxAMD64/QuickStart/dotnet-linuxamd64-quickStart-instrumentApplication.md';
import APM_dotnet_linuxAMD64_quickStart_runApplication from '../Modules/APM/Dotnet/md-docs/LinuxAMD64/QuickStart/dotnet-linuxamd64-quickStart-runApplication.md';
// dotnet-LinuxAMD64-recommended
import APM_dotnet_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Dotnet/md-docs/LinuxAMD64/Recommended/dotnet-linuxamd64-recommended-installOtelCollector.md';
import APM_dotnet_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Dotnet/md-docs/LinuxAMD64/Recommended/dotnet-linuxamd64-recommended-instrumentApplication.md';
import APM_dotnet_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Dotnet/md-docs/LinuxAMD64/Recommended/dotnet-linuxamd64-recommended-runApplication.md';
// dotnet-LinuxARM64-quickstart
import APM_dotnet_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Dotnet/md-docs/LinuxARM64/QuickStart/dotnet-linuxarm64-quickStart-instrumentApplication.md';
import APM_dotnet_linuxARM64_quickStart_runApplication from '../Modules/APM/Dotnet/md-docs/LinuxARM64/QuickStart/dotnet-linuxarm64-quickStart-runApplication.md';
// dotnet-LinuxARM64-recommended
import APM_dotnet_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Dotnet/md-docs/LinuxARM64/Recommended/dotnet-linuxarm64-recommended-installOtelCollector.md';
import APM_dotnet_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Dotnet/md-docs/LinuxARM64/Recommended/dotnet-linuxarm64-recommended-instrumentApplication.md';
import APM_dotnet_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Dotnet/md-docs/LinuxARM64/Recommended/dotnet-linuxarm64-recommended-runApplication.md';
// dotnet-MacOsAMD64-quickstart
import APM_dotnet_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Dotnet/md-docs/MacOsAMD64/QuickStart/dotnet-macosamd64-quickStart-instrumentApplication.md';
import APM_dotnet_macOsAMD64_quickStart_runApplication from '../Modules/APM/Dotnet/md-docs/MacOsAMD64/QuickStart/dotnet-macosamd64-quickStart-runApplication.md';
// dotnet-MacOsAMD64-recommended
import APM_dotnet_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Dotnet/md-docs/MacOsAMD64/Recommended/dotnet-macosamd64-recommended-installOtelCollector.md';
import APM_dotnet_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Dotnet/md-docs/MacOsAMD64/Recommended/dotnet-macosamd64-recommended-instrumentApplication.md';
import APM_dotnet_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Dotnet/md-docs/MacOsAMD64/Recommended/dotnet-macosamd64-recommended-runApplication.md';
// dotnet-MacOsARM64-quickstart
import APM_dotnet_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Dotnet/md-docs/MacOsARM64/QuickStart/dotnet-macosarm64-quickStart-instrumentApplication.md';
import APM_dotnet_macOsARM64_quickStart_runApplication from '../Modules/APM/Dotnet/md-docs/MacOsARM64/QuickStart/dotnet-macosarm64-quickStart-runApplication.md';
// dotnet-MacOsARM64-recommended
import APM_dotnet_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Dotnet/md-docs/MacOsARM64/Recommended/dotnet-macosarm64-recommended-installOtelCollector.md';
import APM_dotnet_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Dotnet/md-docs/MacOsARM64/Recommended/dotnet-macosarm64-recommended-instrumentApplication.md';
import APM_dotnet_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Dotnet/md-docs/MacOsARM64/Recommended/dotnet-macosarm64-recommended-runApplication.md';
import APM_dotnet_windows_quickStart_instrumentApplication from '../Modules/APM/Dotnet/md-docs/Windows/QuickStart/dotnet-windows-quickStart-instrumentApplication.md';
import APM_dotnet_windows_quickStart_runApplication from '../Modules/APM/Dotnet/md-docs/Windows/QuickStart/dotnet-windows-quickStart-runApplication.md';
// dotnet-Windows-recommended
import APM_dotnet_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Dotnet/md-docs/Windows/Recommended/dotnet-windows-recommended-installOtelCollector.md';
import APM_dotnet_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Dotnet/md-docs/Windows/Recommended/dotnet-windows-recommended-instrumentApplication.md';
import APM_dotnet_windows_recommendedSteps_runApplication from '../Modules/APM/Dotnet/md-docs/Windows/Recommended/dotnet-windows-recommended-runApplication.md';
// Elixir Docker
import APM_elixir_docker_quickStart_instrumentApplication from '../Modules/APM/Elixir/md-docs/Docker/QuickStart/elixir-docker-quickStart-instrumentApplication.md';
import APM_elixir_docker_quickStart_runApplication from '../Modules/APM/Elixir/md-docs/Docker/QuickStart/elixir-docker-quickStart-runApplication.md';
// Elixir-LinuxAMD64-recommended
import APM_elixir_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Elixir/md-docs/Docker/Recommended/elixir-docker-recommended-installOtelCollector.md';
import APM_elixir_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Elixir/md-docs/Docker/Recommended/elixir-docker-recommended-instrumentApplication.md';
import APM_elixir_docker_recommendedSteps_runApplication from '../Modules/APM/Elixir/md-docs/Docker/Recommended/elixir-docker-recommended-runApplication.md';
// Elixir-Kubernetes
import APM_elixir_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Elixir/md-docs/Kubernetes/elixir-kubernetes-installOtelCollector.md';
import APM_elixir_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Elixir/md-docs/Kubernetes/elixir-kubernetes-instrumentApplication.md';
import APM_elixir_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Elixir/md-docs/Kubernetes/elixir-kubernetes-runApplication.md';
// Elixir-LinuxAMD64-quickstart
import APM_elixir_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Elixir/md-docs/LinuxAMD64/QuickStart/elixir-linuxamd64-quickStart-instrumentApplication.md';
import APM_elixir_linuxAMD64_quickStart_runApplication from '../Modules/APM/Elixir/md-docs/LinuxAMD64/QuickStart/elixir-linuxamd64-quickStart-runApplication.md';
// Elixir-LinuxAMD64-recommended
import APM_elixir_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Elixir/md-docs/LinuxAMD64/Recommended/elixir-linuxamd64-recommended-installOtelCollector.md';
import APM_elixir_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Elixir/md-docs/LinuxAMD64/Recommended/elixir-linuxamd64-recommended-instrumentApplication.md';
import APM_elixir_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Elixir/md-docs/LinuxAMD64/Recommended/elixir-linuxamd64-recommended-runApplication.md';
// Elixir-LinuxARM64-quickstart
import APM_elixir_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Elixir/md-docs/LinuxARM64/QuickStart/elixir-linuxarm64-quickStart-instrumentApplication.md';
import APM_elixir_linuxARM64_quickStart_runApplication from '../Modules/APM/Elixir/md-docs/LinuxARM64/QuickStart/elixir-linuxarm64-quickStart-runApplication.md';
// Elixir-LinuxARM64-recommended
import APM_elixir_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Elixir/md-docs/LinuxARM64/Recommended/elixir-linuxarm64-recommended-installOtelCollector.md';
import APM_elixir_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Elixir/md-docs/LinuxARM64/Recommended/elixir-linuxarm64-recommended-instrumentApplication.md';
import APM_elixir_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Elixir/md-docs/LinuxARM64/Recommended/elixir-linuxarm64-recommended-runApplication.md';
// Elixir-MacOsAMD64-quickstart
import APM_elixir_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Elixir/md-docs/MacOsAMD64/QuickStart/elixir-macosamd64-quickStart-instrumentApplication.md';
import APM_elixir_macOsAMD64_quickStart_runApplication from '../Modules/APM/Elixir/md-docs/MacOsAMD64/QuickStart/elixir-macosamd64-quickStart-runApplication.md';
// Elixir-MacOsAMD64-recommended
import APM_elixir_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Elixir/md-docs/MacOsAMD64/Recommended/elixir-macosamd64-recommended-installOtelCollector.md';
import APM_elixir_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Elixir/md-docs/MacOsAMD64/Recommended/elixir-macosamd64-recommended-instrumentApplication.md';
import APM_elixir_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Elixir/md-docs/MacOsAMD64/Recommended/elixir-macosamd64-recommended-runApplication.md';
// Elixir-MacOsARM64-quickstart
import APM_elixir_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Elixir/md-docs/MacOsARM64/QuickStart/elixir-macosarm64-quickStart-instrumentApplication.md';
import APM_elixir_macOsARM64_quickStart_runApplication from '../Modules/APM/Elixir/md-docs/MacOsARM64/QuickStart/elixir-macosarm64-quickStart-runApplication.md';
// Elixir-MacOsARM64-recommended
import APM_elixir_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Elixir/md-docs/MacOsARM64/Recommended/elixir-macosarm64-recommended-installOtelCollector.md';
import APM_elixir_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Elixir/md-docs/MacOsARM64/Recommended/elixir-macosarm64-recommended-instrumentApplication.md';
import APM_elixir_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Elixir/md-docs/MacOsARM64/Recommended/elixir-macosarm64-recommended-runApplication.md';
// Elixir Docker
import APM_elixir_windows_quickStart_instrumentApplication from '../Modules/APM/Elixir/md-docs/Windows/QuickStart/elixir-windows-quickStart-instrumentApplication.md';
import APM_elixir_windows_quickStart_runApplication from '../Modules/APM/Elixir/md-docs/Windows/QuickStart/elixir-windows-quickStart-runApplication.md';
// Elixir-LinuxAMD64-recommended
import APM_elixir_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Elixir/md-docs/Windows/Recommended/elixir-windows-recommended-installOtelCollector.md';
import APM_elixir_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Elixir/md-docs/Windows/Recommended/elixir-windows-recommended-instrumentApplication.md';
import APM_elixir_windows_recommendedSteps_runApplication from '../Modules/APM/Elixir/md-docs/Windows/Recommended/elixir-windows-recommended-runApplication.md';
// Golang Docker
import APM_go_docker_quickStart_instrumentApplication from '../Modules/APM/GoLang/md-docs/Docker/QuickStart/golang-docker-quickStart-instrumentApplication.md';
import APM_go_docker_quickStart_runApplication from '../Modules/APM/GoLang/md-docs/Docker/QuickStart/golang-docker-quickStart-runApplication.md';
// Go-LinuxAMD64-recommended
import APM_go_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/GoLang/md-docs/Docker/Recommended/golang-docker-recommended-installOtelCollector.md';
import APM_go_docker_recommendedSteps_instrumentApplication from '../Modules/APM/GoLang/md-docs/Docker/Recommended/golang-docker-recommended-instrumentApplication.md';
import APM_go_docker_recommendedSteps_runApplication from '../Modules/APM/GoLang/md-docs/Docker/Recommended/golang-docker-recommended-runApplication.md';
// Golang Kubernetes
import APM_go_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/GoLang/md-docs/Kubernetes/golang-kubernetes-installOtelCollector.md';
import APM_go_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/GoLang/md-docs/Kubernetes/golang-kubernetes-instrumentApplication.md';
import APM_go_kubernetes_recommendedSteps_runApplication from '../Modules/APM/GoLang/md-docs/Kubernetes/golang-kubernetes-runApplication.md';
// Go-LinuxAMD64-quickstart
import APM_go_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/GoLang/md-docs/LinuxAMD64/QuickStart/golang-linuxamd64-quickStart-instrumentApplication.md';
import APM_go_linuxAMD64_quickStart_runApplication from '../Modules/APM/GoLang/md-docs/LinuxAMD64/QuickStart/golang-linuxamd64-quickStart-runApplication.md';
// Go-LinuxAMD64-recommended
import APM_go_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/GoLang/md-docs/LinuxAMD64/Recommended/golang-linuxamd64-recommended-installOtelCollector.md';
import APM_go_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/GoLang/md-docs/LinuxAMD64/Recommended/golang-linuxamd64-recommended-instrumentApplication.md';
import APM_go_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/GoLang/md-docs/LinuxAMD64/Recommended/golang-linuxamd64-recommended-runApplication.md';
// Go-LinuxARM64-quickstart
import APM_go_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/GoLang/md-docs/LinuxARM64/QuickStart/golang-linuxarm64-quickStart-instrumentApplication.md';
import APM_go_linuxARM64_quickStart_runApplication from '../Modules/APM/GoLang/md-docs/LinuxARM64/QuickStart/golang-linuxarm64-quickStart-runApplication.md';
// Go-LinuxARM64-recommended
import APM_go_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/GoLang/md-docs/LinuxARM64/Recommended/golang-linuxarm64-recommended-installOtelCollector.md';
import APM_go_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/GoLang/md-docs/LinuxARM64/Recommended/golang-linuxarm64-recommended-instrumentApplication.md';
import APM_go_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/GoLang/md-docs/LinuxARM64/Recommended/golang-linuxarm64-recommended-runApplication.md';
// Go-MacOsAMD64-quickstart
import APM_go_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/GoLang/md-docs/MacOsAMD64/QuickStart/golang-macosamd64-quickStart-instrumentApplication.md';
import APM_go_macOsAMD64_quickStart_runApplication from '../Modules/APM/GoLang/md-docs/MacOsAMD64/QuickStart/golang-macosamd64-quickStart-runApplication.md';
// Go-MacOsAMD64-recommended
import APM_go_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/GoLang/md-docs/MacOsAMD64/Recommended/golang-macosamd64-recommended-installOtelCollector.md';
import APM_go_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/GoLang/md-docs/MacOsAMD64/Recommended/golang-macosamd64-recommended-instrumentApplication.md';
import APM_go_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/GoLang/md-docs/MacOsAMD64/Recommended/golang-macosamd64-recommended-runApplication.md';
// Go-MacOsARM64-quickstart
import APM_go_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/GoLang/md-docs/MacOsARM64/QuickStart/golang-macosarm64-quickStart-instrumentApplication.md';
import APM_go_macOsARM64_quickStart_runApplication from '../Modules/APM/GoLang/md-docs/MacOsARM64/QuickStart/golang-macosarm64-quickStart-runApplication.md';
// Go-MacOsARM64-recommended
import APM_go_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/GoLang/md-docs/MacOsARM64/Recommended/golang-macosarm64-recommended-installOtelCollector.md';
import APM_go_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/GoLang/md-docs/MacOsARM64/Recommended/golang-macosarm64-recommended-instrumentApplication.md';
import APM_go_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/GoLang/md-docs/MacOsARM64/Recommended/golang-macosarm64-recommended-runApplication.md';
// Golang Docker
import APM_go_windows_quickStart_instrumentApplication from '../Modules/APM/GoLang/md-docs/Windows/QuickStart/golang-windows-quickStart-instrumentApplication.md';
import APM_go_windows_quickStart_runApplication from '../Modules/APM/GoLang/md-docs/Windows/QuickStart/golang-windows-quickStart-runApplication.md';
// Go-LinuxAMD64-recommended
import APM_go_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/GoLang/md-docs/Windows/Recommended/golang-windows-recommended-installOtelCollector.md';
import APM_go_windows_recommendedSteps_instrumentApplication from '../Modules/APM/GoLang/md-docs/Windows/Recommended/golang-windows-recommended-instrumentApplication.md';
import APM_go_windows_recommendedSteps_runApplication from '../Modules/APM/GoLang/md-docs/Windows/Recommended/golang-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// JBoss DOcker
import APM_java_jboss_docker_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/Docker/QuickStart/jboss-docker-quickStart-instrumentApplication.md';
import APM_java_jboss_docker_quickStart_runApplication from '../Modules/APM/Java/md-docs/Jboss/Docker/QuickStart/jboss-docker-quickStart-runApplication.md';
// Jboss-LinuxAMD64-recommended
import APM_java_jboss_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Jboss/Docker/Recommended/jboss-docker-recommended-installOtelCollector.md';
import APM_java_jboss_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/Docker/Recommended/jboss-docker-recommended-instrumentApplication.md';
import APM_java_jboss_docker_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Jboss/Docker/Recommended/jboss-docker-recommended-runApplication.md';
// Jboss-Kubernetes
import APM_java_jboss_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Jboss/Kubernetes/jboss-kubernetes-installOtelCollector.md';
import APM_java_jboss_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/Kubernetes/jboss-kubernetes-instrumentApplication.md';
import APM_java_jboss_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Jboss/Kubernetes/jboss-kubernetes-runApplication.md';
// Jboss-LinuxAMD64-quickstart
import APM_java_jboss_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/LinuxAMD64/QuickStart/jboss-linuxamd64-quickStart-instrumentApplication.md';
import APM_java_jboss_linuxAMD64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Jboss/LinuxAMD64/QuickStart/jboss-linuxamd64-quickStart-runApplication.md';
// Jboss-LinuxAMD64-recommended
import APM_java_jboss_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Jboss/LinuxAMD64/Recommended/jboss-linuxamd64-recommended-installOtelCollector.md';
import APM_java_jboss_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/LinuxAMD64/Recommended/jboss-linuxamd64-recommended-instrumentApplication.md';
import APM_java_jboss_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Jboss/LinuxAMD64/Recommended/jboss-linuxamd64-recommended-runApplication.md';
// Jboss-LinuxARM64-quickstart
import APM_java_jboss_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/LinuxARM64/QuickStart/jboss-linuxarm64-quickStart-instrumentApplication.md';
import APM_java_jboss_linuxARM64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Jboss/LinuxARM64/QuickStart/jboss-linuxarm64-quickStart-runApplication.md';
// Jboss-LinuxARM64-recommended
import APM_java_jboss_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Jboss/LinuxARM64/Recommended/jboss-linuxarm64-recommended-installOtelCollector.md';
import APM_java_jboss_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/LinuxARM64/Recommended/jboss-linuxarm64-recommended-instrumentApplication.md';
import APM_java_jboss_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Jboss/LinuxARM64/Recommended/jboss-linuxarm64-recommended-runApplication.md';
// Jboss-MacOsAMD64-quickstart
import APM_java_jboss_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/MacOsAMD64/QuickStart/jboss-macosamd64-quickStart-instrumentApplication.md';
import APM_java_jboss_macOsAMD64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Jboss/MacOsAMD64/QuickStart/jboss-macosamd64-quickStart-runApplication.md';
// Jboss-MacOsAMD64-recommended
import APM_java_jboss_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Jboss/MacOsAMD64/Recommended/jboss-macosamd64-recommended-installOtelCollector.md';
import APM_java_jboss_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/MacOsAMD64/Recommended/jboss-macosamd64-recommended-instrumentApplication.md';
import APM_java_jboss_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Jboss/MacOsAMD64/Recommended/jboss-macosamd64-recommended-runApplication.md';
// Jboss-MacOsARM64-quickstart
import APM_java_jboss_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/MacOsARM64/QuickStart/jboss-macosarm64-quickStart-instrumentApplication.md';
import APM_java_jboss_macOsARM64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Jboss/MacOsARM64/QuickStart/jboss-macosarm64-quickStart-runApplication.md';
// Jboss-MacOsARM64-recommended
import APM_java_jboss_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Jboss/MacOsARM64/Recommended/jboss-macosarm64-recommended-installOtelCollector.md';
import APM_java_jboss_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/MacOsARM64/Recommended/jboss-macosarm64-recommended-instrumentApplication.md';
import APM_java_jboss_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Jboss/MacOsARM64/Recommended/jboss-macosarm64-recommended-runApplication.md';
// JBoss DOcker
import APM_java_jboss_windows_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/Windows/QuickStart/jboss-windows-quickStart-instrumentApplication.md';
import APM_java_jboss_windows_quickStart_runApplication from '../Modules/APM/Java/md-docs/Jboss/Windows/QuickStart/jboss-windows-quickStart-runApplication.md';
// Jboss-LinuxAMD64-recommended
import APM_java_jboss_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Jboss/Windows/Recommended/jboss-windows-recommended-installOtelCollector.md';
import APM_java_jboss_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Jboss/Windows/Recommended/jboss-windows-recommended-instrumentApplication.md';
import APM_java_jboss_windows_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Jboss/Windows/Recommended/jboss-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// Other Docker
import APM_java_other_docker_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Others/Docker/QuickStart/others-docker-quickStart-instrumentApplication.md';
import APM_java_other_docker_quickStart_runApplication from '../Modules/APM/Java/md-docs/Others/Docker/QuickStart/others-docker-quickStart-runApplication.md';
// Other-LinuxAMD64-recommended
import APM_java_other_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Others/Docker/Recommended/others-docker-recommended-installOtelCollector.md';
import APM_java_other_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Others/Docker/Recommended/others-docker-recommended-instrumentApplication.md';
import APM_java_other_docker_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Others/Docker/Recommended/others-docker-recommended-runApplication.md';
// Other-Kubernetes
import APM_java_other_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Others/Kubernetes/others-kubernetes-installOtelCollector.md';
import APM_java_other_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Others/Kubernetes/others-kubernetes-instrumentApplication.md';
import APM_java_other_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Others/Kubernetes/others-kubernetes-runApplication.md';
// Other-LinuxAMD64-quickstart
import APM_java_other_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Others/LinuxAMD64/QuickStart/others-linuxamd64-quickStart-instrumentApplication.md';
import APM_java_other_linuxAMD64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Others/LinuxAMD64/QuickStart/others-linuxamd64-quickStart-runApplication.md';
// Other-LinuxAMD64-recommended
import APM_java_other_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Others/LinuxAMD64/Recommended/others-linuxamd64-recommended-installOtelCollector.md';
import APM_java_other_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Others/LinuxAMD64/Recommended/others-linuxamd64-recommended-instrumentApplication.md';
import APM_java_other_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Others/LinuxAMD64/Recommended/others-linuxamd64-recommended-runApplication.md';
// Other-LinuxARM64-quickstart
import APM_java_other_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Others/LinuxARM64/QuickStart/others-linuxarm64-quickStart-instrumentApplication.md';
import APM_java_other_linuxARM64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Others/LinuxARM64/QuickStart/others-linuxarm64-quickStart-runApplication.md';
// Other-LinuxARM64-recommended
import APM_java_other_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Others/LinuxARM64/Recommended/others-linuxarm64-recommended-installOtelCollector.md';
import APM_java_other_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Others/LinuxARM64/Recommended/others-linuxarm64-recommended-instrumentApplication.md';
import APM_java_other_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Others/LinuxARM64/Recommended/others-linuxarm64-recommended-runApplication.md';
// Other-MacOsAMD64-quickstart
import APM_java_other_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Others/MacOsAMD64/QuickStart/others-macosamd64-quickStart-instrumentApplication.md';
import APM_java_other_macOsAMD64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Others/MacOsAMD64/QuickStart/others-macosamd64-quickStart-runApplication.md';
// Other-MacOsAMD64-recommended
import APM_java_other_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Others/MacOsAMD64/Recommended/others-macosamd64-recommended-installOtelCollector.md';
import APM_java_other_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Others/MacOsAMD64/Recommended/others-macosamd64-recommended-instrumentApplication.md';
import APM_java_other_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Others/MacOsAMD64/Recommended/others-macosamd64-recommended-runApplication.md';
// Other-MacOsARM64-quickstart
import APM_java_other_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Others/MacOsARM64/QuickStart/others-macosarm64-quickStart-instrumentApplication.md';
import APM_java_other_macOsARM64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Others/MacOsARM64/QuickStart/others-macosarm64-quickStart-runApplication.md';
// Other-MacOsARM64-recommended
import APM_java_other_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Others/MacOsARM64/Recommended/others-macosarm64-recommended-installOtelCollector.md';
import APM_java_other_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Others/MacOsARM64/Recommended/others-macosarm64-recommended-instrumentApplication.md';
import APM_java_other_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Others/MacOsARM64/Recommended/others-macosarm64-recommended-runApplication.md';
import APM_java_other_windows_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Others/Windows/QuickStart/others-windows-quickStart-instrumentApplication.md';
import APM_java_other_windows_quickStart_runApplication from '../Modules/APM/Java/md-docs/Others/Windows/QuickStart/others-windows-quickStart-runApplication.md';
// Other-LinuxAMD64-recommended
import APM_java_other_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Others/Windows/Recommended/others-windows-recommended-installOtelCollector.md';
import APM_java_other_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Others/Windows/Recommended/others-windows-recommended-instrumentApplication.md';
import APM_java_other_windows_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Others/Windows/Recommended/others-windows-recommended-runApplication.md';
// SpringBoot Docker
import APM_java_springBoot_docker_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/Docker/QuickStart/springBoot-docker-quickStart-instrumentApplication.md';
import APM_java_springBoot_docker_quickStart_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/Docker/QuickStart/springBoot-docker-quickStart-runApplication.md';
// SpringBoot-LinuxAMD64-recommended
import APM_java_springBoot_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/SpringBoot/Docker/Recommended/springBoot-docker-recommended-installOtelCollector.md';
import APM_java_springBoot_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/Docker/Recommended/springBoot-docker-recommended-instrumentApplication.md';
import APM_java_springBoot_docker_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/Docker/Recommended/springBoot-docker-recommended-runApplication.md';
// SpringBoot-Kubernetes
import APM_java_springBoot_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/SpringBoot/Kubernetes/springBoot-kubernetes-installOtelCollector.md';
import APM_java_springBoot_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/Kubernetes/springBoot-kubernetes-instrumentApplication.md';
import APM_java_springBoot_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/Kubernetes/springBoot-kubernetes-runApplication.md';
import APM_java_springBoot_kubernetes_recommendedSteps_runApplication_consumers from '../Modules/APM/Java/md-docs/SpringBoot/Kubernetes/springBoot-kubernetes-runApplication-consumers.md';
import APM_java_springBoot_kubernetes_recommendedSteps_runApplication_producers from '../Modules/APM/Java/md-docs/SpringBoot/Kubernetes/springBoot-kubernetes-runApplication-producers.md';
// SpringBoot-LinuxAMD64-quickstart
import APM_java_springBoot_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/LinuxAMD64/QuickStart/springBoot-linuxamd64-quickStart-instrumentApplication.md';
import APM_java_springBoot_linuxAMD64_quickStart_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/LinuxAMD64/QuickStart/springBoot-linuxamd64-quickStart-runApplication.md';
// SpringBoot-LinuxAMD64-recommended
import APM_java_springBoot_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/SpringBoot/LinuxAMD64/Recommended/springBoot-linuxamd64-recommended-installOtelCollector.md';
import APM_java_springBoot_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/LinuxAMD64/Recommended/springBoot-linuxamd64-recommended-instrumentApplication.md';
import APM_java_springBoot_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/LinuxAMD64/Recommended/springBoot-linuxamd64-recommended-runApplication.md';
// SpringBoot-LinuxARM64-quickstart
import APM_java_springBoot_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/LinuxARM64/QuickStart/springBoot-linuxarm64-quickStart-instrumentApplication.md';
import APM_java_springBoot_linuxARM64_quickStart_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/LinuxARM64/QuickStart/springBoot-linuxarm64-quickStart-runApplication.md';
// SpringBoot-LinuxARM64-recommended
import APM_java_springBoot_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/SpringBoot/LinuxARM64/Recommended/springBoot-linuxarm64-recommended-installOtelCollector.md';
import APM_java_springBoot_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/LinuxARM64/Recommended/springBoot-linuxarm64-recommended-instrumentApplication.md';
import APM_java_springBoot_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/LinuxARM64/Recommended/springBoot-linuxarm64-recommended-runApplication.md';
// SpringBoot-MacOsAMD64-quickstart
import APM_java_springBoot_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/MacOsAMD64/QuickStart/springBoot-macosamd64-quickStart-instrumentApplication.md';
import APM_java_springBoot_macOsAMD64_quickStart_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/MacOsAMD64/QuickStart/springBoot-macosamd64-quickStart-runApplication.md';
// SpringBoot-MacOsAMD64-recommended
import APM_java_springBoot_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/SpringBoot/MacOsAMD64/Recommended/springBoot-macosamd64-recommended-installOtelCollector.md';
import APM_java_springBoot_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/MacOsAMD64/Recommended/springBoot-macosamd64-recommended-instrumentApplication.md';
import APM_java_springBoot_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/MacOsAMD64/Recommended/springBoot-macosamd64-recommended-runApplication.md';
// SpringBoot-MacOsARM64-quickstart
import APM_java_springBoot_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/MacOsARM64/QuickStart/springBoot-macosarm64-quickStart-instrumentApplication.md';
import APM_java_springBoot_macOsARM64_quickStart_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/MacOsARM64/QuickStart/springBoot-macosarm64-quickStart-runApplication.md';
// SpringBoot-MacOsARM64-recommended
import APM_java_springBoot_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/SpringBoot/MacOsARM64/Recommended/springBoot-macosarm64-recommended-installOtelCollector.md';
import APM_java_springBoot_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/MacOsARM64/Recommended/springBoot-macosarm64-recommended-instrumentApplication.md';
import APM_java_springBoot_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/MacOsARM64/Recommended/springBoot-macosarm64-recommended-runApplication.md';
// SpringBoot Docker
import APM_java_springBoot_windows_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/Windows/QuickStart/springBoot-windows-quickStart-instrumentApplication.md';
import APM_java_springBoot_windows_quickStart_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/Windows/QuickStart/springBoot-windows-quickStart-runApplication.md';
// SpringBoot-LinuxAMD64-recommended
import APM_java_springBoot_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/SpringBoot/Windows/Recommended/springBoot-windows-recommended-installOtelCollector.md';
import APM_java_springBoot_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/SpringBoot/Windows/Recommended/springBoot-windows-recommended-instrumentApplication.md';
import APM_java_springBoot_windows_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/SpringBoot/Windows/Recommended/springBoot-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// Tomcat Docker
import APM_java_tomcat_docker_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/Docker/QuickStart/tomcat-docker-quickStart-instrumentApplication.md';
import APM_java_tomcat_docker_quickStart_runApplication from '../Modules/APM/Java/md-docs/Tomcat/Docker/QuickStart/tomcat-docker-quickStart-runApplication.md';
// Tomcat-LinuxAMD64-recommended
import APM_java_tomcat_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Tomcat/Docker/Recommended/tomcat-docker-recommended-installOtelCollector.md';
import APM_java_tomcat_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/Docker/Recommended/tomcat-docker-recommended-instrumentApplication.md';
import APM_java_tomcat_docker_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Tomcat/Docker/Recommended/tomcat-docker-recommended-runApplication.md';
// Tomcat-Kubernetes
import APM_java_tomcat_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Tomcat/Kubernetes/tomcat-kubernetes-installOtelCollector.md';
import APM_java_tomcat_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/Kubernetes/tomcat-kubernetes-instrumentApplication.md';
import APM_java_tomcat_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Tomcat/Kubernetes/tomcat-kubernetes-runApplication.md';
// Tomcat-LinuxAMD64-quickstart
import APM_java_tomcat_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/LinuxAMD64/QuickStart/tomcat-linuxamd64-quickStart-instrumentApplication.md';
import APM_java_tomcat_linuxAMD64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Tomcat/LinuxAMD64/QuickStart/tomcat-linuxamd64-quickStart-runApplication.md';
// Tomcat-LinuxAMD64-recommended
import APM_java_tomcat_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Tomcat/LinuxAMD64/Recommended/tomcat-linuxamd64-recommended-installOtelCollector.md';
import APM_java_tomcat_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/LinuxAMD64/Recommended/tomcat-linuxamd64-recommended-instrumentApplication.md';
import APM_java_tomcat_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Tomcat/LinuxAMD64/Recommended/tomcat-linuxamd64-recommended-runApplication.md';
// Tomcat-LinuxARM64-quickstart
import APM_java_tomcat_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/LinuxARM64/QuickStart/tomcat-linuxarm64-quickStart-instrumentApplication.md';
import APM_java_tomcat_linuxARM64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Tomcat/LinuxARM64/QuickStart/tomcat-linuxarm64-quickStart-runApplication.md';
// Tomcat-LinuxARM64-recommended
import APM_java_tomcat_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Tomcat/LinuxARM64/Recommended/tomcat-linuxarm64-recommended-installOtelCollector.md';
import APM_java_tomcat_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/LinuxARM64/Recommended/tomcat-linuxarm64-recommended-instrumentApplication.md';
import APM_java_tomcat_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Tomcat/LinuxARM64/Recommended/tomcat-linuxarm64-recommended-runApplication.md';
// Tomcat-MacOsAMD64-quickstart
import APM_java_tomcat_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/MacOsAMD64/QuickStart/tomcat-macosamd64-quickStart-instrumentApplication.md';
import APM_java_tomcat_macOsAMD64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Tomcat/MacOsAMD64/QuickStart/tomcat-macosamd64-quickStart-runApplication.md';
// Tomcat-MacOsAMD64-recommended
import APM_java_tomcat_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Tomcat/MacOsAMD64/Recommended/tomcat-macosamd64-recommended-installOtelCollector.md';
import APM_java_tomcat_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/MacOsAMD64/Recommended/tomcat-macosamd64-recommended-instrumentApplication.md';
import APM_java_tomcat_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Tomcat/MacOsAMD64/Recommended/tomcat-macosamd64-recommended-runApplication.md';
// Tomcat-MacOsARM64-quickstart
import APM_java_tomcat_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/MacOsARM64/QuickStart/tomcat-macosarm64-quickStart-instrumentApplication.md';
import APM_java_tomcat_macOsARM64_quickStart_runApplication from '../Modules/APM/Java/md-docs/Tomcat/MacOsARM64/QuickStart/tomcat-macosarm64-quickStart-runApplication.md';
// Tomcat-MacOsARM64-recommended
import APM_java_tomcat_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Tomcat/MacOsARM64/Recommended/tomcat-macosarm64-recommended-installOtelCollector.md';
import APM_java_tomcat_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/MacOsARM64/Recommended/tomcat-macosarm64-recommended-instrumentApplication.md';
import APM_java_tomcat_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Tomcat/MacOsARM64/Recommended/tomcat-macosarm64-recommended-runApplication.md';
// Tomcat Docker
import APM_java_tomcat_windows_quickStart_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/Windows/QuickStart/tomcat-windows-quickStart-instrumentApplication.md';
import APM_java_tomcat_windows_quickStart_runApplication from '../Modules/APM/Java/md-docs/Tomcat/Windows/QuickStart/tomcat-windows-quickStart-runApplication.md';
// Tomcat-LinuxAMD64-recommended
import APM_java_tomcat_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Java/md-docs/Tomcat/Windows/Recommended/tomcat-windows-recommended-installOtelCollector.md';
import APM_java_tomcat_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Java/md-docs/Tomcat/Windows/Recommended/tomcat-windows-recommended-instrumentApplication.md';
import APM_java_tomcat_windows_recommendedSteps_runApplication from '../Modules/APM/Java/md-docs/Tomcat/Windows/Recommended/tomcat-windows-recommended-runApplication.md';
// Angular
import APM_javascript_angular_docker_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/Docker/QuickStart/angular-docker-quickStart-instrumentApplication.md';
import APM_javascript_angular_docker_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Angular/Docker/QuickStart/angular-docker-quickStart-runApplication.md';
// Angular-Docker-recommended
import APM_javascript_angular_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Angular/Docker/Recommended/angular-docker-recommended-installOtelCollector.md';
import APM_javascript_angular_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/Docker/Recommended/angular-docker-recommended-instrumentApplication.md';
import APM_javascript_angular_docker_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Angular/Docker/Recommended/angular-docker-recommended-runApplication.md';
// Angular-Kubernetes
import APM_javascript_angular_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Angular/Kubernetes/angular-kubernetes-installOtelCollector.md';
import APM_javascript_angular_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/Kubernetes/angular-kubernetes-instrumentApplication.md';
import APM_javascript_angular_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Angular/Kubernetes/angular-kubernetes-runApplication.md';
// Angular-LinuxAMD64-quickstart
import APM_javascript_angular_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/LinuxAMD64/QuickStart/angular-linuxamd64-quickStart-instrumentApplication.md';
import APM_javascript_angular_linuxAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Angular/LinuxAMD64/QuickStart/angular-linuxamd64-quickStart-runApplication.md';
// Angular-LinuxAMD64-recommended
import APM_javascript_angular_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Angular/LinuxAMD64/Recommended/angular-linuxamd64-recommended-installOtelCollector.md';
import APM_javascript_angular_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/LinuxAMD64/Recommended/angular-linuxamd64-recommended-instrumentApplication.md';
import APM_javascript_angular_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Angular/LinuxAMD64/Recommended/angular-linuxamd64-recommended-runApplication.md';
// Angular-LinuxARM64-quickstart
import APM_javascript_angular_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/LinuxARM64/QuickStart/angular-linuxarm64-quickStart-instrumentApplication.md';
import APM_javascript_angular_linuxARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Angular/LinuxARM64/QuickStart/angular-linuxarm64-quickStart-runApplication.md';
// Angular-LinuxARM64-recommended
import APM_javascript_angular_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Angular/LinuxARM64/Recommended/angular-linuxarm64-recommended-installOtelCollector.md';
import APM_javascript_angular_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/LinuxARM64/Recommended/angular-linuxarm64-recommended-instrumentApplication.md';
import APM_javascript_angular_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Angular/LinuxARM64/Recommended/angular-linuxarm64-recommended-runApplication.md';
// Angular-MacOsAMD64-quickstart
import APM_javascript_angular_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/MacOsAMD64/QuickStart/angular-macosamd64-quickStart-instrumentApplication.md';
import APM_javascript_angular_macOsAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Angular/MacOsAMD64/QuickStart/angular-macosamd64-quickStart-runApplication.md';
// Angular-MacOsAMD64-recommended
import APM_javascript_angular_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Angular/MacOsAMD64/Recommended/angular-macosamd64-recommended-installOtelCollector.md';
import APM_javascript_angular_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/MacOsAMD64/Recommended/angular-macosamd64-recommended-instrumentApplication.md';
import APM_javascript_angular_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Angular/MacOsAMD64/Recommended/angular-macosamd64-recommended-runApplication.md';
// Angular-MacOsARM64-quickstart
import APM_javascript_angular_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/MacOsARM64/QuickStart/angular-macosarm64-quickStart-instrumentApplication.md';
import APM_javascript_angular_macOsARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Angular/MacOsARM64/QuickStart/angular-macosarm64-quickStart-runApplication.md';
// Angular-MacOsARM64-recommended
import APM_javascript_angular_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Angular/MacOsARM64/Recommended/angular-macosarm64-recommended-installOtelCollector.md';
import APM_javascript_angular_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/MacOsARM64/Recommended/angular-macosarm64-recommended-instrumentApplication.md';
import APM_javascript_angular_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Angular/MacOsARM64/Recommended/angular-macosarm64-recommended-runApplication.md';
import APM_javascript_angular_windows_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/Windows/QuickStart/angular-windows-quickStart-instrumentApplication.md';
import APM_javascript_angular_windows_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Angular/Windows/QuickStart/angular-windows-quickStart-runApplication.md';
// Angular-Docker-recommended
import APM_javascript_angular_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Angular/Windows/Recommended/angular-windows-recommended-installOtelCollector.md';
import APM_javascript_angular_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Angular/Windows/Recommended/angular-windows-recommended-instrumentApplication.md';
import APM_javascript_angular_windows_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Angular/Windows/Recommended/angular-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
/// ////// Python Done
/// ///// JavaScript Start
// Express
// Express Docker
import APM_javascript_express_docker_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/Docker/QuickStart/express-docker-quickStart-instrumentApplication.md';
import APM_javascript_express_docker_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Express/Docker/QuickStart/express-docker-quickStart-runApplication.md';
// Express-LinuxAMD64-recommended
import APM_javascript_express_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Express/Docker/Recommended/express-docker-recommended-installOtelCollector.md';
import APM_javascript_express_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/Docker/Recommended/express-docker-recommended-instrumentApplication.md';
import APM_javascript_express_docker_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Express/Docker/Recommended/express-docker-recommended-runApplication.md';
// Express-Kubernetes
import APM_javascript_express_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Express/Kubernetes/express-kubernetes-installOtelCollector.md';
import APM_javascript_express_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/Kubernetes/express-kubernetes-instrumentApplication.md';
import APM_javascript_express_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Express/Kubernetes/express-kubernetes-runApplication.md';
// Express-LinuxAMD64-quickstart
import APM_javascript_express_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/LinuxAMD64/QuickStart/express-linuxamd64-quickStart-instrumentApplication.md';
import APM_javascript_express_linuxAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Express/LinuxAMD64/QuickStart/express-linuxamd64-quickStart-runApplication.md';
// Express-LinuxAMD64-recommended
import APM_javascript_express_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Express/LinuxAMD64/Recommended/express-linuxamd64-recommended-installOtelCollector.md';
import APM_javascript_express_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/LinuxAMD64/Recommended/express-linuxamd64-recommended-instrumentApplication.md';
import APM_javascript_express_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Express/LinuxAMD64/Recommended/express-linuxamd64-recommended-runApplication.md';
// Express-LinuxARM64-quickstart
import APM_javascript_express_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/LinuxARM64/QuickStart/express-linuxarm64-quickStart-instrumentApplication.md';
import APM_javascript_express_linuxARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Express/LinuxARM64/QuickStart/express-linuxarm64-quickStart-runApplication.md';
// Express-LinuxARM64-recommended
import APM_javascript_express_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Express/LinuxARM64/Recommended/express-linuxarm64-recommended-installOtelCollector.md';
import APM_javascript_express_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/LinuxARM64/Recommended/express-linuxarm64-recommended-instrumentApplication.md';
import APM_javascript_express_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Express/LinuxARM64/Recommended/express-linuxarm64-recommended-runApplication.md';
// Express-MacOsAMD64-quickstart
import APM_javascript_express_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/MacOsAMD64/QuickStart/express-macosamd64-quickStart-instrumentApplication.md';
import APM_javascript_express_macOsAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Express/MacOsAMD64/QuickStart/express-macosamd64-quickStart-runApplication.md';
// Express-MacOsAMD64-recommended
import APM_javascript_express_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Express/MacOsAMD64/Recommended/express-macosamd64-recommended-installOtelCollector.md';
import APM_javascript_express_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/MacOsAMD64/Recommended/express-macosamd64-recommended-instrumentApplication.md';
import APM_javascript_express_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Express/MacOsAMD64/Recommended/express-macosamd64-recommended-runApplication.md';
// Express-MacOsARM64-quickstart
import APM_javascript_express_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/MacOsARM64/QuickStart/express-macosarm64-quickStart-instrumentApplication.md';
import APM_javascript_express_macOsARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Express/MacOsARM64/QuickStart/express-macosarm64-quickStart-runApplication.md';
// Express-MacOsARM64-recommended
import APM_javascript_express_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Express/MacOsARM64/Recommended/express-macosarm64-recommended-installOtelCollector.md';
import APM_javascript_express_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/MacOsARM64/Recommended/express-macosarm64-recommended-instrumentApplication.md';
import APM_javascript_express_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Express/MacOsARM64/Recommended/express-macosarm64-recommended-runApplication.md';
import APM_javascript_express_windows_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/Windows/QuickStart/express-windows-quickStart-instrumentApplication.md';
import APM_javascript_express_windows_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Express/Windows/QuickStart/express-windows-quickStart-runApplication.md';
// Express-LinuxAMD64-recommended
import APM_javascript_express_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Express/Windows/Recommended/express-windows-recommended-installOtelCollector.md';
import APM_javascript_express_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Express/Windows/Recommended/express-windows-recommended-instrumentApplication.md';
import APM_javascript_express_windows_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Express/Windows/Recommended/express-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// NestJS
// NestJS Docker
import APM_javascript_nestjs_docker_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/Docker/QuickStart/nestjs-docker-quickStart-instrumentApplication.md';
import APM_javascript_nestjs_docker_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/Docker/QuickStart/nestjs-docker-quickStart-runApplication.md';
// NestJS-LinuxAMD64-recommended
import APM_javascript_nestjs_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NestJS/Docker/Recommended/nestjs-docker-recommended-installOtelCollector.md';
import APM_javascript_nestjs_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/Docker/Recommended/nestjs-docker-recommended-instrumentApplication.md';
import APM_javascript_nestjs_docker_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/Docker/Recommended/nestjs-docker-recommended-runApplication.md';
// NestJS-Kubernetes
import APM_javascript_nestjs_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NestJS/Kubernetes/nestjs-kubernetes-installOtelCollector.md';
import APM_javascript_nestjs_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/Kubernetes/nestjs-kubernetes-instrumentApplication.md';
import APM_javascript_nestjs_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/Kubernetes/nestjs-kubernetes-runApplication.md';
// NestJS-LinuxAMD64-quickstart
import APM_javascript_nestjs_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/LinuxAMD64/QuickStart/nestjs-linuxamd64-quickStart-instrumentApplication.md';
import APM_javascript_nestjs_linuxAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/LinuxAMD64/QuickStart/nestjs-linuxamd64-quickStart-runApplication.md';
// NestJS-LinuxAMD64-recommended
import APM_javascript_nestjs_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NestJS/LinuxAMD64/Recommended/nestjs-linuxamd64-recommended-installOtelCollector.md';
import APM_javascript_nestjs_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/LinuxAMD64/Recommended/nestjs-linuxamd64-recommended-instrumentApplication.md';
import APM_javascript_nestjs_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/LinuxAMD64/Recommended/nestjs-linuxamd64-recommended-runApplication.md';
// NestJS-LinuxARM64-quickstart
import APM_javascript_nestjs_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/LinuxARM64/QuickStart/nestjs-linuxarm64-quickStart-instrumentApplication.md';
import APM_javascript_nestjs_linuxARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/LinuxARM64/QuickStart/nestjs-linuxarm64-quickStart-runApplication.md';
// NestJS-LinuxARM64-recommended
import APM_javascript_nestjs_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NestJS/LinuxARM64/Recommended/nestjs-linuxarm64-recommended-installOtelCollector.md';
import APM_javascript_nestjs_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/LinuxARM64/Recommended/nestjs-linuxarm64-recommended-instrumentApplication.md';
import APM_javascript_nestjs_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/LinuxARM64/Recommended/nestjs-linuxarm64-recommended-runApplication.md';
// NestJS-MacOsAMD64-quickstart
import APM_javascript_nestjs_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/MacOsAMD64/QuickStart/nestjs-macosamd64-quickStart-instrumentApplication.md';
import APM_javascript_nestjs_macOsAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/MacOsAMD64/QuickStart/nestjs-macosamd64-quickStart-runApplication.md';
// NestJS-MacOsAMD64-recommended
import APM_javascript_nestjs_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NestJS/MacOsAMD64/Recommended/nestjs-macosamd64-recommended-installOtelCollector.md';
import APM_javascript_nestjs_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/MacOsAMD64/Recommended/nestjs-macosamd64-recommended-instrumentApplication.md';
import APM_javascript_nestjs_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/MacOsAMD64/Recommended/nestjs-macosamd64-recommended-runApplication.md';
// NestJS-MacOsARM64-quickstart
import APM_javascript_nestjs_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/MacOsARM64/QuickStart/nestjs-macosarm64-quickStart-instrumentApplication.md';
import APM_javascript_nestjs_macOsARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/MacOsARM64/QuickStart/nestjs-macosarm64-quickStart-runApplication.md';
// NestJS-MacOsARM64-recommended
import APM_javascript_nestjs_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NestJS/MacOsARM64/Recommended/nestjs-macosarm64-recommended-installOtelCollector.md';
import APM_javascript_nestjs_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/MacOsARM64/Recommended/nestjs-macosarm64-recommended-instrumentApplication.md';
import APM_javascript_nestjs_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/MacOsARM64/Recommended/nestjs-macosarm64-recommended-runApplication.md';
// NestJS
// NestJS Docker
import APM_javascript_nestjs_windows_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/Windows/QuickStart/nestjs-windows-quickStart-instrumentApplication.md';
import APM_javascript_nestjs_windows_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/Windows/QuickStart/nestjs-windows-quickStart-runApplication.md';
// NestJS-LinuxAMD64-recommended
import APM_javascript_nestjs_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NestJS/Windows/Recommended/nestjs-windows-recommended-installOtelCollector.md';
import APM_javascript_nestjs_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NestJS/Windows/Recommended/nestjs-windows-recommended-instrumentApplication.md';
import APM_javascript_nestjs_windows_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NestJS/Windows/Recommended/nestjs-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// NodeJS
// NodeJS Docker
import APM_javascript_nodejs_docker_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Docker/QuickStart/nodejs-docker-quickStart-instrumentApplication.md';
import APM_javascript_nodejs_docker_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Docker/QuickStart/nodejs-docker-quickStart-runApplication.md';
// NodeJS-LinuxAMD64-recommended
import APM_javascript_nodejs_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NodeJS/Docker/Recommended/nodejs-docker-recommended-installOtelCollector.md';
import APM_javascript_nodejs_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Docker/Recommended/nodejs-docker-recommended-instrumentApplication.md';
import APM_javascript_nodejs_docker_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Docker/Recommended/nodejs-docker-recommended-runApplication.md';
// NodeJS-Kubernetes
import APM_javascript_nodejs_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NodeJS/Kubernetes/nodejs-kubernetes-installOtelCollector.md';
import APM_javascript_nodejs_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Kubernetes/nodejs-kubernetes-instrumentApplication.md';
import APM_javascript_nodejs_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Kubernetes/nodejs-kubernetes-runApplication.md';
// NodeJS-LinuxAMD64-quickstart
import APM_javascript_nodejs_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxAMD64/QuickStart/nodejs-linuxamd64-quickStart-instrumentApplication.md';
import APM_javascript_nodejs_linuxAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxAMD64/QuickStart/nodejs-linuxamd64-quickStart-runApplication.md';
// NodeJS-LinuxAMD64-recommended
import APM_javascript_nodejs_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxAMD64/Recommended/nodejs-linuxamd64-recommended-installOtelCollector.md';
import APM_javascript_nodejs_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxAMD64/Recommended/nodejs-linuxamd64-recommended-instrumentApplication.md';
import APM_javascript_nodejs_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxAMD64/Recommended/nodejs-linuxamd64-recommended-runApplication.md';
// NodeJS-LinuxARM64-quickstart
import APM_javascript_nodejs_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxARM64/QuickStart/nodejs-linuxarm64-quickStart-instrumentApplication.md';
import APM_javascript_nodejs_linuxARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxARM64/QuickStart/nodejs-linuxarm64-quickStart-runApplication.md';
// NodeJS-LinuxARM64-recommended
import APM_javascript_nodejs_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxARM64/Recommended/nodejs-linuxarm64-recommended-installOtelCollector.md';
import APM_javascript_nodejs_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxARM64/Recommended/nodejs-linuxarm64-recommended-instrumentApplication.md';
import APM_javascript_nodejs_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/LinuxARM64/Recommended/nodejs-linuxarm64-recommended-runApplication.md';
// NodeJS-MacOsAMD64-quickstart
import APM_javascript_nodejs_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsAMD64/QuickStart/nodejs-macosamd64-quickStart-instrumentApplication.md';
import APM_javascript_nodejs_macOsAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsAMD64/QuickStart/nodejs-macosamd64-quickStart-runApplication.md';
// NodeJS-MacOsAMD64-recommended
import APM_javascript_nodejs_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsAMD64/Recommended/nodejs-macosamd64-recommended-installOtelCollector.md';
import APM_javascript_nodejs_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsAMD64/Recommended/nodejs-macosamd64-recommended-instrumentApplication.md';
import APM_javascript_nodejs_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsAMD64/Recommended/nodejs-macosamd64-recommended-runApplication.md';
// NodeJS-MacOsARM64-quickstart
import APM_javascript_nodejs_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsARM64/QuickStart/nodejs-macosarm64-quickStart-instrumentApplication.md';
import APM_javascript_nodejs_macOsARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsARM64/QuickStart/nodejs-macosarm64-quickStart-runApplication.md';
// NodeJS-MacOsARM64-recommended
import APM_javascript_nodejs_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsARM64/Recommended/nodejs-macosarm64-recommended-installOtelCollector.md';
import APM_javascript_nodejs_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsARM64/Recommended/nodejs-macosarm64-recommended-instrumentApplication.md';
import APM_javascript_nodejs_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/MacOsARM64/Recommended/nodejs-macosarm64-recommended-runApplication.md';
// NodeJS Docker
import APM_javascript_nodejs_windows_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Windows/QuickStart/nodejs-windows-quickStart-instrumentApplication.md';
import APM_javascript_nodejs_windows_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Windows/QuickStart/nodejs-windows-quickStart-runApplication.md';
// NodeJS-LinuxAMD64-recommended
import APM_javascript_nodejs_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/NodeJS/Windows/Recommended/nodejs-windows-recommended-installOtelCollector.md';
import APM_javascript_nodejs_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Windows/Recommended/nodejs-windows-recommended-instrumentApplication.md';
import APM_javascript_nodejs_windows_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/NodeJS/Windows/Recommended/nodejs-windows-recommended-runApplication.md';
/// // JavaScript Others
// Others Docker
import APM_javascript_others_docker_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/Docker/QuickStart/others-docker-quickStart-instrumentApplication.md';
import APM_javascript_others_docker_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Others/Docker/QuickStart/others-docker-quickStart-runApplication.md';
// // Others-JavaScript-LinuxAMD64-recommended
import APM_javascript_others_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Others/Docker/Recommended/others-docker-recommended-installOtelCollector.md';
import APM_javascript_others_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/Docker/Recommended/others-docker-recommended-instrumentApplication.md';
import APM_javascript_others_docker_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Others/Docker/Recommended/others-docker-recommended-runApplication.md';
// Kubernetes
import APM_javascript_others_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Others/Kubernetes/others-kubernetes-installOtelCollector.md';
import APM_javascript_others_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/Kubernetes/others-kubernetes-instrumentApplication.md';
import APM_javascript_others_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Others/Kubernetes/others-kubernetes-runApplication.md';
// Others-JavaScript-LinuxAMD64-quickstart
import APM_javascript_others_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/LinuxAMD64/QuickStart/others-linuxamd64-quickStart-instrumentApplication.md';
import APM_javascript_others_linuxAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Others/LinuxAMD64/QuickStart/others-linuxamd64-quickStart-runApplication.md';
// // Others-JavaScript-LinuxAMD64-recommended
import APM_javascript_others_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Others/LinuxAMD64/Recommended/others-linuxamd64-recommended-installOtelCollector.md';
import APM_javascript_others_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/LinuxAMD64/Recommended/others-linuxamd64-recommended-instrumentApplication.md';
import APM_javascript_others_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Others/LinuxAMD64/Recommended/others-linuxamd64-recommended-runApplication.md';
// Others-JavaScript-LinuxARM64-quiOthers
import APM_javascript_others_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/LinuxARM64/QuickStart/others-linuxarm64-quickStart-instrumentApplication.md';
import APM_javascript_others_linuxARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Others/LinuxARM64/QuickStart/others-linuxarm64-quickStart-runApplication.md';
// Others-JavaScript-LinuxARM64-recommended
import APM_javascript_others_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Others/LinuxARM64/Recommended/others-linuxarm64-recommended-installOtelCollector.md';
import APM_javascript_others_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/LinuxARM64/Recommended/others-linuxarm64-recommended-instrumentApplication.md';
import APM_javascript_others_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Others/LinuxARM64/Recommended/others-linuxarm64-recommended-runApplication.md';
// Others-JavaScript-MacOsAMD64-quickstart
import APM_javascript_others_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/MacOsAMD64/QuickStart/others-macosamd64-quickStart-instrumentApplication.md';
import APM_javascript_others_macOsAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Others/MacOsAMD64/QuickStart/others-macosamd64-quickStart-runApplication.md';
// Others-JavaScript-MacOsAMD64-recommended
import APM_javascript_others_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Others/MacOsAMD64/Recommended/others-macosamd64-recommended-installOtelCollector.md';
import APM_javascript_others_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/MacOsAMD64/Recommended/others-macosamd64-recommended-instrumentApplication.md';
import APM_javascript_others_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Others/MacOsAMD64/Recommended/others-macosamd64-recommended-runApplication.md';
// Others-JavaScript-MacOsARM64-quickstart
import APM_javascript_others_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/MacOsARM64/QuickStart/others-macosarm64-quickStart-instrumentApplication.md';
import APM_javascript_others_macOsARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Others/MacOsARM64/QuickStart/others-macosarm64-quickStart-runApplication.md';
// Others-JavaScript-MacOsARM64-recommended
import APM_javascript_others_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Others/MacOsARM64/Recommended/others-macosarm64-recommended-installOtelCollector.md';
import APM_javascript_others_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/MacOsARM64/Recommended/others-macosarm64-recommended-instrumentApplication.md';
import APM_javascript_others_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Others/MacOsARM64/Recommended/others-macosarm64-recommended-runApplication.md';
// Others Docker
import APM_javascript_others_windows_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/Windows/QuickStart/others-windows-quickStart-instrumentApplication.md';
import APM_javascript_others_windows_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/Others/Windows/QuickStart/others-windows-quickStart-runApplication.md';
// // Others-JavaScript-LinuxAMD64-recommended
import APM_javascript_others_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/Others/Windows/Recommended/others-windows-recommended-installOtelCollector.md';
import APM_javascript_others_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/Others/Windows/Recommended/others-windows-recommended-instrumentApplication.md';
import APM_javascript_others_windows_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/Others/Windows/Recommended/others-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// ReactJS Docker
import APM_javascript_reactjs_docker_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Docker/QuickStart/reactjs-docker-quickStart-instrumentApplication.md';
import APM_javascript_reactjs_docker_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Docker/QuickStart/reactjs-docker-quickStart-runApplication.md';
// // ReactJS-LinuxAMD64-recommended
import APM_javascript_reactjs_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/ReactJS/Docker/Recommended/reactjs-docker-recommended-installOtelCollector.md';
import APM_javascript_reactjs_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Docker/Recommended/reactjs-docker-recommended-instrumentApplication.md';
import APM_javascript_reactjs_docker_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Docker/Recommended/reactjs-docker-recommended-runApplication.md';
// ReactJS-Kubernetes
import APM_javascript_reactjs_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/ReactJS/Kubernetes/reactjs-kubernetes-installOtelCollector.md';
import APM_javascript_reactjs_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Kubernetes/reactjs-kubernetes-instrumentApplication.md';
import APM_javascript_reactjs_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Kubernetes/reactjs-kubernetes-runApplication.md';
// ReactJS-LinuxAMD64-quickstart
import APM_javascript_reactjs_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxAMD64/QuickStart/reactjs-linuxamd64-quickStart-instrumentApplication.md';
import APM_javascript_reactjs_linuxAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxAMD64/QuickStart/reactjs-linuxamd64-quickStart-runApplication.md';
// // ReactJS-LinuxAMD64-recommended
import APM_javascript_reactjs_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxAMD64/Recommended/reactjs-linuxamd64-recommended-installOtelCollector.md';
import APM_javascript_reactjs_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxAMD64/Recommended/reactjs-linuxamd64-recommended-instrumentApplication.md';
import APM_javascript_reactjs_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxAMD64/Recommended/reactjs-linuxamd64-recommended-runApplication.md';
// ReactJS-LinuxARM64-quickstart
import APM_javascript_reactjs_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxARM64/QuickStart/reactjs-linuxarm64-quickStart-instrumentApplication.md';
import APM_javascript_reactjs_linuxARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxARM64/QuickStart/reactjs-linuxarm64-quickStart-runApplication.md';
// ReactJS-LinuxARM64-recommended
import APM_javascript_reactjs_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxARM64/Recommended/reactjs-linuxarm64-recommended-installOtelCollector.md';
import APM_javascript_reactjs_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxARM64/Recommended/reactjs-linuxarm64-recommended-instrumentApplication.md';
import APM_javascript_reactjs_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/LinuxARM64/Recommended/reactjs-linuxarm64-recommended-runApplication.md';
// ReactJS-MacOsAMD64-quickstart
import APM_javascript_reactjs_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsAMD64/QuickStart/reactjs-macosamd64-quickStart-instrumentApplication.md';
import APM_javascript_reactjs_macOsAMD64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsAMD64/QuickStart/reactjs-macosamd64-quickStart-runApplication.md';
// ReactJS-MacOsAMD64-recommended
import APM_javascript_reactjs_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsAMD64/Recommended/reactjs-macosamd64-recommended-installOtelCollector.md';
import APM_javascript_reactjs_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsAMD64/Recommended/reactjs-macosamd64-recommended-instrumentApplication.md';
import APM_javascript_reactjs_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsAMD64/Recommended/reactjs-macosamd64-recommended-runApplication.md';
// ReactJS-MacOsARM64-quickstart
import APM_javascript_reactjs_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsARM64/QuickStart/reactjs-macosarm64-quickStart-instrumentApplication.md';
import APM_javascript_reactjs_macOsARM64_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsARM64/QuickStart/reactjs-macosarm64-quickStart-runApplication.md';
// ReactJS-MacOsARM64-recommended
import APM_javascript_reactjs_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsARM64/Recommended/reactjs-macosarm64-recommended-installOtelCollector.md';
import APM_javascript_reactjs_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsARM64/Recommended/reactjs-macosarm64-recommended-instrumentApplication.md';
import APM_javascript_reactjs_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/MacOsARM64/Recommended/reactjs-macosarm64-recommended-runApplication.md';
// ReactJS Docker
import APM_javascript_reactjs_windows_quickStart_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Windows/QuickStart/reactjs-windows-quickStart-instrumentApplication.md';
import APM_javascript_reactjs_windows_quickStart_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Windows/QuickStart/reactjs-windows-quickStart-runApplication.md';
// // ReactJS-LinuxAMD64-recommended
import APM_javascript_reactjs_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Javascript/md-docs/ReactJS/Windows/Recommended/reactjs-windows-recommended-installOtelCollector.md';
import APM_javascript_reactjs_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Windows/Recommended/reactjs-windows-recommended-instrumentApplication.md';
import APM_javascript_reactjs_windows_recommendedSteps_runApplication from '../Modules/APM/Javascript/md-docs/ReactJS/Windows/Recommended/reactjs-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// PHP Docker
import APM_php_docker_quickStart_instrumentApplication from '../Modules/APM/Php/md-docs/Docker/QuickStart/php-docker-quickStart-instrumentApplication.md';
import APM_php_docker_quickStart_runApplication from '../Modules/APM/Php/md-docs/Docker/QuickStart/php-docker-quickStart-runApplication.md';
// PHP-LinuxAMD64-recommended
import APM_php_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Php/md-docs/Docker/Recommended/php-docker-recommended-installOtelCollector.md';
import APM_php_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Php/md-docs/Docker/Recommended/php-docker-recommended-instrumentApplication.md';
import APM_php_docker_recommendedSteps_runApplication from '../Modules/APM/Php/md-docs/Docker/Recommended/php-docker-recommended-runApplication.md';
// PHP-Kubernetes
import APM_php_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Php/md-docs/Kubernetes/php-kubernetes-installOtelCollector.md';
import APM_php_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Php/md-docs/Kubernetes/php-kubernetes-instrumentApplication.md';
import APM_php_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Php/md-docs/Kubernetes/php-kubernetes-runApplication.md';
// PHP-LinuxAMD64-quickstart
import APM_php_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Php/md-docs/LinuxAMD64/QuickStart/php-linuxamd64-quickStart-instrumentApplication.md';
import APM_php_linuxAMD64_quickStart_runApplication from '../Modules/APM/Php/md-docs/LinuxAMD64/QuickStart/php-linuxamd64-quickStart-runApplication.md';
// PHP-LinuxAMD64-recommended
import APM_php_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Php/md-docs/LinuxAMD64/Recommended/php-linuxamd64-recommended-installOtelCollector.md';
import APM_php_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Php/md-docs/LinuxAMD64/Recommended/php-linuxamd64-recommended-instrumentApplication.md';
import APM_php_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Php/md-docs/LinuxAMD64/Recommended/php-linuxamd64-recommended-runApplication.md';
// PHP-LinuxARM64-quickstart
import APM_php_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Php/md-docs/LinuxARM64/QuickStart/php-linuxarm64-quickStart-instrumentApplication.md';
import APM_php_linuxARM64_quickStart_runApplication from '../Modules/APM/Php/md-docs/LinuxARM64/QuickStart/php-linuxarm64-quickStart-runApplication.md';
// PHP-LinuxARM64-recommended
import APM_php_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Php/md-docs/LinuxARM64/Recommended/php-linuxarm64-recommended-installOtelCollector.md';
import APM_php_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Php/md-docs/LinuxARM64/Recommended/php-linuxarm64-recommended-instrumentApplication.md';
import APM_php_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Php/md-docs/LinuxARM64/Recommended/php-linuxarm64-recommended-runApplication.md';
// PHP-MacOsAMD64-quickstart
import APM_php_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Php/md-docs/MacOsAMD64/QuickStart/php-macosamd64-quickStart-instrumentApplication.md';
import APM_php_macOsAMD64_quickStart_runApplication from '../Modules/APM/Php/md-docs/MacOsAMD64/QuickStart/php-macosamd64-quickStart-runApplication.md';
// PHP-MacOsAMD64-recommended
import APM_php_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Php/md-docs/MacOsAMD64/Recommended/php-macosamd64-recommended-installOtelCollector.md';
import APM_php_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Php/md-docs/MacOsAMD64/Recommended/php-macosamd64-recommended-instrumentApplication.md';
import APM_php_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Php/md-docs/MacOsAMD64/Recommended/php-macosamd64-recommended-runApplication.md';
// PHP-MacOsARM64-quickstart
import APM_php_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Php/md-docs/MacOsARM64/QuickStart/php-macosarm64-quickStart-instrumentApplication.md';
import APM_php_macOsARM64_quickStart_runApplication from '../Modules/APM/Php/md-docs/MacOsARM64/QuickStart/php-macosarm64-quickStart-runApplication.md';
// PHP-MacOsARM64-recommended
import APM_php_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Php/md-docs/MacOsARM64/Recommended/php-macosarm64-recommended-installOtelCollector.md';
import APM_php_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Php/md-docs/MacOsARM64/Recommended/php-macosarm64-recommended-instrumentApplication.md';
import APM_php_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Php/md-docs/MacOsARM64/Recommended/php-macosarm64-recommended-runApplication.md';
// PHP Docker
import APM_php_windows_quickStart_instrumentApplication from '../Modules/APM/Php/md-docs/Windows/QuickStart/php-windows-quickStart-instrumentApplication.md';
import APM_php_windows_quickStart_runApplication from '../Modules/APM/Php/md-docs/Windows/QuickStart/php-windows-quickStart-runApplication.md';
// PHP-LinuxAMD64-recommended
import APM_php_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Php/md-docs/Windows/Recommended/php-windows-recommended-installOtelCollector.md';
import APM_php_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Php/md-docs/Windows/Recommended/php-windows-recommended-instrumentApplication.md';
import APM_php_windows_recommendedSteps_runApplication from '../Modules/APM/Php/md-docs/Windows/Recommended/php-windows-recommended-runApplication.md';
/// ////// Docker instructions
import APM_python_django_docker_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Django/Docker/QuickStart/django-docker-quickStart-instrumentApplication.md';
import APM_python_django_docker_quickStart_runApplication from '../Modules/APM/Python/md-docs/Django/Docker/QuickStart/django-docker-quickStart-runApplication.md';
// Django-LinuxAMD64-recommended
import APM_python_django_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Django/Docker/Recommended/django-docker-recommended-installOtelCollector.md';
import APM_python_django_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Django/Docker/Recommended/django-docker-recommended-instrumentApplication.md';
import APM_python_django_docker_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Django/Docker/Recommended/django-docker-recommended-runApplication.md';
/// ////// Javascript Done
/// ///// Python Start
// Django
// Django-Kubernetes
import APM_python_django_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Django/Kubernetes/django-kubernetes-installOtelCollector.md';
import APM_python_django_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Django/Kubernetes/django-kubernetes-instrumentApplication.md';
import APM_python_django_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Django/Kubernetes/django-kubernetes-runApplication.md';
// Django-LinuxAMD64-quickstart
import APM_python_django_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Django/LinuxAMD64/QuickStart/django-linuxamd64-quickStart-instrumentApplication.md';
import APM_python_django_linuxAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Django/LinuxAMD64/QuickStart/django-linuxamd64-quickStart-runApplication.md';
// Django-LinuxAMD64-recommended
import APM_python_django_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Django/LinuxAMD64/Recommended/django-linuxamd64-recommended-installOtelCollector.md';
import APM_python_django_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Django/LinuxAMD64/Recommended/django-linuxamd64-recommended-instrumentApplication.md';
import APM_python_django_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Django/LinuxAMD64/Recommended/django-linuxamd64-recommended-runApplication.md';
// Django-LinuxARM64-quickstart
import APM_python_django_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Django/LinuxARM64/QuickStart/django-linuxarm64-quickStart-instrumentApplication.md';
import APM_python_django_linuxARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Django/LinuxARM64/QuickStart/django-linuxarm64-quickStart-runApplication.md';
// Django-LinuxARM64-recommended
import APM_python_django_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Django/LinuxARM64/Recommended/django-linuxarm64-recommended-installOtelCollector.md';
import APM_python_django_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Django/LinuxARM64/Recommended/django-linuxarm64-recommended-instrumentApplication.md';
import APM_python_django_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Django/LinuxARM64/Recommended/django-linuxarm64-recommended-runApplication.md';
// Django-MacOsAMD64-quickstart
import APM_python_django_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Django/MacOsAMD64/QuickStart/django-macosamd64-quickStart-instrumentApplication.md';
import APM_python_django_macOsAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Django/MacOsAMD64/QuickStart/django-macosamd64-quickStart-runApplication.md';
// Django-MacOsAMD64-recommended
import APM_python_django_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Django/MacOsAMD64/Recommended/django-macosamd64-recommended-installOtelCollector.md';
import APM_python_django_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Django/MacOsAMD64/Recommended/django-macosamd64-recommended-instrumentApplication.md';
import APM_python_django_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Django/MacOsAMD64/Recommended/django-macosamd64-recommended-runApplication.md';
// Django-MacOsARM64-quickstart
import APM_python_django_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Django/MacOsARM64/QuickStart/django-macosarm64-quickStart-instrumentApplication.md';
import APM_python_django_macOsARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Django/MacOsARM64/QuickStart/django-macosarm64-quickStart-runApplication.md';
// Django-MacOsARM64-recommended
import APM_python_django_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Django/MacOsARM64/Recommended/django-macosarm64-recommended-installOtelCollector.md';
import APM_python_django_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Django/MacOsARM64/Recommended/django-macosarm64-recommended-instrumentApplication.md';
import APM_python_django_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Django/MacOsARM64/Recommended/django-macosarm64-recommended-runApplication.md';
import APM_python_django_windows_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Django/Windows/QuickStart/django-windows-quickStart-instrumentApplication.md';
import APM_python_django_windows_quickStart_runApplication from '../Modules/APM/Python/md-docs/Django/Windows/QuickStart/django-windows-quickStart-runApplication.md';
// Django-LinuxAMD64-recommended
import APM_python_django_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Django/Windows/Recommended/django-windows-recommended-installOtelCollector.md';
import APM_python_django_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Django/Windows/Recommended/django-windows-recommended-instrumentApplication.md';
import APM_python_django_windows_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Django/Windows/Recommended/django-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// Falcon
// Falcon Docker
import APM_python_falcon_docker_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/Docker/QuickStart/falcon-docker-quickStart-instrumentApplication.md';
import APM_python_falcon_docker_quickStart_runApplication from '../Modules/APM/Python/md-docs/Falcon/Docker/QuickStart/falcon-docker-quickStart-runApplication.md';
// Falcon-LinuxAMD64-recommended
import APM_python_falcon_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Falcon/Docker/Recommended/falcon-docker-recommended-installOtelCollector.md';
import APM_python_falcon_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/Docker/Recommended/falcon-docker-recommended-instrumentApplication.md';
import APM_python_falcon_docker_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Falcon/Docker/Recommended/falcon-docker-recommended-runApplication.md';
// Falcon-Kubernetes
import APM_python_falcon_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Falcon/Kubernetes/falcon-kubernetes-installOtelCollector.md';
import APM_python_falcon_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/Kubernetes/falcon-kubernetes-instrumentApplication.md';
import APM_python_falcon_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Falcon/Kubernetes/falcon-kubernetes-runApplication.md';
// Falcon-LinuxAMD64-quickstart
import APM_python_falcon_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/LinuxAMD64/QuickStart/falcon-linuxamd64-quickStart-instrumentApplication.md';
import APM_python_falcon_linuxAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Falcon/LinuxAMD64/QuickStart/falcon-linuxamd64-quickStart-runApplication.md';
// Falcon-LinuxAMD64-recommended
import APM_python_falcon_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Falcon/LinuxAMD64/Recommended/falcon-linuxamd64-recommended-installOtelCollector.md';
import APM_python_falcon_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/LinuxAMD64/Recommended/falcon-linuxamd64-recommended-instrumentApplication.md';
import APM_python_falcon_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Falcon/LinuxAMD64/Recommended/falcon-linuxamd64-recommended-runApplication.md';
// Falcon-LinuxARM64-quickstart
import APM_python_falcon_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/LinuxARM64/QuickStart/falcon-linuxarm64-quickStart-instrumentApplication.md';
import APM_python_falcon_linuxARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Falcon/LinuxARM64/QuickStart/falcon-linuxarm64-quickStart-runApplication.md';
// Falcon-LinuxARM64-recommended
import APM_python_falcon_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Falcon/LinuxARM64/Recommended/falcon-linuxarm64-recommended-installOtelCollector.md';
import APM_python_falcon_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/LinuxARM64/Recommended/falcon-linuxarm64-recommended-instrumentApplication.md';
import APM_python_falcon_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Falcon/LinuxARM64/Recommended/falcon-linuxarm64-recommended-runApplication.md';
// Falcon-MacOsAMD64-quickstart
import APM_python_falcon_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/MacOsAMD64/QuickStart/falcon-macosamd64-quickStart-instrumentApplication.md';
import APM_python_falcon_macOsAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Falcon/MacOsAMD64/QuickStart/falcon-macosamd64-quickStart-runApplication.md';
// Falcon-MacOsAMD64-recommended
import APM_python_falcon_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Falcon/MacOsAMD64/Recommended/falcon-macosamd64-recommended-installOtelCollector.md';
import APM_python_falcon_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/MacOsAMD64/Recommended/falcon-macosamd64-recommended-instrumentApplication.md';
import APM_python_falcon_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Falcon/MacOsAMD64/Recommended/falcon-macosamd64-recommended-runApplication.md';
// Falcon-MacOsARM64-quickstart
import APM_python_falcon_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/MacOsARM64/QuickStart/falcon-macosarm64-quickStart-instrumentApplication.md';
import APM_python_falcon_macOsARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Falcon/MacOsARM64/QuickStart/falcon-macosarm64-quickStart-runApplication.md';
// Falcon-MacOsARM64-recommended
import APM_python_falcon_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Falcon/MacOsARM64/Recommended/falcon-macosarm64-recommended-installOtelCollector.md';
import APM_python_falcon_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/MacOsARM64/Recommended/falcon-macosarm64-recommended-instrumentApplication.md';
import APM_python_falcon_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Falcon/MacOsARM64/Recommended/falcon-macosarm64-recommended-runApplication.md';
import APM_python_falcon_windows_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/Windows/QuickStart/falcon-windows-quickStart-instrumentApplication.md';
import APM_python_falcon_windows_quickStart_runApplication from '../Modules/APM/Python/md-docs/Falcon/Windows/QuickStart/falcon-windows-quickStart-runApplication.md';
// Falcon-LinuxAMD64-recommended
import APM_python_falcon_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Falcon/Windows/Recommended/falcon-windows-recommended-installOtelCollector.md';
import APM_python_falcon_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Falcon/Windows/Recommended/falcon-windows-recommended-instrumentApplication.md';
import APM_python_falcon_windows_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Falcon/Windows/Recommended/falcon-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// FastAPI
// FastAPI Docker
import APM_python_fastAPI_docker_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/Docker/QuickStart/fastapi-docker-quickStart-instrumentApplication.md';
import APM_python_fastAPI_docker_quickStart_runApplication from '../Modules/APM/Python/md-docs/FastAPI/Docker/QuickStart/fastapi-docker-quickStart-runApplication.md';
// FastAPI-LinuxAMD64-recommended
import APM_python_fastAPI_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/FastAPI/Docker/Recommended/fastapi-docker-recommended-installOtelCollector.md';
import APM_python_fastAPI_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/Docker/Recommended/fastapi-docker-recommended-instrumentApplication.md';
import APM_python_fastAPI_docker_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/FastAPI/Docker/Recommended/fastapi-docker-recommended-runApplication.md';
// FastAPI-Kubernetes
import APM_python_fastAPI_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/FastAPI/Kubernetes/fastapi-kubernetes-installOtelCollector.md';
import APM_python_fastAPI_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/Kubernetes/fastapi-kubernetes-instrumentApplication.md';
import APM_python_fastAPI_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/FastAPI/Kubernetes/fastapi-kubernetes-runApplication.md';
// FastAPI-LinuxAMD64-quickstart
import APM_python_fastAPI_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/LinuxAMD64/QuickStart/fastapi-linuxamd64-quickStart-instrumentApplication.md';
import APM_python_fastAPI_linuxAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/FastAPI/LinuxAMD64/QuickStart/fastapi-linuxamd64-quickStart-runApplication.md';
// FastAPI-LinuxAMD64-recommended
import APM_python_fastAPI_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/FastAPI/LinuxAMD64/Recommended/fastapi-linuxamd64-recommended-installOtelCollector.md';
import APM_python_fastAPI_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/LinuxAMD64/Recommended/fastapi-linuxamd64-recommended-instrumentApplication.md';
import APM_python_fastAPI_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/FastAPI/LinuxAMD64/Recommended/fastapi-linuxamd64-recommended-runApplication.md';
// FastAPI-LinuxARM64-quickstart
import APM_python_fastAPI_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/LinuxARM64/QuickStart/fastapi-linuxarm64-quickStart-instrumentApplication.md';
import APM_python_fastAPI_linuxARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/FastAPI/LinuxARM64/QuickStart/fastapi-linuxarm64-quickStart-runApplication.md';
// FastAPI-LinuxARM64-recommfastapi
import APM_python_fastAPI_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/FastAPI/LinuxARM64/Recommended/fastapi-linuxarm64-recommended-installOtelCollector.md';
import APM_python_fastAPI_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/LinuxARM64/Recommended/fastapi-linuxarm64-recommended-instrumentApplication.md';
import APM_python_fastAPI_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/FastAPI/LinuxARM64/Recommended/fastapi-linuxarm64-recommended-runApplication.md';
// FastAPI-MacOsAMD64-quickstart
import APM_python_fastAPI_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/MacOsAMD64/QuickStart/fastapi-macosamd64-quickStart-instrumentApplication.md';
import APM_python_fastAPI_macOsAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/FastAPI/MacOsAMD64/QuickStart/fastapi-macosamd64-quickStart-runApplication.md';
// FastAPI-MacOsAMD64-recommended
import APM_python_fastAPI_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/FastAPI/MacOsAMD64/Recommended/fastapi-macosamd64-recommended-installOtelCollector.md';
import APM_python_fastAPI_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/MacOsAMD64/Recommended/fastapi-macosamd64-recommended-instrumentApplication.md';
import APM_python_fastAPI_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/FastAPI/MacOsAMD64/Recommended/fastapi-macosamd64-recommended-runApplication.md';
// FastAPI-MacOsARM64-quickstart
import APM_python_fastAPI_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/MacOsARM64/QuickStart/fastapi-macosarm64-quickStart-instrumentApplication.md';
import APM_python_fastAPI_macOsARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/FastAPI/MacOsARM64/QuickStart/fastapi-macosarm64-quickStart-runApplication.md';
// FastAPI-MacOsARM64-recommended
import APM_python_fastAPI_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/FastAPI/MacOsARM64/Recommended/fastapi-macosarm64-recommended-installOtelCollector.md';
import APM_python_fastAPI_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/MacOsARM64/Recommended/fastapi-macosarm64-recommended-instrumentApplication.md';
import APM_python_fastAPI_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/FastAPI/MacOsARM64/Recommended/fastapi-macosarm64-recommended-runApplication.md';
// FastAPI Docker
import APM_python_fastAPI_windows_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/Windows/QuickStart/fastapi-windows-quickStart-instrumentApplication.md';
import APM_python_fastAPI_windows_quickStart_runApplication from '../Modules/APM/Python/md-docs/FastAPI/Windows/QuickStart/fastapi-windows-quickStart-runApplication.md';
// FastAPI-LinuxAMD64-recommended
import APM_python_fastAPI_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/FastAPI/Windows/Recommended/fastapi-windows-recommended-installOtelCollector.md';
import APM_python_fastAPI_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/FastAPI/Windows/Recommended/fastapi-windows-recommended-instrumentApplication.md';
import APM_python_fastAPI_windows_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/FastAPI/Windows/Recommended/fastapi-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// Flask
// Flask Docker
import APM_python_flask_docker_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/Docker/QuickStart/flask-docker-quickStart-instrumentApplication.md';
import APM_python_flask_docker_quickStart_runApplication from '../Modules/APM/Python/md-docs/Flask/Docker/QuickStart/flask-docker-quickStart-runApplication.md';
import APM_python_flask_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Flask/Docker/Recommended/flask-docker-recommended-installOtelCollector.md';
import APM_python_flask_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/Docker/Recommended/flask-docker-recommended-instrumentApplication.md';
import APM_python_flask_docker_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Flask/Docker/Recommended/flask-docker-recommended-runApplication.md';
// Flask-Kubernetes
import APM_python_flask_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Flask/Kubernetes/flask-kubernetes-installOtelCollector.md';
import APM_python_flask_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/Kubernetes/flask-kubernetes-instrumentApplication.md';
import APM_python_flask_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Flask/Kubernetes/flask-kubernetes-runApplication.md';
// Flask-LinuxAMD64-quickstart
import APM_python_flask_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/LinuxAMD64/QuickStart/flask-linuxamd64-quickStart-instrumentApplication.md';
import APM_python_flask_linuxAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Flask/LinuxAMD64/QuickStart/flask-linuxamd64-quickStart-runApplication.md';
// Flask-LinuxAMD64-recommended
import APM_python_flask_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Flask/LinuxAMD64/Recommended/flask-linuxamd64-recommended-installOtelCollector.md';
import APM_python_flask_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/LinuxAMD64/Recommended/flask-linuxamd64-recommended-instrumentApplication.md';
import APM_python_flask_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Flask/LinuxAMD64/Recommended/flask-linuxamd64-recommended-runApplication.md';
// Flask-LinuxARM64-quickstart
import APM_python_flask_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/LinuxARM64/QuickStart/flask-linuxarm64-quickStart-instrumentApplication.md';
import APM_python_flask_linuxARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Flask/LinuxARM64/QuickStart/flask-linuxarm64-quickStart-runApplication.md';
// Flask-LinuxARM64-recommended
import APM_python_flask_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Flask/LinuxARM64/Recommended/flask-linuxarm64-recommended-installOtelCollector.md';
import APM_python_flask_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/LinuxARM64/Recommended/flask-linuxarm64-recommended-instrumentApplication.md';
import APM_python_flask_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Flask/LinuxARM64/Recommended/flask-linuxarm64-recommended-runApplication.md';
// Flask-MacOsAMD64-quickstart
import APM_python_flask_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/MacOsAMD64/QuickStart/flask-macosamd64-quickStart-instrumentApplication.md';
import APM_python_flask_macOsAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Flask/MacOsAMD64/QuickStart/flask-macosamd64-quickStart-runApplication.md';
// Flask-MacOsAMD64-recommended
import APM_python_flask_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Flask/MacOsAMD64/Recommended/flask-macosamd64-recommended-installOtelCollector.md';
import APM_python_flask_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/MacOsAMD64/Recommended/flask-macosamd64-recommended-instrumentApplication.md';
import APM_python_flask_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Flask/MacOsAMD64/Recommended/flask-macosamd64-recommended-runApplication.md';
// Flask-MacOsARM64-quickstart
import APM_python_flask_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/MacOsARM64/QuickStart/flask-macosarm64-quickStart-instrumentApplication.md';
import APM_python_flask_macOsARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Flask/MacOsARM64/QuickStart/flask-macosarm64-quickStart-runApplication.md';
// Flask-MacOsARM64-recommended
import APM_python_flask_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Flask/MacOsARM64/Recommended/flask-macosarm64-recommended-installOtelCollector.md';
import APM_python_flask_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/MacOsARM64/Recommended/flask-macosarm64-recommended-instrumentApplication.md';
import APM_python_flask_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Flask/MacOsARM64/Recommended/flask-macosarm64-recommended-runApplication.md';
// Flask Docker
import APM_python_flask_windows_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/Windows/QuickStart/flask-windows-quickStart-instrumentApplication.md';
import APM_python_flask_windows_quickStart_runApplication from '../Modules/APM/Python/md-docs/Flask/Windows/QuickStart/flask-windows-quickStart-runApplication.md';
import APM_python_flask_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Flask/Windows/Recommended/flask-windows-recommended-installOtelCollector.md';
import APM_python_flask_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Flask/Windows/Recommended/flask-windows-recommended-instrumentApplication.md';
import APM_python_flask_windows_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Flask/Windows/Recommended/flask-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
// Others
// Others Docker
import APM_python_other_docker_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Others/Docker/QuickStart/others-docker-quickStart-instrumentApplication.md';
import APM_python_other_docker_quickStart_runApplication from '../Modules/APM/Python/md-docs/Others/Docker/QuickStart/others-docker-quickStart-runApplication.md';
// Others-LinuxAMD64-recommended
import APM_python_other_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Others/Docker/Recommended/others-docker-recommended-installOtelCollector.md';
import APM_python_other_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Others/Docker/Recommended/others-docker-recommended-instrumentApplication.md';
import APM_python_other_docker_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Others/Docker/Recommended/others-docker-recommended-runApplication.md';
// Others-Kubernetes
import APM_python_other_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Others/Kubernetes/others-kubernetes-installOtelCollector.md';
import APM_python_other_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Others/Kubernetes/others-kubernetes-instrumentApplication.md';
import APM_python_other_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Others/Kubernetes/others-kubernetes-runApplication.md';
// Others-LinuxAMD64-quickstart
import APM_python_other_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Others/LinuxAMD64/QuickStart/others-linuxamd64-quickStart-instrumentApplication.md';
import APM_python_other_linuxAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Others/LinuxAMD64/QuickStart/others-linuxamd64-quickStart-runApplication.md';
// Others-LinuxAMD64-recommended
import APM_python_other_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Others/LinuxAMD64/Recommended/others-linuxamd64-recommended-installOtelCollector.md';
import APM_python_other_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Others/LinuxAMD64/Recommended/others-linuxamd64-recommended-instrumentApplication.md';
import APM_python_other_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Others/LinuxAMD64/Recommended/others-linuxamd64-recommended-runApplication.md';
// Others-LinuxARM64-quickstart
import APM_python_other_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Others/LinuxARM64/QuickStart/others-linuxarm64-quickStart-instrumentApplication.md';
import APM_python_other_linuxARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Others/LinuxARM64/QuickStart/others-linuxarm64-quickStart-runApplication.md';
// Others-LinuxARM64-recommended
import APM_python_other_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Others/LinuxARM64/Recommended/others-linuxarm64-recommended-installOtelCollector.md';
import APM_python_other_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Others/LinuxARM64/Recommended/others-linuxarm64-recommended-instrumentApplication.md';
import APM_python_other_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Others/LinuxARM64/Recommended/others-linuxarm64-recommended-runApplication.md';
// Others-MacOsAMD64-quickstart
import APM_python_other_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Others/MacOsAMD64/QuickStart/others-macosamd64-quickStart-instrumentApplication.md';
import APM_python_other_macOsAMD64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Others/MacOsAMD64/QuickStart/others-macosamd64-quickStart-runApplication.md';
// Others-MacOsAMD64-recommended
import APM_python_other_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Others/MacOsAMD64/Recommended/others-macosamd64-recommended-installOtelCollector.md';
import APM_python_other_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Others/MacOsAMD64/Recommended/others-macosamd64-recommended-instrumentApplication.md';
import APM_python_other_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Others/MacOsAMD64/Recommended/others-macosamd64-recommended-runApplication.md';
// Others-MacOsARM64-quickstart
import APM_python_other_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Others/MacOsARM64/QuickStart/others-macosarm64-quickStart-instrumentApplication.md';
import APM_python_other_macOsARM64_quickStart_runApplication from '../Modules/APM/Python/md-docs/Others/MacOsARM64/QuickStart/others-macosarm64-quickStart-runApplication.md';
// Others-MacOsARM64-recommended
import APM_python_other_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Others/MacOsARM64/Recommended/others-macosarm64-recommended-installOtelCollector.md';
import APM_python_other_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Others/MacOsARM64/Recommended/others-macosarm64-recommended-instrumentApplication.md';
import APM_python_other_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Others/MacOsARM64/Recommended/others-macosarm64-recommended-runApplication.md';
// Others Docker
import APM_python_other_windows_quickStart_instrumentApplication from '../Modules/APM/Python/md-docs/Others/Windows/QuickStart/others-windows-quickStart-instrumentApplication.md';
import APM_python_other_windows_quickStart_runApplication from '../Modules/APM/Python/md-docs/Others/Windows/QuickStart/others-windows-quickStart-runApplication.md';
// Others-LinuxAMD64-recommended
import APM_python_other_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Python/md-docs/Others/Windows/Recommended/others-windows-recommended-installOtelCollector.md';
import APM_python_other_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Python/md-docs/Others/Windows/Recommended/others-windows-recommended-instrumentApplication.md';
import APM_python_other_windows_recommendedSteps_runApplication from '../Modules/APM/Python/md-docs/Others/Windows/Recommended/others-windows-recommended-runApplication.md';
// ----------------------------------------------------------------------------
/// ///// ROR Start
// ROR Docker
import APM_rails_docker_quickStart_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/Docker/QuickStart/ror-docker-quickStart-instrumentApplication.md';
import APM_rails_docker_quickStart_runApplication from '../Modules/APM/RubyOnRails/md-docs/Docker/QuickStart/ror-docker-quickStart-runApplication.md';
// ROR-LinuxAMD64-recommended
import APM_rails_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/RubyOnRails/md-docs/Docker/Recommended/ror-docker-recommended-installOtelCollector.md';
import APM_rails_docker_recommendedSteps_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/Docker/Recommended/ror-docker-recommended-instrumentApplication.md';
import APM_rails_docker_recommendedSteps_runApplication from '../Modules/APM/RubyOnRails/md-docs/Docker/Recommended/ror-docker-recommended-runApplication.md';
// ROR-Kubernetes
import APM_rails_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/RubyOnRails/md-docs/Kubernetes/ror-kubernetes-installOtelCollector.md';
import APM_rails_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/Kubernetes/ror-kubernetes-instrumentApplication.md';
import APM_rails_kubernetes_recommendedSteps_runApplication from '../Modules/APM/RubyOnRails/md-docs/Kubernetes/ror-kubernetes-runApplication.md';
// ROR-LinuxAMD64-quickstart
import APM_rails_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/LinuxAMD64/QuickStart/ror-linuxamd64-quickStart-instrumentApplication.md';
import APM_rails_linuxAMD64_quickStart_runApplication from '../Modules/APM/RubyOnRails/md-docs/LinuxAMD64/QuickStart/ror-linuxamd64-quickStart-runApplication.md';
// ROR-LinuxAMD64-recommended
import APM_rails_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/RubyOnRails/md-docs/LinuxAMD64/Recommended/ror-linuxamd64-recommended-installOtelCollector.md';
import APM_rails_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/LinuxAMD64/Recommended/ror-linuxamd64-recommended-instrumentApplication.md';
import APM_rails_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/RubyOnRails/md-docs/LinuxAMD64/Recommended/ror-linuxamd64-recommended-runApplication.md';
// ROR-LinuxARM64-quickstart
import APM_rails_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/LinuxARM64/QuickStart/ror-linuxarm64-quickStart-instrumentApplication.md';
import APM_rails_linuxARM64_quickStart_runApplication from '../Modules/APM/RubyOnRails/md-docs/LinuxARM64/QuickStart/ror-linuxarm64-quickStart-runApplication.md';
// ROR-LinuxARM64-recommended
import APM_rails_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/RubyOnRails/md-docs/LinuxARM64/Recommended/ror-linuxarm64-recommended-installOtelCollector.md';
import APM_rails_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/LinuxARM64/Recommended/ror-linuxarm64-recommended-instrumentApplication.md';
import APM_rails_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/RubyOnRails/md-docs/LinuxARM64/Recommended/ror-linuxarm64-recommended-runApplication.md';
// ROR-MacOsAMD64-quickstart
import APM_rails_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/MacOsAMD64/QuickStart/ror-macosamd64-quickStart-instrumentApplication.md';
import APM_rails_macOsAMD64_quickStart_runApplication from '../Modules/APM/RubyOnRails/md-docs/MacOsAMD64/QuickStart/ror-macosamd64-quickStart-runApplication.md';
// ROR-MacOsAMD64-recommended
import APM_rails_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/RubyOnRails/md-docs/MacOsAMD64/Recommended/ror-macosamd64-recommended-installOtelCollector.md';
import APM_rails_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/MacOsAMD64/Recommended/ror-macosamd64-recommended-instrumentApplication.md';
import APM_rails_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/RubyOnRails/md-docs/MacOsAMD64/Recommended/ror-macosamd64-recommended-runApplication.md';
// ROR-MacOsARM64-quickstart
import APM_rails_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/MacOsARM64/QuickStart/ror-macosarm64-quickStart-instrumentApplication.md';
import APM_rails_macOsARM64_quickStart_runApplication from '../Modules/APM/RubyOnRails/md-docs/MacOsARM64/QuickStart/ror-macosarm64-quickStart-runApplication.md';
// ROR-MacOsARM64-recommended
import APM_rails_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/RubyOnRails/md-docs/MacOsARM64/Recommended/ror-macosarm64-recommended-installOtelCollector.md';
import APM_rails_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/MacOsARM64/Recommended/ror-macosarm64-recommended-instrumentApplication.md';
import APM_rails_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/RubyOnRails/md-docs/MacOsARM64/Recommended/ror-macosarm64-recommended-runApplication.md';
// ROR Docker
import APM_rails_windows_quickStart_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/Windows/QuickStart/ror-windows-quickStart-instrumentApplication.md';
import APM_rails_windows_quickStart_runApplication from '../Modules/APM/RubyOnRails/md-docs/Windows/QuickStart/ror-windows-quickStart-runApplication.md';
// ROR-LinuxAMD64-recommended
import APM_rails_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/RubyOnRails/md-docs/Windows/Recommended/ror-windows-recommended-installOtelCollector.md';
import APM_rails_windows_recommendedSteps_instrumentApplication from '../Modules/APM/RubyOnRails/md-docs/Windows/Recommended/ror-windows-recommended-instrumentApplication.md';
import APM_rails_windows_recommendedSteps_runApplication from '../Modules/APM/RubyOnRails/md-docs/Windows/Recommended/ror-windows-recommended-runApplication.md';
// Rust DOcker
import APM_rust_docker_quickStart_instrumentApplication from '../Modules/APM/Rust/md-docs/Docker/QuickStart/rust-docker-quickStart-instrumentApplication.md';
import APM_rust_docker_quickStart_runApplication from '../Modules/APM/Rust/md-docs/Docker/QuickStart/rust-docker-quickStart-runApplication.md';
// Rust-LinuxAMD64-recommended
import APM_rust_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Rust/md-docs/Docker/Recommended/rust-docker-recommended-installOtelCollector.md';
import APM_rust_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Rust/md-docs/Docker/Recommended/rust-docker-recommended-instrumentApplication.md';
import APM_rust_docker_recommendedSteps_runApplication from '../Modules/APM/Rust/md-docs/Docker/Recommended/rust-docker-recommended-runApplication.md';
// Rust-Kubernetes
import APM_rust_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Rust/md-docs/Kubernetes/rust-kubernetes-installOtelCollector.md';
import APM_rust_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Rust/md-docs/Kubernetes/rust-kubernetes-instrumentApplication.md';
import APM_rust_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Rust/md-docs/Kubernetes/rust-kubernetes-runApplication.md';
// Rust-LinuxAMD64-quickstart
import APM_rust_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Rust/md-docs/LinuxAMD64/QuickStart/rust-linuxamd64-quickStart-instrumentApplication.md';
import APM_rust_linuxAMD64_quickStart_runApplication from '../Modules/APM/Rust/md-docs/LinuxAMD64/QuickStart/rust-linuxamd64-quickStart-runApplication.md';
// Rust-LinuxAMD64-recommended
import APM_rust_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Rust/md-docs/LinuxAMD64/Recommended/rust-linuxamd64-recommended-installOtelCollector.md';
import APM_rust_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Rust/md-docs/LinuxAMD64/Recommended/rust-linuxamd64-recommended-instrumentApplication.md';
import APM_rust_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Rust/md-docs/LinuxAMD64/Recommended/rust-linuxamd64-recommended-runApplication.md';
// Rust-LinuxARM64-quickstart
import APM_rust_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Rust/md-docs/LinuxARM64/QuickStart/rust-linuxarm64-quickStart-instrumentApplication.md';
import APM_rust_linuxARM64_quickStart_runApplication from '../Modules/APM/Rust/md-docs/LinuxARM64/QuickStart/rust-linuxarm64-quickStart-runApplication.md';
// Rust-LinuxARM64-recommended
import APM_rust_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Rust/md-docs/LinuxARM64/Recommended/rust-linuxarm64-recommended-installOtelCollector.md';
import APM_rust_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Rust/md-docs/LinuxARM64/Recommended/rust-linuxarm64-recommended-instrumentApplication.md';
import APM_rust_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Rust/md-docs/LinuxARM64/Recommended/rust-linuxarm64-recommended-runApplication.md';
// Rust-MacOsAMD64-quickstart
import APM_rust_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Rust/md-docs/MacOsAMD64/QuickStart/rust-macosamd64-quickStart-instrumentApplication.md';
import APM_rust_macOsAMD64_quickStart_runApplication from '../Modules/APM/Rust/md-docs/MacOsAMD64/QuickStart/rust-macosamd64-quickStart-runApplication.md';
// Rust-MacOsAMD64-recommended
import APM_rust_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Rust/md-docs/MacOsAMD64/Recommended/rust-macosamd64-recommended-installOtelCollector.md';
import APM_rust_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Rust/md-docs/MacOsAMD64/Recommended/rust-macosamd64-recommended-instrumentApplication.md';
import APM_rust_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Rust/md-docs/MacOsAMD64/Recommended/rust-macosamd64-recommended-runApplication.md';
// Rust-MacOsARM64-quickstart
import APM_rust_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Rust/md-docs/MacOsARM64/QuickStart/rust-macosarm64-quickStart-instrumentApplication.md';
import APM_rust_macOsARM64_quickStart_runApplication from '../Modules/APM/Rust/md-docs/MacOsARM64/QuickStart/rust-macosarm64-quickStart-runApplication.md';
// Rust-MacOsARM64-recommended
import APM_rust_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Rust/md-docs/MacOsARM64/Recommended/rust-macosarm64-recommended-installOtelCollector.md';
import APM_rust_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Rust/md-docs/MacOsARM64/Recommended/rust-macosarm64-recommended-instrumentApplication.md';
import APM_rust_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Rust/md-docs/MacOsARM64/Recommended/rust-macosarm64-recommended-runApplication.md';
// Rust DOcker
import APM_rust_windows_quickStart_instrumentApplication from '../Modules/APM/Rust/md-docs/Windows/QuickStart/rust-windows-quickStart-instrumentApplication.md';
import APM_rust_windows_quickStart_runApplication from '../Modules/APM/Rust/md-docs/Windows/QuickStart/rust-windows-quickStart-runApplication.md';
// Rust-LinuxAMD64-recommended
import APM_rust_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Rust/md-docs/Windows/Recommended/rust-windows-recommended-installOtelCollector.md';
import APM_rust_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Rust/md-docs/Windows/Recommended/rust-windows-recommended-instrumentApplication.md';
import APM_rust_windows_recommendedSteps_runApplication from '../Modules/APM/Rust/md-docs/Windows/Recommended/rust-windows-recommended-runApplication.md';
// Swift Docker
import APM_swift_docker_quickStart_instrumentApplication from '../Modules/APM/Swift/md-docs/Docker/QuickStart/swift-docker-quickStart-instrumentApplication.md';
import APM_swift_docker_quickStart_runApplication from '../Modules/APM/Swift/md-docs/Docker/QuickStart/swift-docker-quickStart-runApplication.md';
// Swift-LinuxAMD64-recommended
import APM_swift_docker_recommendedSteps_setupOtelCollector from '../Modules/APM/Swift/md-docs/Docker/Recommended/swift-docker-recommended-installOtelCollector.md';
import APM_swift_docker_recommendedSteps_instrumentApplication from '../Modules/APM/Swift/md-docs/Docker/Recommended/swift-docker-recommended-instrumentApplication.md';
import APM_swift_docker_recommendedSteps_runApplication from '../Modules/APM/Swift/md-docs/Docker/Recommended/swift-docker-recommended-runApplication.md';
// Swift-Kubernetes
import APM_swift_kubernetes_recommendedSteps_setupOtelCollector from '../Modules/APM/Swift/md-docs/Kubernetes/swift-kubernetes-installOtelCollector.md';
import APM_swift_kubernetes_recommendedSteps_instrumentApplication from '../Modules/APM/Swift/md-docs/Kubernetes/swift-kubernetes-instrumentApplication.md';
import APM_swift_kubernetes_recommendedSteps_runApplication from '../Modules/APM/Swift/md-docs/Kubernetes/swift-kubernetes-runApplication.md';
// Swift-LinuxAMD64-quickstart
import APM_swift_linuxAMD64_quickStart_instrumentApplication from '../Modules/APM/Swift/md-docs/LinuxAMD64/QuickStart/swift-linuxamd64-quickStart-instrumentApplication.md';
import APM_swift_linuxAMD64_quickStart_runApplication from '../Modules/APM/Swift/md-docs/LinuxAMD64/QuickStart/swift-linuxamd64-quickStart-runApplication.md';
// Swift-LinuxAMD64-recommended
import APM_swift_linuxAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Swift/md-docs/LinuxAMD64/Recommended/swift-linuxamd64-recommended-installOtelCollector.md';
import APM_swift_linuxAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Swift/md-docs/LinuxAMD64/Recommended/swift-linuxamd64-recommended-instrumentApplication.md';
import APM_swift_linuxAMD64_recommendedSteps_runApplication from '../Modules/APM/Swift/md-docs/LinuxAMD64/Recommended/swift-linuxamd64-recommended-runApplication.md';
// Swift-LinuxARM64-quickstart
import APM_swift_linuxARM64_quickStart_instrumentApplication from '../Modules/APM/Swift/md-docs/LinuxARM64/QuickStart/swift-linuxarm64-quickStart-instrumentApplication.md';
import APM_swift_linuxARM64_quickStart_runApplication from '../Modules/APM/Swift/md-docs/LinuxARM64/QuickStart/swift-linuxarm64-quickStart-runApplication.md';
// Swift-LinuxARM64-recommended
import APM_swift_linuxARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Swift/md-docs/LinuxARM64/Recommended/swift-linuxarm64-recommended-installOtelCollector.md';
import APM_swift_linuxARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Swift/md-docs/LinuxARM64/Recommended/swift-linuxarm64-recommended-instrumentApplication.md';
import APM_swift_linuxARM64_recommendedSteps_runApplication from '../Modules/APM/Swift/md-docs/LinuxARM64/Recommended/swift-linuxarm64-recommended-runApplication.md';
// Swift-MacOsAMD64-quickstart
import APM_swift_macOsAMD64_quickStart_instrumentApplication from '../Modules/APM/Swift/md-docs/MacOsAMD64/QuickStart/swift-macosamd64-quickStart-instrumentApplication.md';
import APM_swift_macOsAMD64_quickStart_runApplication from '../Modules/APM/Swift/md-docs/MacOsAMD64/QuickStart/swift-macosamd64-quickStart-runApplication.md';
// Swift-MacOsAMD64-recommended
import APM_swift_macOsAMD64_recommendedSteps_setupOtelCollector from '../Modules/APM/Swift/md-docs/MacOsAMD64/Recommended/swift-macosamd64-recommended-installOtelCollector.md';
import APM_swift_macOsAMD64_recommendedSteps_instrumentApplication from '../Modules/APM/Swift/md-docs/MacOsAMD64/Recommended/swift-macosamd64-recommended-instrumentApplication.md';
import APM_swift_macOsAMD64_recommendedSteps_runApplication from '../Modules/APM/Swift/md-docs/MacOsAMD64/Recommended/swift-macosamd64-recommended-runApplication.md';
// Swift-MacOsARM64-quickstart
import APM_swift_macOsARM64_quickStart_instrumentApplication from '../Modules/APM/Swift/md-docs/MacOsARM64/QuickStart/swift-macosarm64-quickStart-instrumentApplication.md';
import APM_swift_macOsARM64_quickStart_runApplication from '../Modules/APM/Swift/md-docs/MacOsARM64/QuickStart/swift-macosarm64-quickStart-runApplication.md';
// Swift-MacOsARM64-recommended
import APM_swift_macOsARM64_recommendedSteps_setupOtelCollector from '../Modules/APM/Swift/md-docs/MacOsARM64/Recommended/swift-macosarm64-recommended-installOtelCollector.md';
import APM_swift_macOsARM64_recommendedSteps_instrumentApplication from '../Modules/APM/Swift/md-docs/MacOsARM64/Recommended/swift-macosarm64-recommended-instrumentApplication.md';
import APM_swift_macOsARM64_recommendedSteps_runApplication from '../Modules/APM/Swift/md-docs/MacOsARM64/Recommended/swift-macosarm64-recommended-runApplication.md';
// Swift Docker
import APM_swift_windows_quickStart_instrumentApplication from '../Modules/APM/Swift/md-docs/Windows/QuickStart/swift-windows-quickStart-instrumentApplication.md';
import APM_swift_windows_quickStart_runApplication from '../Modules/APM/Swift/md-docs/Windows/QuickStart/swift-windows-quickStart-runApplication.md';
// Swift-LinuxAMD64-recommended
import APM_swift_windows_recommendedSteps_setupOtelCollector from '../Modules/APM/Swift/md-docs/Windows/Recommended/swift-windows-recommended-installOtelCollector.md';
import APM_swift_windows_recommendedSteps_instrumentApplication from '../Modules/APM/Swift/md-docs/Windows/Recommended/swift-windows-recommended-instrumentApplication.md';
import APM_swift_windows_recommendedSteps_runApplication from '../Modules/APM/Swift/md-docs/Windows/Recommended/swift-windows-recommended-runApplication.md';

export const ApmDocFilePaths = {
	// Aust

	/// //// Java Start

	// SpringBoot-Kubernetes
	APM_java_springBoot_kubernetes_recommendedSteps_setupOtelCollector,
	APM_java_springBoot_kubernetes_recommendedSteps_instrumentApplication,
	APM_java_springBoot_kubernetes_recommendedSteps_runApplication,
	APM_java_springBoot_kubernetes_recommendedSteps_runApplication_producers,
	APM_java_springBoot_kubernetes_recommendedSteps_runApplication_consumers,

	// SpringBoot-LinuxAMD64-recommended
	APM_java_springBoot_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_java_springBoot_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_java_springBoot_linuxAMD64_recommendedSteps_runApplication,

	// SpringBoot-LinuxAMD64-quickstart
	APM_java_springBoot_linuxAMD64_quickStart_instrumentApplication,
	APM_java_springBoot_linuxAMD64_quickStart_runApplication,

	// SpringBoot-LinuxARM64-recommended
	APM_java_springBoot_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_java_springBoot_linuxARM64_recommendedSteps_instrumentApplication,
	APM_java_springBoot_linuxARM64_recommendedSteps_runApplication,

	// SpringBoot-LinuxARM64-quickstart
	APM_java_springBoot_linuxARM64_quickStart_instrumentApplication,
	APM_java_springBoot_linuxARM64_quickStart_runApplication,

	// SpringBoot-MacOsAMD64-recommended
	APM_java_springBoot_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_java_springBoot_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_java_springBoot_macOsAMD64_recommendedSteps_runApplication,

	// SpringBoot-MacOsAMD64-quickstart
	APM_java_springBoot_macOsAMD64_quickStart_instrumentApplication,
	APM_java_springBoot_macOsAMD64_quickStart_runApplication,

	// SpringBoot-MacOsARM64-recommended
	APM_java_springBoot_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_java_springBoot_macOsARM64_recommendedSteps_instrumentApplication,
	APM_java_springBoot_macOsARM64_recommendedSteps_runApplication,

	// SpringBoot-MacOsARM64-quickstart
	APM_java_springBoot_macOsARM64_quickStart_instrumentApplication,
	APM_java_springBoot_macOsARM64_quickStart_runApplication,

	//	------------------------------------------------------------------------------------------------

	// Tomcat

	// Tomcat-Kubernetes
	APM_java_tomcat_kubernetes_recommendedSteps_setupOtelCollector,
	APM_java_tomcat_kubernetes_recommendedSteps_instrumentApplication,
	APM_java_tomcat_kubernetes_recommendedSteps_runApplication,

	// Tomcat-LinuxAMD64-recommended
	APM_java_tomcat_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_java_tomcat_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_java_tomcat_linuxAMD64_recommendedSteps_runApplication,

	// Tomcat-LinuxAMD64-quickstart
	APM_java_tomcat_linuxAMD64_quickStart_instrumentApplication,
	APM_java_tomcat_linuxAMD64_quickStart_runApplication,

	// Tomcat-LinuxARM64-recommended
	APM_java_tomcat_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_java_tomcat_linuxARM64_recommendedSteps_instrumentApplication,
	APM_java_tomcat_linuxARM64_recommendedSteps_runApplication,

	// Tomcat-LinuxARM64-quickstart
	APM_java_tomcat_linuxARM64_quickStart_instrumentApplication,
	APM_java_tomcat_linuxARM64_quickStart_runApplication,

	// Tomcat-MacOsAMD64-recommended
	APM_java_tomcat_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_java_tomcat_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_java_tomcat_macOsAMD64_recommendedSteps_runApplication,

	// Tomcat-MacOsAMD64-quickstart
	APM_java_tomcat_macOsAMD64_quickStart_instrumentApplication,
	APM_java_tomcat_macOsAMD64_quickStart_runApplication,

	// Tomcat-MacOsARM64-recommended
	APM_java_tomcat_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_java_tomcat_macOsARM64_recommendedSteps_instrumentApplication,
	APM_java_tomcat_macOsARM64_recommendedSteps_runApplication,

	// Tomcat-MacOsARM64-quickstart
	APM_java_tomcat_macOsARM64_quickStart_instrumentApplication,
	APM_java_tomcat_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------
	// Jboss

	// Jboss-Kubernetes
	APM_java_jboss_kubernetes_recommendedSteps_setupOtelCollector,
	APM_java_jboss_kubernetes_recommendedSteps_instrumentApplication,
	APM_java_jboss_kubernetes_recommendedSteps_runApplication,

	// Jboss-LinuxAMD64-recommended
	APM_java_jboss_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_java_jboss_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_java_jboss_linuxAMD64_recommendedSteps_runApplication,

	// Jboss-LinuxAMD64-quickstart
	APM_java_jboss_linuxAMD64_quickStart_instrumentApplication,
	APM_java_jboss_linuxAMD64_quickStart_runApplication,

	// Jboss-LinuxARM64-recommended
	APM_java_jboss_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_java_jboss_linuxARM64_recommendedSteps_instrumentApplication,
	APM_java_jboss_linuxARM64_recommendedSteps_runApplication,

	// Jboss-LinuxARM64-quickstart
	APM_java_jboss_linuxARM64_quickStart_instrumentApplication,
	APM_java_jboss_linuxARM64_quickStart_runApplication,

	// Jboss-MacOsAMD64-recommended
	APM_java_jboss_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_java_jboss_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_java_jboss_macOsAMD64_recommendedSteps_runApplication,

	// Jboss-MacOsAMD64-quickstart
	APM_java_jboss_macOsAMD64_quickStart_instrumentApplication,
	APM_java_jboss_macOsAMD64_quickStart_runApplication,

	// Jboss-MacOsARM64-recommended
	APM_java_jboss_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_java_jboss_macOsARM64_recommendedSteps_instrumentApplication,
	APM_java_jboss_macOsARM64_recommendedSteps_runApplication,

	// Jboss-MacOsARM64-quickstart
	APM_java_jboss_macOsARM64_quickStart_instrumentApplication,
	APM_java_jboss_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	// Others

	// Other-Kubernetes
	APM_java_other_kubernetes_recommendedSteps_setupOtelCollector,
	APM_java_other_kubernetes_recommendedSteps_instrumentApplication,
	APM_java_other_kubernetes_recommendedSteps_runApplication,

	// Other-LinuxAMD64-recommended
	APM_java_other_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_java_other_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_java_other_linuxAMD64_recommendedSteps_runApplication,

	// Other-LinuxAMD64-quickstart
	APM_java_other_linuxAMD64_quickStart_instrumentApplication,
	APM_java_other_linuxAMD64_quickStart_runApplication,

	// Other-LinuxARM64-recommended
	APM_java_other_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_java_other_linuxARM64_recommendedSteps_instrumentApplication,
	APM_java_other_linuxARM64_recommendedSteps_runApplication,

	// Other-LinuxARM64-quickstart
	APM_java_other_linuxARM64_quickStart_instrumentApplication,
	APM_java_other_linuxARM64_quickStart_runApplication,

	// Other-MacOsAMD64-recommended
	APM_java_other_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_java_other_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_java_other_macOsAMD64_recommendedSteps_runApplication,

	// Other-MacOsAMD64-quickstart
	APM_java_other_macOsAMD64_quickStart_instrumentApplication,
	APM_java_other_macOsAMD64_quickStart_runApplication,

	// Other-MacOsARM64-recommended
	APM_java_other_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_java_other_macOsARM64_recommendedSteps_instrumentApplication,
	APM_java_other_macOsARM64_recommendedSteps_runApplication,

	// Other-MacOsARM64-quickstart
	APM_java_other_macOsARM64_quickStart_instrumentApplication,
	APM_java_other_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	/// //// Java Done

	/// //// Python Start

	// Django

	// Django-Kubernetes
	APM_python_django_kubernetes_recommendedSteps_setupOtelCollector,
	APM_python_django_kubernetes_recommendedSteps_instrumentApplication,
	APM_python_django_kubernetes_recommendedSteps_runApplication,

	// Django-LinuxAMD64-recommended
	APM_python_django_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_python_django_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_python_django_linuxAMD64_recommendedSteps_runApplication,

	// Django-LinuxAMD64-quickstart
	APM_python_django_linuxAMD64_quickStart_instrumentApplication,
	APM_python_django_linuxAMD64_quickStart_runApplication,

	// Django-LinuxARM64-recommended
	APM_python_django_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_python_django_linuxARM64_recommendedSteps_instrumentApplication,
	APM_python_django_linuxARM64_recommendedSteps_runApplication,

	// Django-LinuxARM64-quickstart
	APM_python_django_linuxARM64_quickStart_instrumentApplication,
	APM_python_django_linuxARM64_quickStart_runApplication,

	// Django-MacOsAMD64-recommended
	APM_python_django_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_python_django_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_python_django_macOsAMD64_recommendedSteps_runApplication,

	// Django-MacOsAMD64-quickstart
	APM_python_django_macOsAMD64_quickStart_instrumentApplication,
	APM_python_django_macOsAMD64_quickStart_runApplication,

	// Django-MacOsARM64-recommended
	APM_python_django_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_python_django_macOsARM64_recommendedSteps_instrumentApplication,
	APM_python_django_macOsARM64_recommendedSteps_runApplication,

	// Django-MacOsARM64-quickstart
	APM_python_django_macOsARM64_quickStart_instrumentApplication,
	APM_python_django_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	// Flask

	// Flask-Kubernetes
	APM_python_flask_kubernetes_recommendedSteps_setupOtelCollector,
	APM_python_flask_kubernetes_recommendedSteps_instrumentApplication,
	APM_python_flask_kubernetes_recommendedSteps_runApplication,

	// Flask-LinuxAMD64-recommended
	APM_python_flask_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_python_flask_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_python_flask_linuxAMD64_recommendedSteps_runApplication,

	// Flask-LinuxAMD64-quickstart
	APM_python_flask_linuxAMD64_quickStart_instrumentApplication,
	APM_python_flask_linuxAMD64_quickStart_runApplication,

	// Flask-LinuxARM64-recommended
	APM_python_flask_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_python_flask_linuxARM64_recommendedSteps_instrumentApplication,
	APM_python_flask_linuxARM64_recommendedSteps_runApplication,

	// Flask-LinuxARM64-quickstart
	APM_python_flask_linuxARM64_quickStart_instrumentApplication,
	APM_python_flask_linuxARM64_quickStart_runApplication,

	// Flask-MacOsAMD64-recommended
	APM_python_flask_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_python_flask_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_python_flask_macOsAMD64_recommendedSteps_runApplication,

	// Flask-MacOsAMD64-quickstart
	APM_python_flask_macOsAMD64_quickStart_instrumentApplication,
	APM_python_flask_macOsAMD64_quickStart_runApplication,

	// Flask-MacOsARM64-recommended
	APM_python_flask_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_python_flask_macOsARM64_recommendedSteps_instrumentApplication,
	APM_python_flask_macOsARM64_recommendedSteps_runApplication,

	// Flask-MacOsARM64-quickstart
	APM_python_flask_macOsARM64_quickStart_instrumentApplication,
	APM_python_flask_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	// FastAPI

	// FastAPI-Kubernetes
	APM_python_fastAPI_kubernetes_recommendedSteps_setupOtelCollector,
	APM_python_fastAPI_kubernetes_recommendedSteps_instrumentApplication,
	APM_python_fastAPI_kubernetes_recommendedSteps_runApplication,

	// FastAPI-LinuxAMD64-recommended
	APM_python_fastAPI_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_python_fastAPI_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_python_fastAPI_linuxAMD64_recommendedSteps_runApplication,

	// FastAPI-LinuxAMD64-quickstart
	APM_python_fastAPI_linuxAMD64_quickStart_instrumentApplication,
	APM_python_fastAPI_linuxAMD64_quickStart_runApplication,

	// FastAPI-LinuxARM64-recommended
	APM_python_fastAPI_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_python_fastAPI_linuxARM64_recommendedSteps_instrumentApplication,
	APM_python_fastAPI_linuxARM64_recommendedSteps_runApplication,

	// FastAPI-LinuxARM64-quickstart
	APM_python_fastAPI_linuxARM64_quickStart_instrumentApplication,
	APM_python_fastAPI_linuxARM64_quickStart_runApplication,

	// FastAPI-MacOsAMD64-recommended
	APM_python_fastAPI_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_python_fastAPI_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_python_fastAPI_macOsAMD64_recommendedSteps_runApplication,

	// FastAPI-MacOsAMD64-quickstart
	APM_python_fastAPI_macOsAMD64_quickStart_instrumentApplication,
	APM_python_fastAPI_macOsAMD64_quickStart_runApplication,

	// FastAPI-MacOsARM64-recommended
	APM_python_fastAPI_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_python_fastAPI_macOsARM64_recommendedSteps_instrumentApplication,
	APM_python_fastAPI_macOsARM64_recommendedSteps_runApplication,

	// FastAPI-MacOsARM64-quickstart
	APM_python_fastAPI_macOsARM64_quickStart_instrumentApplication,
	APM_python_fastAPI_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	// Falcon

	// Falcon-Kubernetes
	APM_python_falcon_kubernetes_recommendedSteps_setupOtelCollector,
	APM_python_falcon_kubernetes_recommendedSteps_instrumentApplication,
	APM_python_falcon_kubernetes_recommendedSteps_runApplication,

	// Falcon-LinuxAMD64-recommended
	APM_python_falcon_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_python_falcon_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_python_falcon_linuxAMD64_recommendedSteps_runApplication,

	// Falcon-LinuxAMD64-quickstart
	APM_python_falcon_linuxAMD64_quickStart_instrumentApplication,
	APM_python_falcon_linuxAMD64_quickStart_runApplication,

	// Falcon-LinuxARM64-recommended
	APM_python_falcon_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_python_falcon_linuxARM64_recommendedSteps_instrumentApplication,
	APM_python_falcon_linuxARM64_recommendedSteps_runApplication,

	// Falcon-LinuxARM64-quickstart
	APM_python_falcon_linuxARM64_quickStart_instrumentApplication,
	APM_python_falcon_linuxARM64_quickStart_runApplication,

	// Falcon-MacOsAMD64-recommended
	APM_python_falcon_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_python_falcon_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_python_falcon_macOsAMD64_recommendedSteps_runApplication,

	// Falcon-MacOsAMD64-quickstart
	APM_python_falcon_macOsAMD64_quickStart_instrumentApplication,
	APM_python_falcon_macOsAMD64_quickStart_runApplication,

	// Falcon-MacOsARM64-recommended
	APM_python_falcon_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_python_falcon_macOsARM64_recommendedSteps_instrumentApplication,
	APM_python_falcon_macOsARM64_recommendedSteps_runApplication,

	// Falcon-MacOsARM64-quickstart
	APM_python_falcon_macOsARM64_quickStart_instrumentApplication,
	APM_python_falcon_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	// Others

	// Others-Kubernetes
	APM_python_other_kubernetes_recommendedSteps_setupOtelCollector,
	APM_python_other_kubernetes_recommendedSteps_instrumentApplication,
	APM_python_other_kubernetes_recommendedSteps_runApplication,

	// Others-LinuxAMD64-recommended
	APM_python_other_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_python_other_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_python_other_linuxAMD64_recommendedSteps_runApplication,

	// Others-LinuxAMD64-quickstart
	APM_python_other_linuxAMD64_quickStart_instrumentApplication,
	APM_python_other_linuxAMD64_quickStart_runApplication,

	// Others-LinuxARM64-recommended
	APM_python_other_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_python_other_linuxARM64_recommendedSteps_instrumentApplication,
	APM_python_other_linuxARM64_recommendedSteps_runApplication,

	// Others-LinuxARM64-quickstart
	APM_python_other_linuxARM64_quickStart_instrumentApplication,
	APM_python_other_linuxARM64_quickStart_runApplication,

	// Others-MacOsAMD64-recommended
	APM_python_other_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_python_other_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_python_other_macOsAMD64_recommendedSteps_runApplication,

	// Others-MacOsAMD64-quickstart
	APM_python_other_macOsAMD64_quickStart_instrumentApplication,
	APM_python_other_macOsAMD64_quickStart_runApplication,

	// Others-MacOsARM64-recommended
	APM_python_other_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_python_other_macOsARM64_recommendedSteps_instrumentApplication,
	APM_python_other_macOsARM64_recommendedSteps_runApplication,

	// Others-MacOsARM64-quickstart
	APM_python_other_macOsARM64_quickStart_instrumentApplication,
	APM_python_other_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	/// //// Python Done

	/// //// JavaScript Start

	// Express

	// Express-Kubernetes
	APM_javascript_express_kubernetes_recommendedSteps_setupOtelCollector,
	APM_javascript_express_kubernetes_recommendedSteps_instrumentApplication,
	APM_javascript_express_kubernetes_recommendedSteps_runApplication,

	// Express-LinuxAMD64-recommended
	APM_javascript_express_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_express_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_express_linuxAMD64_recommendedSteps_runApplication,

	// Express-LinuxAMD64-quickstart
	APM_javascript_express_linuxAMD64_quickStart_instrumentApplication,
	APM_javascript_express_linuxAMD64_quickStart_runApplication,

	// Express-LinuxARM64-recommended
	APM_javascript_express_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_express_linuxARM64_recommendedSteps_instrumentApplication,
	APM_javascript_express_linuxARM64_recommendedSteps_runApplication,

	// Express-LinuxARM64-quickstart
	APM_javascript_express_linuxARM64_quickStart_instrumentApplication,
	APM_javascript_express_linuxARM64_quickStart_runApplication,

	// Express-MacOsAMD64-recommended
	APM_javascript_express_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_express_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_express_macOsAMD64_recommendedSteps_runApplication,

	// Express-MacOsAMD64-quickstart
	APM_javascript_express_macOsAMD64_quickStart_instrumentApplication,
	APM_javascript_express_macOsAMD64_quickStart_runApplication,

	// Express-MacOsARM64-recommended
	APM_javascript_express_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_express_macOsARM64_recommendedSteps_instrumentApplication,
	APM_javascript_express_macOsARM64_recommendedSteps_runApplication,

	// Express-MacOsARM64-quickstart
	APM_javascript_express_macOsARM64_quickStart_instrumentApplication,
	APM_javascript_express_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	// NestJS

	// NestJS-Kubernetes
	APM_javascript_nestjs_kubernetes_recommendedSteps_setupOtelCollector,
	APM_javascript_nestjs_kubernetes_recommendedSteps_instrumentApplication,
	APM_javascript_nestjs_kubernetes_recommendedSteps_runApplication,

	// NestJS-LinuxAMD64-recommended
	APM_javascript_nestjs_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_nestjs_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_nestjs_linuxAMD64_recommendedSteps_runApplication,

	// NestJS-LinuxAMD64-quickstart
	APM_javascript_nestjs_linuxAMD64_quickStart_instrumentApplication,
	APM_javascript_nestjs_linuxAMD64_quickStart_runApplication,

	// NestJS-LinuxARM64-recommended
	APM_javascript_nestjs_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_nestjs_linuxARM64_recommendedSteps_instrumentApplication,
	APM_javascript_nestjs_linuxARM64_recommendedSteps_runApplication,

	// NestJS-LinuxARM64-quinestjs
	APM_javascript_nestjs_linuxARM64_quickStart_instrumentApplication,
	APM_javascript_nestjs_linuxARM64_quickStart_runApplication,

	// NestJS-MacOsAMD64-recommended
	APM_javascript_nestjs_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_nestjs_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_nestjs_macOsAMD64_recommendedSteps_runApplication,

	// NestJS-MacOsAMD64-quickstart
	APM_javascript_nestjs_macOsAMD64_quickStart_instrumentApplication,
	APM_javascript_nestjs_macOsAMD64_quickStart_runApplication,

	// NestJS-MacOsARM64-recommended
	APM_javascript_nestjs_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_nestjs_macOsARM64_recommendedSteps_instrumentApplication,
	APM_javascript_nestjs_macOsARM64_recommendedSteps_runApplication,

	// NestJS-MacOsARM64-quickstart
	APM_javascript_nestjs_macOsARM64_quickStart_instrumentApplication,
	APM_javascript_nestjs_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	// NodeJS

	// NodeJS-Kubernetes
	APM_javascript_nodejs_kubernetes_recommendedSteps_setupOtelCollector,
	APM_javascript_nodejs_kubernetes_recommendedSteps_instrumentApplication,
	APM_javascript_nodejs_kubernetes_recommendedSteps_runApplication,

	// NodeJS-LinuxAMD64-recommended
	APM_javascript_nodejs_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_nodejs_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_nodejs_linuxAMD64_recommendedSteps_runApplication,

	// NodeJS-LinuxAMD64-quickstart
	APM_javascript_nodejs_linuxAMD64_quickStart_instrumentApplication,
	APM_javascript_nodejs_linuxAMD64_quickStart_runApplication,

	// NodeJS-LinuxARM64-recommended
	APM_javascript_nodejs_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_nodejs_linuxARM64_recommendedSteps_instrumentApplication,
	APM_javascript_nodejs_linuxARM64_recommendedSteps_runApplication,

	// NodeJS-LinuxARM64-quinestjs
	APM_javascript_nodejs_linuxARM64_quickStart_instrumentApplication,
	APM_javascript_nodejs_linuxARM64_quickStart_runApplication,

	// NodeJS-MacOsAMD64-recommended
	APM_javascript_nodejs_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_nodejs_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_nodejs_macOsAMD64_recommendedSteps_runApplication,

	// NodeJS-MacOsAMD64-quickstart
	APM_javascript_nodejs_macOsAMD64_quickStart_instrumentApplication,
	APM_javascript_nodejs_macOsAMD64_quickStart_runApplication,

	// NodeJS-MacOsARM64-recommended
	APM_javascript_nodejs_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_nodejs_macOsARM64_recommendedSteps_instrumentApplication,
	APM_javascript_nodejs_macOsARM64_recommendedSteps_runApplication,

	// NodeJS-MacOsARM64-quickstart
	APM_javascript_nodejs_macOsARM64_quickStart_instrumentApplication,
	APM_javascript_nodejs_macOsARM64_quickStart_runApplication,

	/// React JS

	// ReeactJS-Kubernetes
	APM_javascript_reactjs_kubernetes_recommendedSteps_setupOtelCollector,
	APM_javascript_reactjs_kubernetes_recommendedSteps_instrumentApplication,
	APM_javascript_reactjs_kubernetes_recommendedSteps_runApplication,

	// ReactJS-LinuxAMD64-quickstart
	APM_javascript_reactjs_linuxAMD64_quickStart_instrumentApplication,
	APM_javascript_reactjs_linuxAMD64_quickStart_runApplication,

	// // ReactJS-LinuxAMD64-recommended
	APM_javascript_reactjs_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_reactjs_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_reactjs_linuxAMD64_recommendedSteps_runApplication,

	// ReactJS-LinuxARM64-quickstart
	APM_javascript_reactjs_linuxARM64_quickStart_instrumentApplication,
	APM_javascript_reactjs_linuxARM64_quickStart_runApplication,

	// ReactJS-LinuxARM64-recommended
	APM_javascript_reactjs_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_reactjs_linuxARM64_recommendedSteps_instrumentApplication,
	APM_javascript_reactjs_linuxARM64_recommendedSteps_runApplication,

	// ReactJS-MacOsAMD64-quickstart
	APM_javascript_reactjs_macOsAMD64_quickStart_instrumentApplication,
	APM_javascript_reactjs_macOsAMD64_quickStart_runApplication,

	// ReactJS-MacOsAMD64-recommended
	APM_javascript_reactjs_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_reactjs_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_reactjs_macOsAMD64_recommendedSteps_runApplication,

	// ReactJS-MacOsARM64-quickstart
	APM_javascript_reactjs_macOsARM64_quickStart_instrumentApplication,
	APM_javascript_reactjs_macOsARM64_quickStart_runApplication,

	// ReactJS-MacOsARM64-recommended
	APM_javascript_reactjs_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_reactjs_macOsARM64_recommendedSteps_instrumentApplication,
	APM_javascript_reactjs_macOsARM64_recommendedSteps_runApplication,

	/// -------

	// Angular-Kubernetes
	APM_javascript_angular_kubernetes_recommendedSteps_setupOtelCollector,
	APM_javascript_angular_kubernetes_recommendedSteps_instrumentApplication,
	APM_javascript_angular_kubernetes_recommendedSteps_runApplication,

	// Angular-LinuxAMD64-recommended
	APM_javascript_angular_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_angular_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_angular_linuxAMD64_recommendedSteps_runApplication,

	// Angular-LinuxAMD64-quickstart
	APM_javascript_angular_linuxAMD64_quickStart_instrumentApplication,
	APM_javascript_angular_linuxAMD64_quickStart_runApplication,

	// Angular-LinuxARM64-recommended
	APM_javascript_angular_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_angular_linuxARM64_recommendedSteps_instrumentApplication,
	APM_javascript_angular_linuxARM64_recommendedSteps_runApplication,

	// Angular-LinuxARM64-quickstart
	APM_javascript_angular_linuxARM64_quickStart_instrumentApplication,
	APM_javascript_angular_linuxARM64_quickStart_runApplication,

	// Angular-MacOsAMD64-recommended
	APM_javascript_angular_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_angular_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_angular_macOsAMD64_recommendedSteps_runApplication,

	// Angular-MacOsAMD64-quickstart
	APM_javascript_angular_macOsAMD64_quickStart_instrumentApplication,
	APM_javascript_angular_macOsAMD64_quickStart_runApplication,

	// Angular-MacOsARM64-recommended
	APM_javascript_angular_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_angular_macOsARM64_recommendedSteps_instrumentApplication,
	APM_javascript_angular_macOsARM64_recommendedSteps_runApplication,

	// Angular-MacOsARM64-quickstart
	APM_javascript_angular_macOsARM64_quickStart_instrumentApplication,
	APM_javascript_angular_macOsARM64_quickStart_runApplication,

	///--------------------

	/// // JavaScript Others

	APM_javascript_others_kubernetes_recommendedSteps_setupOtelCollector,
	APM_javascript_others_kubernetes_recommendedSteps_instrumentApplication,
	APM_javascript_others_kubernetes_recommendedSteps_runApplication,

	// Others-JavaScript-LinuxAMD64-quickstart
	APM_javascript_others_linuxAMD64_quickStart_instrumentApplication,
	APM_javascript_others_linuxAMD64_quickStart_runApplication,

	// // Others-JavaScript-LinuxAMD64-recommended
	APM_javascript_others_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_others_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_others_linuxAMD64_recommendedSteps_runApplication,

	// Others-JavaScript-LinuxARM64-quiOthers
	APM_javascript_others_linuxARM64_quickStart_instrumentApplication,
	APM_javascript_others_linuxARM64_quickStart_runApplication,

	// Others-JavaScript-LinuxARM64-recommended
	APM_javascript_others_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_others_linuxARM64_recommendedSteps_instrumentApplication,
	APM_javascript_others_linuxARM64_recommendedSteps_runApplication,

	// Others-JavaScript-MacOsAMD64-quickstart
	APM_javascript_others_macOsAMD64_quickStart_instrumentApplication,
	APM_javascript_others_macOsAMD64_quickStart_runApplication,

	// Others-JavaScript-MacOsAMD64-recommended
	APM_javascript_others_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_javascript_others_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_javascript_others_macOsAMD64_recommendedSteps_runApplication,

	// Others-JavaScript-MacOsARM64-quickstart
	APM_javascript_others_macOsARM64_quickStart_instrumentApplication,
	APM_javascript_others_macOsARM64_quickStart_runApplication,

	// Others-JavaScript-MacOsARM64-recommended
	APM_javascript_others_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_javascript_others_macOsARM64_recommendedSteps_instrumentApplication,
	APM_javascript_others_macOsARM64_recommendedSteps_runApplication,

	// ------------------------------------------------------------------------------------------------

	/// //// JavaScript Done

	/// //// Go Start

	// Go-Kubernetes
	APM_go_kubernetes_recommendedSteps_setupOtelCollector,
	APM_go_kubernetes_recommendedSteps_instrumentApplication,
	APM_go_kubernetes_recommendedSteps_runApplication,

	// Go-LinuxAMD64-recommended
	APM_go_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_go_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_go_linuxAMD64_recommendedSteps_runApplication,

	// Go-LinuxAMD64-quickstart
	APM_go_linuxAMD64_quickStart_instrumentApplication,
	APM_go_linuxAMD64_quickStart_runApplication,

	// Go-LinuxARM64-recommended
	APM_go_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_go_linuxARM64_recommendedSteps_instrumentApplication,
	APM_go_linuxARM64_recommendedSteps_runApplication,

	// Go-LinuxARM64-quinestjs
	APM_go_linuxARM64_quickStart_instrumentApplication,
	APM_go_linuxARM64_quickStart_runApplication,

	// Go-MacOsAMD64-recommended
	APM_go_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_go_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_go_macOsAMD64_recommendedSteps_runApplication,

	// Go-MacOsAMD64-quickstart
	APM_go_macOsAMD64_quickStart_instrumentApplication,
	APM_go_macOsAMD64_quickStart_runApplication,

	// Go-MacOsARM64-recommended
	APM_go_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_go_macOsARM64_recommendedSteps_instrumentApplication,
	APM_go_macOsARM64_recommendedSteps_runApplication,

	// Go-MacOsARM64-quickstart
	APM_go_macOsARM64_quickStart_instrumentApplication,
	APM_go_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	/// //// Go Done

	/// //// ROR Start

	// ROR-Kubernetes
	APM_rails_kubernetes_recommendedSteps_setupOtelCollector,
	APM_rails_kubernetes_recommendedSteps_instrumentApplication,
	APM_rails_kubernetes_recommendedSteps_runApplication,

	// ROR-LinuxAMD64-recommended
	APM_rails_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_rails_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_rails_linuxAMD64_recommendedSteps_runApplication,

	// ROR-LinuxAMD64-quickstart
	APM_rails_linuxAMD64_quickStart_instrumentApplication,
	APM_rails_linuxAMD64_quickStart_runApplication,

	// ROR-LinuxARM64-recommended
	APM_rails_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_rails_linuxARM64_recommendedSteps_instrumentApplication,
	APM_rails_linuxARM64_recommendedSteps_runApplication,

	// ROR-LinuxARM64-quickstart
	APM_rails_linuxARM64_quickStart_instrumentApplication,
	APM_rails_linuxARM64_quickStart_runApplication,

	// ROR-MacOsAMD64-recommended
	APM_rails_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_rails_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_rails_macOsAMD64_recommendedSteps_runApplication,

	// ROR-MacOsAMD64-quickstart
	APM_rails_macOsAMD64_quickStart_instrumentApplication,
	APM_rails_macOsAMD64_quickStart_runApplication,

	// ROR-MacOsARM64-recommended
	APM_rails_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_rails_macOsARM64_recommendedSteps_instrumentApplication,
	APM_rails_macOsARM64_recommendedSteps_runApplication,

	// ROR-MacOsARM64-quickstart
	APM_rails_macOsARM64_quickStart_instrumentApplication,
	APM_rails_macOsARM64_quickStart_runApplication,

	// ------------------------------------------------------------------------------------------------

	/// //// ROR Done

	/// //// .NET Start

	// dotnet-Kubernetes
	APM_dotnet_kubernetes_recommendedSteps_setupOtelCollector,
	APM_dotnet_kubernetes_recommendedSteps_instrumentApplication,
	APM_dotnet_kubernetes_recommendedSteps_runApplication,

	// dotnet-LinuxAMD64-quickstart
	APM_dotnet_linuxAMD64_quickStart_instrumentApplication,
	APM_dotnet_linuxAMD64_quickStart_runApplication,

	// dotnet-LinuxAMD64-recommended
	APM_dotnet_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_dotnet_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_dotnet_linuxAMD64_recommendedSteps_runApplication,

	// dotnet-LinuxARM64-quickstart
	APM_dotnet_linuxARM64_quickStart_instrumentApplication,
	APM_dotnet_linuxARM64_quickStart_runApplication,

	// dotnet-LinuxARM64-recommended
	APM_dotnet_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_dotnet_linuxARM64_recommendedSteps_instrumentApplication,
	APM_dotnet_linuxARM64_recommendedSteps_runApplication,

	// dotnet-MacOsAMD64-quickstart
	APM_dotnet_macOsAMD64_quickStart_instrumentApplication,
	APM_dotnet_macOsAMD64_quickStart_runApplication,

	// dotnet-MacOsAMD64-recommended
	APM_dotnet_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_dotnet_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_dotnet_macOsAMD64_recommendedSteps_runApplication,

	// dotnet-MacOsARM64-quickstart
	APM_dotnet_macOsARM64_quickStart_instrumentApplication,
	APM_dotnet_macOsARM64_quickStart_runApplication,

	// dotnet-MacOsARM64-recommended
	APM_dotnet_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_dotnet_macOsARM64_recommendedSteps_instrumentApplication,
	APM_dotnet_macOsARM64_recommendedSteps_runApplication,

	// Rust
	APM_rust_kubernetes_recommendedSteps_setupOtelCollector,
	APM_rust_kubernetes_recommendedSteps_instrumentApplication,
	APM_rust_kubernetes_recommendedSteps_runApplication,

	APM_rust_linuxAMD64_quickStart_instrumentApplication,
	APM_rust_linuxAMD64_quickStart_runApplication,

	APM_rust_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_rust_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_rust_linuxAMD64_recommendedSteps_runApplication,

	APM_rust_linuxARM64_quickStart_instrumentApplication,
	APM_rust_linuxARM64_quickStart_runApplication,

	APM_rust_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_rust_linuxARM64_recommendedSteps_instrumentApplication,
	APM_rust_linuxARM64_recommendedSteps_runApplication,

	APM_rust_macOsAMD64_quickStart_instrumentApplication,
	APM_rust_macOsAMD64_quickStart_runApplication,

	APM_rust_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_rust_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_rust_macOsAMD64_recommendedSteps_runApplication,

	APM_rust_macOsARM64_quickStart_instrumentApplication,
	APM_rust_macOsARM64_quickStart_runApplication,

	APM_rust_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_rust_macOsARM64_recommendedSteps_instrumentApplication,
	APM_rust_macOsARM64_recommendedSteps_runApplication,

	// Elixir
	APM_elixir_kubernetes_recommendedSteps_setupOtelCollector,
	APM_elixir_kubernetes_recommendedSteps_instrumentApplication,
	APM_elixir_kubernetes_recommendedSteps_runApplication,

	APM_elixir_linuxAMD64_quickStart_instrumentApplication,
	APM_elixir_linuxAMD64_quickStart_runApplication,

	APM_elixir_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_elixir_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_elixir_linuxAMD64_recommendedSteps_runApplication,

	APM_elixir_linuxARM64_quickStart_instrumentApplication,
	APM_elixir_linuxARM64_quickStart_runApplication,

	APM_elixir_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_elixir_linuxARM64_recommendedSteps_instrumentApplication,
	APM_elixir_linuxARM64_recommendedSteps_runApplication,

	APM_elixir_macOsAMD64_quickStart_instrumentApplication,
	APM_elixir_macOsAMD64_quickStart_runApplication,

	APM_elixir_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_elixir_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_elixir_macOsAMD64_recommendedSteps_runApplication,

	APM_elixir_macOsARM64_quickStart_instrumentApplication,
	APM_elixir_macOsARM64_quickStart_runApplication,

	APM_elixir_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_elixir_macOsARM64_recommendedSteps_instrumentApplication,
	APM_elixir_macOsARM64_recommendedSteps_runApplication,

	// Swift
	APM_swift_kubernetes_recommendedSteps_setupOtelCollector,
	APM_swift_kubernetes_recommendedSteps_instrumentApplication,
	APM_swift_kubernetes_recommendedSteps_runApplication,

	APM_swift_linuxAMD64_quickStart_instrumentApplication,
	APM_swift_linuxAMD64_quickStart_runApplication,

	APM_swift_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_swift_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_swift_linuxAMD64_recommendedSteps_runApplication,

	APM_swift_linuxARM64_quickStart_instrumentApplication,
	APM_swift_linuxARM64_quickStart_runApplication,

	APM_swift_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_swift_linuxARM64_recommendedSteps_instrumentApplication,
	APM_swift_linuxARM64_recommendedSteps_runApplication,

	APM_swift_macOsAMD64_quickStart_instrumentApplication,
	APM_swift_macOsAMD64_quickStart_runApplication,

	APM_swift_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_swift_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_swift_macOsAMD64_recommendedSteps_runApplication,

	APM_swift_macOsARM64_quickStart_instrumentApplication,
	APM_swift_macOsARM64_quickStart_runApplication,

	APM_swift_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_swift_macOsARM64_recommendedSteps_instrumentApplication,
	APM_swift_macOsARM64_recommendedSteps_runApplication,

	APM_php_kubernetes_recommendedSteps_setupOtelCollector,
	APM_php_kubernetes_recommendedSteps_instrumentApplication,
	APM_php_kubernetes_recommendedSteps_runApplication,

	APM_php_linuxAMD64_quickStart_instrumentApplication,
	APM_php_linuxAMD64_quickStart_runApplication,

	APM_php_linuxAMD64_recommendedSteps_setupOtelCollector,
	APM_php_linuxAMD64_recommendedSteps_instrumentApplication,
	APM_php_linuxAMD64_recommendedSteps_runApplication,

	APM_php_linuxARM64_quickStart_instrumentApplication,
	APM_php_linuxARM64_quickStart_runApplication,

	APM_php_linuxARM64_recommendedSteps_setupOtelCollector,
	APM_php_linuxARM64_recommendedSteps_instrumentApplication,
	APM_php_linuxARM64_recommendedSteps_runApplication,

	APM_php_macOsAMD64_quickStart_instrumentApplication,
	APM_php_macOsAMD64_quickStart_runApplication,

	APM_php_macOsAMD64_recommendedSteps_setupOtelCollector,
	APM_php_macOsAMD64_recommendedSteps_instrumentApplication,
	APM_php_macOsAMD64_recommendedSteps_runApplication,

	APM_php_macOsARM64_quickStart_instrumentApplication,
	APM_php_macOsARM64_quickStart_runApplication,

	APM_php_macOsARM64_recommendedSteps_setupOtelCollector,
	APM_php_macOsARM64_recommendedSteps_instrumentApplication,
	APM_php_macOsARM64_recommendedSteps_runApplication,

	/// ///// Docker Steps

	APM_python_django_docker_quickStart_instrumentApplication,
	APM_python_django_docker_quickStart_runApplication,

	APM_python_django_docker_recommendedSteps_setupOtelCollector,
	APM_python_django_docker_recommendedSteps_instrumentApplication,
	APM_python_django_docker_recommendedSteps_runApplication,

	APM_python_flask_docker_quickStart_instrumentApplication,
	APM_python_flask_docker_quickStart_runApplication,

	APM_python_flask_docker_recommendedSteps_setupOtelCollector,
	APM_python_flask_docker_recommendedSteps_instrumentApplication,
	APM_python_flask_docker_recommendedSteps_runApplication,

	APM_python_fastAPI_docker_quickStart_instrumentApplication,
	APM_python_fastAPI_docker_quickStart_runApplication,

	APM_python_fastAPI_docker_recommendedSteps_setupOtelCollector,
	APM_python_fastAPI_docker_recommendedSteps_instrumentApplication,
	APM_python_fastAPI_docker_recommendedSteps_runApplication,

	APM_python_falcon_docker_quickStart_instrumentApplication,
	APM_python_falcon_docker_quickStart_runApplication,

	APM_python_falcon_docker_recommendedSteps_setupOtelCollector,
	APM_python_falcon_docker_recommendedSteps_instrumentApplication,
	APM_python_falcon_docker_recommendedSteps_runApplication,

	APM_python_other_docker_quickStart_instrumentApplication,
	APM_python_other_docker_quickStart_runApplication,

	APM_python_other_docker_recommendedSteps_setupOtelCollector,
	APM_python_other_docker_recommendedSteps_instrumentApplication,
	APM_python_other_docker_recommendedSteps_runApplication,

	APM_javascript_nodejs_docker_quickStart_instrumentApplication,
	APM_javascript_nodejs_docker_quickStart_runApplication,

	APM_javascript_nodejs_docker_recommendedSteps_setupOtelCollector,
	APM_javascript_nodejs_docker_recommendedSteps_instrumentApplication,
	APM_javascript_nodejs_docker_recommendedSteps_runApplication,

	APM_javascript_nestjs_docker_quickStart_instrumentApplication,
	APM_javascript_nestjs_docker_quickStart_runApplication,

	APM_javascript_nestjs_docker_recommendedSteps_instrumentApplication,
	APM_javascript_nestjs_docker_recommendedSteps_setupOtelCollector,
	APM_javascript_nestjs_docker_recommendedSteps_runApplication,

	APM_javascript_express_docker_quickStart_instrumentApplication,
	APM_javascript_express_docker_quickStart_runApplication,

	APM_javascript_express_docker_recommendedSteps_setupOtelCollector,
	APM_javascript_express_docker_recommendedSteps_instrumentApplication,
	APM_javascript_express_docker_recommendedSteps_runApplication,

	APM_javascript_reactjs_docker_quickStart_instrumentApplication,
	APM_javascript_reactjs_docker_quickStart_runApplication,

	APM_javascript_reactjs_docker_recommendedSteps_setupOtelCollector,
	APM_javascript_reactjs_docker_recommendedSteps_instrumentApplication,
	APM_javascript_reactjs_docker_recommendedSteps_runApplication,

	APM_javascript_angular_docker_quickStart_instrumentApplication,
	APM_javascript_angular_docker_quickStart_runApplication,

	APM_javascript_angular_docker_recommendedSteps_setupOtelCollector,
	APM_javascript_angular_docker_recommendedSteps_instrumentApplication,
	APM_javascript_angular_docker_recommendedSteps_runApplication,

	APM_javascript_others_docker_quickStart_instrumentApplication,
	APM_javascript_others_docker_quickStart_runApplication,

	APM_javascript_others_docker_recommendedSteps_setupOtelCollector,
	APM_javascript_others_docker_recommendedSteps_instrumentApplication,
	APM_javascript_others_docker_recommendedSteps_runApplication,

	APM_java_jboss_docker_quickStart_instrumentApplication,
	APM_java_jboss_docker_quickStart_runApplication,

	APM_java_jboss_docker_recommendedSteps_setupOtelCollector,
	APM_java_jboss_docker_recommendedSteps_instrumentApplication,
	APM_java_jboss_docker_recommendedSteps_runApplication,

	APM_java_springBoot_docker_quickStart_instrumentApplication,
	APM_java_springBoot_docker_quickStart_runApplication,

	APM_java_springBoot_docker_recommendedSteps_setupOtelCollector,
	APM_java_springBoot_docker_recommendedSteps_instrumentApplication,
	APM_java_springBoot_docker_recommendedSteps_runApplication,

	APM_java_tomcat_docker_quickStart_instrumentApplication,
	APM_java_tomcat_docker_quickStart_runApplication,

	APM_java_tomcat_docker_recommendedSteps_setupOtelCollector,
	APM_java_tomcat_docker_recommendedSteps_instrumentApplication,
	APM_java_tomcat_docker_recommendedSteps_runApplication,

	APM_java_other_docker_quickStart_instrumentApplication,
	APM_java_other_docker_quickStart_runApplication,

	APM_java_other_docker_recommendedSteps_setupOtelCollector,
	APM_java_other_docker_recommendedSteps_instrumentApplication,
	APM_java_other_docker_recommendedSteps_runApplication,

	APM_go_docker_quickStart_instrumentApplication,
	APM_go_docker_quickStart_runApplication,

	APM_go_docker_recommendedSteps_setupOtelCollector,
	APM_go_docker_recommendedSteps_instrumentApplication,
	APM_go_docker_recommendedSteps_runApplication,

	APM_rust_docker_quickStart_instrumentApplication,
	APM_rust_docker_quickStart_runApplication,

	APM_rust_docker_recommendedSteps_setupOtelCollector,
	APM_rust_docker_recommendedSteps_instrumentApplication,
	APM_rust_docker_recommendedSteps_runApplication,

	APM_elixir_docker_quickStart_instrumentApplication,
	APM_elixir_docker_quickStart_runApplication,

	APM_elixir_docker_recommendedSteps_setupOtelCollector,
	APM_elixir_docker_recommendedSteps_instrumentApplication,
	APM_elixir_docker_recommendedSteps_runApplication,

	APM_dotnet_docker_quickStart_instrumentApplication,
	APM_dotnet_docker_quickStart_runApplication,

	APM_dotnet_docker_recommendedSteps_setupOtelCollector,
	APM_dotnet_docker_recommendedSteps_instrumentApplication,
	APM_dotnet_docker_recommendedSteps_runApplication,

	APM_rails_docker_quickStart_instrumentApplication,
	APM_rails_docker_quickStart_runApplication,

	APM_rails_docker_recommendedSteps_setupOtelCollector,
	APM_rails_docker_recommendedSteps_instrumentApplication,
	APM_rails_docker_recommendedSteps_runApplication,

	APM_swift_docker_quickStart_instrumentApplication,
	APM_swift_docker_quickStart_runApplication,

	APM_swift_docker_recommendedSteps_setupOtelCollector,
	APM_swift_docker_recommendedSteps_instrumentApplication,
	APM_swift_docker_recommendedSteps_runApplication,

	APM_php_docker_quickStart_instrumentApplication,
	APM_php_docker_quickStart_runApplication,

	APM_php_docker_recommendedSteps_setupOtelCollector,
	APM_php_docker_recommendedSteps_instrumentApplication,
	APM_php_docker_recommendedSteps_runApplication,

	/// ///// Windows Steps

	APM_python_django_windows_quickStart_instrumentApplication,
	APM_python_django_windows_quickStart_runApplication,

	APM_python_django_windows_recommendedSteps_setupOtelCollector,
	APM_python_django_windows_recommendedSteps_instrumentApplication,
	APM_python_django_windows_recommendedSteps_runApplication,

	APM_python_flask_windows_quickStart_instrumentApplication,
	APM_python_flask_windows_quickStart_runApplication,

	APM_python_flask_windows_recommendedSteps_setupOtelCollector,
	APM_python_flask_windows_recommendedSteps_instrumentApplication,
	APM_python_flask_windows_recommendedSteps_runApplication,

	APM_python_fastAPI_windows_quickStart_instrumentApplication,
	APM_python_fastAPI_windows_quickStart_runApplication,

	APM_python_fastAPI_windows_recommendedSteps_setupOtelCollector,
	APM_python_fastAPI_windows_recommendedSteps_instrumentApplication,
	APM_python_fastAPI_windows_recommendedSteps_runApplication,

	APM_python_falcon_windows_quickStart_instrumentApplication,
	APM_python_falcon_windows_quickStart_runApplication,

	APM_python_falcon_windows_recommendedSteps_setupOtelCollector,
	APM_python_falcon_windows_recommendedSteps_instrumentApplication,
	APM_python_falcon_windows_recommendedSteps_runApplication,

	APM_python_other_windows_quickStart_instrumentApplication,
	APM_python_other_windows_quickStart_runApplication,

	APM_python_other_windows_recommendedSteps_setupOtelCollector,
	APM_python_other_windows_recommendedSteps_instrumentApplication,
	APM_python_other_windows_recommendedSteps_runApplication,

	APM_javascript_nodejs_windows_quickStart_instrumentApplication,
	APM_javascript_nodejs_windows_quickStart_runApplication,

	APM_javascript_nodejs_windows_recommendedSteps_setupOtelCollector,
	APM_javascript_nodejs_windows_recommendedSteps_instrumentApplication,
	APM_javascript_nodejs_windows_recommendedSteps_runApplication,

	APM_javascript_nestjs_windows_quickStart_instrumentApplication,
	APM_javascript_nestjs_windows_quickStart_runApplication,

	APM_javascript_nestjs_windows_recommendedSteps_instrumentApplication,
	APM_javascript_nestjs_windows_recommendedSteps_setupOtelCollector,
	APM_javascript_nestjs_windows_recommendedSteps_runApplication,

	APM_javascript_express_windows_quickStart_instrumentApplication,
	APM_javascript_express_windows_quickStart_runApplication,

	APM_javascript_express_windows_recommendedSteps_setupOtelCollector,
	APM_javascript_express_windows_recommendedSteps_instrumentApplication,
	APM_javascript_express_windows_recommendedSteps_runApplication,

	APM_javascript_reactjs_windows_quickStart_instrumentApplication,
	APM_javascript_reactjs_windows_quickStart_runApplication,

	APM_javascript_reactjs_windows_recommendedSteps_setupOtelCollector,
	APM_javascript_reactjs_windows_recommendedSteps_instrumentApplication,
	APM_javascript_reactjs_windows_recommendedSteps_runApplication,

	APM_javascript_angular_windows_quickStart_instrumentApplication,
	APM_javascript_angular_windows_quickStart_runApplication,

	APM_javascript_angular_windows_recommendedSteps_setupOtelCollector,
	APM_javascript_angular_windows_recommendedSteps_instrumentApplication,
	APM_javascript_angular_windows_recommendedSteps_runApplication,

	APM_javascript_others_windows_quickStart_instrumentApplication,
	APM_javascript_others_windows_quickStart_runApplication,

	APM_javascript_others_windows_recommendedSteps_setupOtelCollector,
	APM_javascript_others_windows_recommendedSteps_instrumentApplication,
	APM_javascript_others_windows_recommendedSteps_runApplication,

	APM_java_jboss_windows_quickStart_instrumentApplication,
	APM_java_jboss_windows_quickStart_runApplication,

	APM_java_jboss_windows_recommendedSteps_setupOtelCollector,
	APM_java_jboss_windows_recommendedSteps_instrumentApplication,
	APM_java_jboss_windows_recommendedSteps_runApplication,

	APM_java_springBoot_windows_quickStart_instrumentApplication,
	APM_java_springBoot_windows_quickStart_runApplication,

	APM_java_springBoot_windows_recommendedSteps_setupOtelCollector,
	APM_java_springBoot_windows_recommendedSteps_instrumentApplication,
	APM_java_springBoot_windows_recommendedSteps_runApplication,

	APM_java_tomcat_windows_quickStart_instrumentApplication,
	APM_java_tomcat_windows_quickStart_runApplication,

	APM_java_tomcat_windows_recommendedSteps_setupOtelCollector,
	APM_java_tomcat_windows_recommendedSteps_instrumentApplication,
	APM_java_tomcat_windows_recommendedSteps_runApplication,

	APM_java_other_windows_quickStart_instrumentApplication,
	APM_java_other_windows_quickStart_runApplication,

	APM_java_other_windows_recommendedSteps_setupOtelCollector,
	APM_java_other_windows_recommendedSteps_instrumentApplication,
	APM_java_other_windows_recommendedSteps_runApplication,

	APM_go_windows_quickStart_instrumentApplication,
	APM_go_windows_quickStart_runApplication,

	APM_go_windows_recommendedSteps_setupOtelCollector,
	APM_go_windows_recommendedSteps_instrumentApplication,
	APM_go_windows_recommendedSteps_runApplication,

	APM_rust_windows_quickStart_instrumentApplication,
	APM_rust_windows_quickStart_runApplication,

	APM_rust_windows_recommendedSteps_setupOtelCollector,
	APM_rust_windows_recommendedSteps_instrumentApplication,
	APM_rust_windows_recommendedSteps_runApplication,

	APM_elixir_windows_quickStart_instrumentApplication,
	APM_elixir_windows_quickStart_runApplication,

	APM_elixir_windows_recommendedSteps_setupOtelCollector,
	APM_elixir_windows_recommendedSteps_instrumentApplication,
	APM_elixir_windows_recommendedSteps_runApplication,

	APM_dotnet_windows_quickStart_instrumentApplication,
	APM_dotnet_windows_quickStart_runApplication,

	APM_dotnet_windows_recommendedSteps_setupOtelCollector,
	APM_dotnet_windows_recommendedSteps_instrumentApplication,
	APM_dotnet_windows_recommendedSteps_runApplication,

	APM_rails_windows_quickStart_instrumentApplication,
	APM_rails_windows_quickStart_runApplication,

	APM_rails_windows_recommendedSteps_setupOtelCollector,
	APM_rails_windows_recommendedSteps_instrumentApplication,
	APM_rails_windows_recommendedSteps_runApplication,

	APM_swift_windows_quickStart_instrumentApplication,
	APM_swift_windows_quickStart_runApplication,

	APM_swift_windows_recommendedSteps_setupOtelCollector,
	APM_swift_windows_recommendedSteps_instrumentApplication,
	APM_swift_windows_recommendedSteps_runApplication,

	APM_php_windows_quickStart_instrumentApplication,
	APM_php_windows_quickStart_runApplication,

	APM_php_windows_recommendedSteps_setupOtelCollector,
	APM_php_windows_recommendedSteps_instrumentApplication,
	APM_php_windows_recommendedSteps_runApplication,
};
