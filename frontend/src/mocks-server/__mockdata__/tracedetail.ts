/* eslint-disable sonarjs/no-duplicate-string */
export const traceDetailResponse = [
	{
		startTimestampMillis: 1721304358677,
		endTimestampMillis: 1721304360928,
		columns: [
			'__time',
			'SpanId',
			'TraceId',
			'ServiceName',
			'Name',
			'Kind',
			'DurationNano',
			'TagsKeys',
			'TagsValues',
			'References',
			'Events',
			'HasError',
			'StatusMessage',
			'StatusCodeString',
			'SpanKind',
		],
		events: [
			[
				1721304359454,
				'74fff7b42cbc923b',
				'000000000000000071dc9b0a338729b4',
				'customer',
				'HTTP GET /customer',
				'2',
				'348950000',
				[
					'signoz.collector.id',
					'client-uuid',
					'component',
					'host.name',
					'http.method',
					'http.url',
					'ip',
					'http.status_code',
					'opencensus.exporterversion',
					'service.name',
				],
				[
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'7cd5f22c2034bc4e',
					'net/http',
					'4f6ec470feea',
					'GET',
					'/customer?customer=731',
					'**********',
					'200',
					'Jaeger-Go-2.30.0',
					'customer',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=1518282c4475a6d7, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304359454282000,"attributeMap":{"level":"info","method":"GET","url":"/customer?customer=731"}}',
					'{"name":"Loading customer","timeUnixNano":1721304359454308000,"attributeMap":{"customer_id":"731","level":"info"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304359454,
				'1518282c4475a6d7',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'349380000',
				[
					'net/http.was_idle',
					'client-uuid',
					'host.name',
					'http.status_code',
					'net/http.reused',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
					'component',
					'http.method',
					'http.url',
					'ip',
				],
				[
					'true',
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'200',
					'true',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'net/http',
					'GET',
					'0.0.0.0:8081',
					'**********',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=51a3062c3456cd26, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304359454120000}',
					'{"name":"GotConn","timeUnixNano":1721304359454129000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304359454156000}',
					'{"name":"WroteRequest","timeUnixNano":1721304359454158000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304359803413000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304359803452000}',
					'{"name":"ClosedBody","timeUnixNano":1721304359803472000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359454,
				'51a3062c3456cd26',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /customer',
				'0',
				'349435000',
				[
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'ip',
				],
				[
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304359454,
				'67e901d53c8f11d4',
				'000000000000000071dc9b0a338729b4',
				'mysql',
				'SQL SELECT',
				'3',
				'348855000',
				[
					'host.name',
					'peer.service',
					'request',
					'signoz.collector.id',
					'sql.query',
					'client-uuid',
					'ip',
					'opencensus.exporterversion',
					'service.name',
				],
				[
					'4f6ec470feea',
					'mysql',
					'',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'SELECT * FROM customer WHERE customer_id=731',
					'3441c794741337d0',
					'**********',
					'Jaeger-Go-2.30.0',
					'mysql',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=74fff7b42cbc923b, RefType=CHILD_OF}',
				],
				[
					'{"name":"Waiting for lock behind 1 transactions","timeUnixNano":1721304359454335000,"attributeMap":{"blockers":"[]"}}',
					'{"name":"Acquired lock with 0 transactions waiting behind","timeUnixNano":1721304359529107000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359803,
				'541274ec900e06ae',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'FindDriverIDs',
				'3',
				'15030000',
				[
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'param.location',
				],
				[
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'728,326',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[
					'{"name":"Found drivers","timeUnixNano":1721304359818819000,"attributeMap":{"level":"info"}}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359818,
				'14a6cad2442f62ae',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'7524000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'param.driverID',
					'service.name',
					'signoz.collector.id',
				],
				[
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'T746480C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359826,
				'49771a34b0287913',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'10207000',
				[
					'param.driverID',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
				],
				[
					'T787177C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359836,
				'53e1096a98361b25',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'32032000',
				[
					'param.driverID',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
				],
				[
					'T718678C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[
					'{"name":"redis timeout","timeUnixNano":1721304359868650000,"attributeMap":{"driver_id":"T718678C","error":"redis timeout","level":"error"}}',
				],
				true,
				'',
				'Error',
				'Client',
			],
			[
				1721304359868,
				'62f28274cd882905',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'9969000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'param.driverID',
					'service.name',
					'signoz.collector.id',
				],
				[
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'T718678C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359878,
				'273c24835b4733f7',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'6974000',
				[
					'opencensus.exporterversion',
					'param.driverID',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'ip',
				],
				[
					'Jaeger-Go-2.30.0',
					'T778818C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359885,
				'1fee566b7d89250d',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'11594000',
				[
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'param.driverID',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
				],
				[
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'T730205C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'1b45eecbdc9fca62',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359897,
				'6b7bfcbede8a2e10',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'9198000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'param.driverID',
					'service.name',
					'signoz.collector.id',
				],
				[
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'T708669C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359906,
				'707b2e18e9f4b36d',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'28706000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'param.driverID',
					'service.name',
					'signoz.collector.id',
				],
				[
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'T740153C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[
					'{"name":"redis timeout","timeUnixNano":1721304359935174000,"attributeMap":{"driver_id":"T740153C","error":"redis timeout","level":"error"}}',
				],
				true,
				'',
				'Error',
				'Client',
			],
			[
				1721304359935,
				'759867234892fa23',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'10115000',
				[
					'ip',
					'opencensus.exporterversion',
					'param.driverID',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
				],
				[
					'**********',
					'Jaeger-Go-2.30.0',
					'T740153C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'1b45eecbdc9fca62',
					'4f6ec470feea',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359945,
				'40e021cf08a5849d',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'13283000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'param.driverID',
					'service.name',
					'signoz.collector.id',
				],
				[
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'T739323C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359958,
				'53a1db7583207833',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'7573000',
				[
					'opencensus.exporterversion',
					'param.driverID',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'ip',
				],
				[
					'Jaeger-Go-2.30.0',
					'T731135C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359966,
				'46aaaa6f2a2fc84f',
				'000000000000000071dc9b0a338729b4',
				'redis',
				'GetDriver',
				'3',
				'9238000',
				[
					'param.driverID',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
				],
				[
					'T760263C',
					'redis',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'1b45eecbdc9fca62',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=22eadd8a0f27a552, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359803,
				'22eadd8a0f27a552',
				'000000000000000071dc9b0a338729b4',
				'driver',
				'/driver.DriverService/FindNearest',
				'2',
				'172564000',
				[
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'component',
					'host.name',
					'ip',
				],
				[
					'Jaeger-Go-2.30.0',
					'driver',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'46ade24719771fcb',
					'gRPC',
					'4f6ec470feea',
					'**********',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=0b67082081caa21d, RefType=CHILD_OF}',
				],
				[
					'{"name":"Searching for nearby drivers","timeUnixNano":1721304359803853000,"attributeMap":{"level":"info","location":"728,326"}}',
					'{"name":"Retrying GetDriver after error","timeUnixNano":1721304359868728000,"attributeMap":{"error":"redis timeout","level":"error","retry_no":"1"}}',
					'{"name":"Retrying GetDriver after error","timeUnixNano":1721304359935265000,"attributeMap":{"error":"redis timeout","level":"error","retry_no":"1"}}',
					'{"name":"Search successful","timeUnixNano":1721304359975617000,"attributeMap":{"level":"info","num_drivers":"10"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304359803,
				'0b67082081caa21d',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'/driver.DriverService/FindNearest',
				'3',
				'173105000',
				[
					'component',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
				],
				[
					'gRPC',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'6fb81b8ca91b2b4d',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359978,
				'3ed7b128c9d765bf',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'54072000',
				[
					'component',
					'http.method',
					'http.status_code',
					'http.url',
					'net/http.reused',
					'net/http.was_idle',
					'service.name',
					'client-uuid',
					'signoz.collector.id',
					'ip',
					'opencensus.exporterversion',
					'host.name',
				],
				[
					'net/http',
					'GET',
					'200',
					'0.0.0.0:8083',
					'true',
					'true',
					'frontend',
					'6fb81b8ca91b2b4d',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'**********',
					'Jaeger-Go-2.30.0',
					'4f6ec470feea',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=0ac9396a0836beef, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304359978461000}',
					'{"name":"GotConn","timeUnixNano":1721304359978470000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304359978490000}',
					'{"name":"WroteRequest","timeUnixNano":1721304359978492000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360032438000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360032487000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360032511000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359978,
				'0ac9396a0836beef',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'54081000',
				[
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
				],
				[
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304359978,
				'2b837048c74fc169',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'64945000',
				[
					'ip',
					'service.name',
					'client-uuid',
					'component',
					'http.method',
					'http.status_code',
					'opencensus.exporterversion',
					'signoz.collector.id',
					'host.name',
					'http.url',
					'net/http.reused',
					'net/http.was_idle',
				],
				[
					'**********',
					'frontend',
					'6fb81b8ca91b2b4d',
					'net/http',
					'GET',
					'200',
					'Jaeger-Go-2.30.0',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'4f6ec470feea',
					'0.0.0.0:8083',
					'false',
					'false',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=1e320f1c93026e9d, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304359979001000}',
					'{"name":"ConnectStart","timeUnixNano":1721304359979065000,"attributeMap":{"addr":"0.0.0.0:8083","network":"tcp"}}',
					'{"name":"ConnectDone","timeUnixNano":1721304359979262000,"attributeMap":{"addr":"0.0.0.0:8083","network":"tcp"}}',
					'{"name":"GotConn","timeUnixNano":1721304359979287000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304359979341000}',
					'{"name":"WroteRequest","timeUnixNano":1721304359979342000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360043858000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360043897000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360043919000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359978,
				'1e320f1c93026e9d',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'64954000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
				],
				[
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304359977,
				'3d559df27da11f0f',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'76137000',
				[
					'net/http.reused',
					'net/http.was_idle',
					'component',
					'host.name',
					'http.url',
					'ip',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'http.method',
					'http.status_code',
					'opencensus.exporterversion',
				],
				[
					'true',
					'true',
					'net/http',
					'4f6ec470feea',
					'0.0.0.0:8083',
					'**********',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'6fb81b8ca91b2b4d',
					'GET',
					'200',
					'Jaeger-Go-2.30.0',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=6acf3a3fd51f294a, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304359977905000}',
					'{"name":"GotConn","timeUnixNano":1721304359977914000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304359977939000}',
					'{"name":"WroteRequest","timeUnixNano":1721304359977941000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360053941000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360053982000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360054009000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304359977,
				'6acf3a3fd51f294a',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'76161000',
				[
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
				],
				[
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'6fb81b8ca91b2b4d',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304360033,
				'4f50f3f099502699',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'69683000',
				[
					'client-uuid',
					'component',
					'http.status_code',
					'http.url',
					'net/http.reused',
					'net/http.was_idle',
					'signoz.collector.id',
					'host.name',
					'http.method',
					'ip',
					'opencensus.exporterversion',
					'service.name',
				],
				[
					'6fb81b8ca91b2b4d',
					'net/http',
					'200',
					'0.0.0.0:8083',
					'true',
					'true',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'4f6ec470feea',
					'GET',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=0f064e2af29e5e3d, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304360033336000}',
					'{"name":"GotConn","timeUnixNano":1721304360033345000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304360033369000}',
					'{"name":"WroteRequest","timeUnixNano":1721304360033372000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360102925000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360102965000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360102988000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304360033,
				'0f064e2af29e5e3d',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'69697000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
				],
				[
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304360054,
				'1968d4e502b91ac4',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'54187000',
				[
					'client-uuid',
					'component',
					'http.status_code',
					'net/http.was_idle',
					'opencensus.exporterversion',
					'signoz.collector.id',
					'host.name',
					'http.method',
					'http.url',
					'ip',
					'net/http.reused',
					'service.name',
				],
				[
					'6fb81b8ca91b2b4d',
					'net/http',
					'200',
					'true',
					'Jaeger-Go-2.30.0',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'4f6ec470feea',
					'GET',
					'0.0.0.0:8083',
					'**********',
					'true',
					'frontend',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=4984e1953fd99760, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304360054823000}',
					'{"name":"GotConn","timeUnixNano":1721304360054831000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304360054855000}',
					'{"name":"WroteRequest","timeUnixNano":1721304360054857000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360108921000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360108961000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360108984000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304360054,
				'4984e1953fd99760',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'54200000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
				],
				[
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304360044,
				'032263185bbbf38c',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'69252000',
				[
					'host.name',
					'ip',
					'net/http.was_idle',
					'service.name',
					'client-uuid',
					'component',
					'http.method',
					'http.status_code',
					'http.url',
					'net/http.reused',
					'opencensus.exporterversion',
					'signoz.collector.id',
				],
				[
					'4f6ec470feea',
					'**********',
					'true',
					'frontend',
					'6fb81b8ca91b2b4d',
					'net/http',
					'GET',
					'200',
					'0.0.0.0:8083',
					'true',
					'Jaeger-Go-2.30.0',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=54965682749b13bf, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304360044759000}',
					'{"name":"GotConn","timeUnixNano":1721304360044768000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304360044793000}',
					'{"name":"WroteRequest","timeUnixNano":1721304360044796000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360113908000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360113948000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360113971000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304360044,
				'54965682749b13bf',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'69266000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
				],
				[
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304360109,
				'255866d4d4b6ec08',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'41317000',
				[
					'component',
					'host.name',
					'http.method',
					'http.status_code',
					'net/http.reused',
					'net/http.was_idle',
					'opencensus.exporterversion',
					'client-uuid',
					'http.url',
					'ip',
					'service.name',
					'signoz.collector.id',
				],
				[
					'net/http',
					'4f6ec470feea',
					'GET',
					'200',
					'true',
					'true',
					'Jaeger-Go-2.30.0',
					'6fb81b8ca91b2b4d',
					'0.0.0.0:8083',
					'**********',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=690f0b793f302403, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304360109790000}',
					'{"name":"GotConn","timeUnixNano":1721304360109798000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304360109823000}',
					'{"name":"WroteRequest","timeUnixNano":1721304360109826000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360151018000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360151059000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360151081000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304360109,
				'690f0b793f302403',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'41332000',
				[
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
				],
				[
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304360103,
				'26fbd8f91831c199',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'48308000',
				[
					'http.method',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'component',
					'http.url',
					'net/http.reused',
					'net/http.was_idle',
					'host.name',
					'http.status_code',
				],
				[
					'GET',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'6fb81b8ca91b2b4d',
					'net/http',
					'0.0.0.0:8083',
					'true',
					'true',
					'4f6ec470feea',
					'200',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=55e1875d184a70fe, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304360103810000}',
					'{"name":"GotConn","timeUnixNano":1721304360103818000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304360103842000}',
					'{"name":"WroteRequest","timeUnixNano":1721304360103844000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360152042000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360152070000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360152091000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304360103,
				'55e1875d184a70fe',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'48325000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
				],
				[
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304360114,
				'2903e6c8cbe5f8c6',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'65640000',
				[
					'http.status_code',
					'ip',
					'signoz.collector.id',
					'client-uuid',
					'component',
					'http.method',
					'net/http.was_idle',
					'opencensus.exporterversion',
					'service.name',
					'host.name',
					'http.url',
					'net/http.reused',
				],
				[
					'200',
					'**********',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'6fb81b8ca91b2b4d',
					'net/http',
					'GET',
					'true',
					'Jaeger-Go-2.30.0',
					'frontend',
					'4f6ec470feea',
					'0.0.0.0:8083',
					'true',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=5bcb6dfccf311afe, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304360114775000}',
					'{"name":"GotConn","timeUnixNano":1721304360114783000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304360114809000}',
					'{"name":"WroteRequest","timeUnixNano":1721304360114812000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360180281000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360180362000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360180389000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304360114,
				'5bcb6dfccf311afe',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'65664000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
				],
				[
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304360151,
				'3ed0b66560a03e14',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET',
				'3',
				'78546000',
				[
					'http.status_code',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'component',
					'host.name',
					'http.method',
					'http.url',
					'ip',
					'net/http.reused',
					'net/http.was_idle',
					'opencensus.exporterversion',
				],
				[
					'200',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'6fb81b8ca91b2b4d',
					'net/http',
					'4f6ec470feea',
					'GET',
					'0.0.0.0:8083',
					'**********',
					'true',
					'true',
					'Jaeger-Go-2.30.0',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=352ac0b61c8c826f, RefType=CHILD_OF}',
				],
				[
					'{"name":"GetConn","timeUnixNano":1721304360151933000}',
					'{"name":"GotConn","timeUnixNano":1721304360151942000}',
					'{"name":"WroteHeaders","timeUnixNano":1721304360151967000}',
					'{"name":"WroteRequest","timeUnixNano":1721304360151969000}',
					'{"name":"GotFirstResponseByte","timeUnixNano":1721304360230321000}',
					'{"name":"PutIdleConn","timeUnixNano":1721304360230365000}',
					'{"name":"ClosedBody","timeUnixNano":1721304360230449000}',
				],
				false,
				'',
				'Unset',
				'Client',
			],
			[
				1721304360151,
				'352ac0b61c8c826f',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET: /route',
				'0',
				'78561000',
				[
					'client-uuid',
					'host.name',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
				],
				[
					'6fb81b8ca91b2b4d',
					'4f6ec470feea',
					'**********',
					'Jaeger-Go-2.30.0',
					'frontend',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=71dc9b0a338729b4, RefType=CHILD_OF}',
				],
				[],
				false,
				'',
				'Unset',
				'Unspecified',
			],
			[
				1721304359453,
				'71dc9b0a338729b4',
				'000000000000000071dc9b0a338729b4',
				'frontend',
				'HTTP GET /dispatch',
				'2',
				'776756000',
				[
					'http.status_code',
					'http.url',
					'opencensus.exporterversion',
					'sampler.param',
					'client-uuid',
					'component',
					'host.name',
					'http.method',
					'signoz.collector.id',
					'ip',
					'sampler.type',
					'service.name',
				],
				[
					'200',
					'/dispatch?customer=731\u0026nonse=0.8022286220408668',
					'Jaeger-Go-2.30.0',
					'true',
					'6fb81b8ca91b2b4d',
					'net/http',
					'4f6ec470feea',
					'GET',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'**********',
					'const',
					'frontend',
				],
				['{TraceId=000000000000000071dc9b0a338729b4, SpanId=, RefType=CHILD_OF}'],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304359453904000,"attributeMap":{"level":"info","method":"GET","url":"/dispatch?customer=731\\u0026nonse=0.8022286220408668"}}',
					'{"name":"Getting customer","timeUnixNano":1721304359453990000,"attributeMap":{"customer_id":"731","level":"info"}}',
					'{"name":"Found customer","timeUnixNano":1721304359803486000,"attributeMap":{"level":"info"}}',
					'{"name":"baggage","timeUnixNano":1721304359803546000,"attributeMap":{"key":"customer","value":"Japanese Desserts"}}',
					'{"name":"Finding nearest drivers","timeUnixNano":1721304359803551000,"attributeMap":{"level":"info","location":"728,326"}}',
					'{"name":"Found drivers","timeUnixNano":1721304359976695000,"attributeMap":{"level":"info"}}',
					'{"name":"Finding route","timeUnixNano":1721304359977409000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"165,543"}}',
					'{"name":"Finding route","timeUnixNano":1721304359977989000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"720,72"}}',
					'{"name":"Finding route","timeUnixNano":1721304359978530000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"530,911"}}',
					'{"name":"Finding route","timeUnixNano":1721304360032532000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"686,946"}}',
					'{"name":"Finding route","timeUnixNano":1721304360043941000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"222,675"}}',
					'{"name":"Finding route","timeUnixNano":1721304360054028000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"660,997"}}',
					'{"name":"Finding route","timeUnixNano":1721304360103005000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"609,233"}}',
					'{"name":"Finding route","timeUnixNano":1721304360109005000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"516,970"}}',
					'{"name":"Finding route","timeUnixNano":1721304360113986000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"524,763"}}',
					'{"name":"Finding route","timeUnixNano":1721304360151099000,"attributeMap":{"dropoff":"728,326","level":"info","pickup":"267,822"}}',
					'{"name":"Found routes","timeUnixNano":1721304360230473000,"attributeMap":{"level":"info"}}',
					'{"name":"Dispatch successful","timeUnixNano":1721304360230582000,"attributeMap":{"driver":"T718678C","eta":"2m0s","level":"info"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304359980,
				'472ffaa577ce6b0e',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'52268000',
				[
					'component',
					'host.name',
					'http.url',
					'opencensus.exporterversion',
					'service.name',
					'client-uuid',
					'http.method',
					'http.status_code',
					'ip',
					'signoz.collector.id',
				],
				[
					'net/http',
					'4f6ec470feea',
					'/route?dropoff=728%2C326\u0026pickup=720%2C72',
					'Jaeger-Go-2.30.0',
					'route',
					'64a18ffd5f8adbfb',
					'GET',
					'200',
					'**********',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=3ed7b128c9d765bf, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304359980063000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=720%2C72"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304359979,
				'2a835737138b4671',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'64209000',
				[
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'component',
					'host.name',
					'http.url',
					'opencensus.exporterversion',
					'http.method',
					'http.status_code',
					'ip',
				],
				[
					'route',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'64a18ffd5f8adbfb',
					'net/http',
					'4f6ec470feea',
					'/route?dropoff=728%2C326\u0026pickup=530%2C911',
					'Jaeger-Go-2.30.0',
					'GET',
					'200',
					'**********',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=2b837048c74fc169, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304359979511000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=530%2C911"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304359980,
				'28a8a67365d0bd8b',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'73145000',
				[
					'http.method',
					'http.status_code',
					'opencensus.exporterversion',
					'signoz.collector.id',
					'component',
					'host.name',
					'ip',
					'service.name',
					'client-uuid',
					'http.url',
				],
				[
					'GET',
					'200',
					'Jaeger-Go-2.30.0',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'net/http',
					'4f6ec470feea',
					'**********',
					'route',
					'64a18ffd5f8adbfb',
					'/route?dropoff=728%2C326\u0026pickup=165%2C543',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=3d559df27da11f0f, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304359980599000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=165%2C543"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304360033,
				'38de18f04671e5db',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'69230000',
				[
					'http.status_code',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'component',
					'http.method',
					'host.name',
					'http.url',
				],
				[
					'200',
					'**********',
					'Jaeger-Go-2.30.0',
					'route',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'64a18ffd5f8adbfb',
					'net/http',
					'GET',
					'4f6ec470feea',
					'/route?dropoff=728%2C326\u0026pickup=686%2C946',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=4f50f3f099502699, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304360033555000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=686%2C946"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304360054,
				'3420998a476237ce',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'53811000',
				[
					'client-uuid',
					'host.name',
					'http.status_code',
					'http.url',
					'opencensus.exporterversion',
					'service.name',
					'component',
					'http.method',
					'ip',
					'signoz.collector.id',
				],
				[
					'64a18ffd5f8adbfb',
					'4f6ec470feea',
					'200',
					'/route?dropoff=728%2C326\u0026pickup=660%2C997',
					'Jaeger-Go-2.30.0',
					'route',
					'net/http',
					'GET',
					'**********',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=1968d4e502b91ac4, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304360054989000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=660%2C997"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304360044,
				'0c5a7edd2e365047',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'68850000',
				[
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'ip',
					'http.method',
					'http.status_code',
					'http.url',
					'component',
					'host.name',
				],
				[
					'Jaeger-Go-2.30.0',
					'route',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'64a18ffd5f8adbfb',
					'**********',
					'GET',
					'200',
					'/route?dropoff=728%2C326\u0026pickup=222%2C675',
					'net/http',
					'4f6ec470feea',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=032263185bbbf38c, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304360044934000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=222%2C675"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304360103,
				'538b56106b402474',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'46813000',
				[
					'http.url',
					'opencensus.exporterversion',
					'client-uuid',
					'component',
					'host.name',
					'http.method',
					'http.status_code',
					'ip',
					'service.name',
					'signoz.collector.id',
				],
				[
					'/route?dropoff=728%2C326\u0026pickup=609%2C233',
					'Jaeger-Go-2.30.0',
					'64a18ffd5f8adbfb',
					'net/http',
					'4f6ec470feea',
					'GET',
					'200',
					'**********',
					'route',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=26fbd8f91831c199, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304360103976000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=609%2C233"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304360109,
				'668e5fc06e78796b',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'41012000',
				[
					'component',
					'http.method',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
					'host.name',
					'http.status_code',
					'http.url',
					'ip',
					'opencensus.exporterversion',
				],
				[
					'net/http',
					'GET',
					'route',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'64a18ffd5f8adbfb',
					'4f6ec470feea',
					'200',
					'/route?dropoff=728%2C326\u0026pickup=516%2C970',
					'**********',
					'Jaeger-Go-2.30.0',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=255866d4d4b6ec08, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304360109961000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=516%2C970"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304360114,
				'7eda7467eedf3b92',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'65157000',
				[
					'host.name',
					'ip',
					'service.name',
					'http.status_code',
					'http.url',
					'opencensus.exporterversion',
					'signoz.collector.id',
					'client-uuid',
					'component',
					'http.method',
				],
				[
					'4f6ec470feea',
					'**********',
					'route',
					'200',
					'/route?dropoff=728%2C326\u0026pickup=524%2C763',
					'Jaeger-Go-2.30.0',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'64a18ffd5f8adbfb',
					'net/http',
					'GET',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=2903e6c8cbe5f8c6, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304360114939000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=524%2C763"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
			[
				1721304360152,
				'231d5099ef1d826f',
				'000000000000000071dc9b0a338729b4',
				'route',
				'HTTP GET /route',
				'2',
				'77985000',
				[
					'host.name',
					'http.method',
					'http.status_code',
					'component',
					'http.url',
					'ip',
					'opencensus.exporterversion',
					'service.name',
					'signoz.collector.id',
					'client-uuid',
				],
				[
					'4f6ec470feea',
					'GET',
					'200',
					'net/http',
					'/route?dropoff=728%2C326\u0026pickup=267%2C822',
					'**********',
					'Jaeger-Go-2.30.0',
					'route',
					'70d440fb-4875-4371-9a13-1bf5117c99e7',
					'64a18ffd5f8adbfb',
				],
				[
					'{TraceId=000000000000000071dc9b0a338729b4, SpanId=3ed0b66560a03e14, RefType=CHILD_OF}',
				],
				[
					'{"name":"HTTP request received","timeUnixNano":1721304360152182000,"attributeMap":{"level":"info","method":"GET","url":"/route?dropoff=728%2C326\\u0026pickup=267%2C822"}}',
				],
				false,
				'',
				'Unset',
				'Server',
			],
		],
		isSubTree: false,
	},
];
