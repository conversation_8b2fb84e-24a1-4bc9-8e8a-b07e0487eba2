# Please adjust to your needs (see https://www.gitpod.io/docs/config-gitpod-file)
# and commit this file to your remote git repository to share the goodness with others.


tasks:
  - name: Run Docker Images
    init: |
      cd ./deploy/docker
      sudo docker compose up -d

  - name: Run Frontend
    init: |
      cd ./frontend
      yarn install
    command:
      yarn dev

ports:
  - port: 8080
    onOpen: open-browser
  - port: 9000
    onOpen: ignore
  - port: 8123
    onOpen: ignore
  - port: 8089
    onOpen: ignore
  - port: 9093
    onOpen: ignore
