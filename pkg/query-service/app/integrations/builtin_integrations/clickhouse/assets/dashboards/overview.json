{"id": "clickhouse-overview", "description": "This dashboard provides a high-level overview of your Clickhouse Server hosts.", "layout": [{"h": 3, "i": "3b96004b-f356-4fc9-a7de-36af5eabad5d", "moved": false, "static": false, "w": 6, "x": 0, "y": 9}, {"h": 3, "i": "be48f124-96d1-4327-ae10-6f2c1b81fe50", "moved": false, "static": false, "w": 6, "x": 6, "y": 12}, {"h": 3, "i": "8eb1e295-d5e8-4007-acb1-a9ec385d6f4d", "moved": false, "static": false, "w": 6, "x": 0, "y": 12}, {"h": 3, "i": "7f36d404-8915-4bfb-ac93-b69a9ea1428a", "moved": false, "static": false, "w": 6, "x": 6, "y": 6}, {"h": 3, "i": "947053c4-b809-4241-9cb2-90e09868c2a9", "moved": false, "static": false, "w": 6, "x": 6, "y": 3}, {"h": 3, "i": "653248f0-9658-4be3-ba03-017d804e90c8", "moved": false, "static": false, "w": 6, "x": 6, "y": 0}, {"h": 3, "i": "a62fdaa8-9193-46c1-a951-8e3d5a6b1cf9", "moved": false, "static": false, "w": 6, "x": 0, "y": 0}, {"h": 3, "i": "1a1a3a6a-83d2-49db-aa4d-d6f166cc65f0", "moved": false, "static": false, "w": 6, "x": 0, "y": 6}, {"h": 3, "i": "97a2c7bb-f135-412b-a006-167dcc1882c6", "moved": false, "static": false, "w": 6, "x": 0, "y": 3}, {"h": 3, "i": "486edfb2-02ce-45a3-ab89-06fce614edcf", "moved": false, "static": false, "w": 6, "x": 6, "y": 15}, {"h": 3, "i": "fcb693b5-6786-49e0-8007-4bc6009c70e2", "moved": false, "static": false, "w": 6, "x": 0, "y": 15}, {"h": 3, "i": "581eb12e-8c92-4a8d-9800-0f72f350cfd1", "moved": false, "static": false, "w": 6, "x": 6, "y": 9}], "tags": [], "title": "Clickhouse Overview", "variables": {"eaf65de3-20bb-4dbc-9792-32a445f7be8f": {"allSelected": false, "customValue": "", "description": "", "id": "eaf65de3-20bb-4dbc-9792-32a445f7be8f", "key": "eaf65de3-20bb-4dbc-9792-32a445f7be8f", "modificationUUID": "2fbb477e-7775-427b-ab14-29b673dd216c", "multiSelect": false, "name": "host_name", "order": 0, "queryValue": "SELECT JSONExtractString(labels, 'host_name') AS host_name\nFROM signoz_metrics.distributed_time_series_v4_1day\nWHERE metric_name = 'ClickHouseMetrics_VersionInteger'\nGROUP BY host_name", "selectedValue": "", "showALLOption": false, "sort": "DISABLED", "textboxValue": "", "type": "QUERY"}}, "uploadedGrafana": false, "version": "v4", "widgets": [{"description": "", "fillSpans": false, "id": "a62fdaa8-9193-46c1-a951-8e3d5a6b1cf9", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_Query--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_Query", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "f39fa24e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "feac81df", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "c8516e54", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_AsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_AsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "fabac011", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "9a78e4be-1f4d-4104-b97b-26ba927b100e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rate of Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "1a1a3a6a-83d2-49db-aa4d-d6f166cc65f0", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "7d653034", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedSelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedSelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "18cd347e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "96af023f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedAsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedAsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "1ec63ffc", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "536cb476-340c-4afb-a5cc-538ebe419f8d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Failed Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "653248f0-9658-4be3-ba03-017d804e90c8", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertedRows--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertedRows", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "a7d9e1d3", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Inserted Rows", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "ee3de02b-cb8b-4ebc-8041-245f10943ca6", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Inserted Rows", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "a62fdaa8-9193-46c1-a951-8e3d5a6b1cf9", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_Query--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_Query", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "f39fa24e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "feac81df", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "c8516e54", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_AsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_AsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "fabac011", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "9a78e4be-1f4d-4104-b97b-26ba927b100e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rate of Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "1a1a3a6a-83d2-49db-aa4d-d6f166cc65f0", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "7d653034", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedSelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedSelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "18cd347e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "96af023f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedAsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedAsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "1ec63ffc", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "536cb476-340c-4afb-a5cc-538ebe419f8d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Failed Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "97a2c7bb-f135-412b-a006-167dcc1882c6", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_QueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_QueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "6fd64e65", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "All Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "ce64d4b6", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "ffcd0e01", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_OtherQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_OtherQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "da05a175", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Other Queries (not select or insert)", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "e1a1e8b1-60c0-40b6-8391-151d79e91325", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Total Query Time", "yAxisUnit": "µs"}, {"description": "Writes rejected with \"Too many parts\" for inserts or \"Too many mutations\" for mutations", "fillSpans": false, "id": "7f36d404-8915-4bfb-ac93-b69a9ea1428a", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_RejectedInserts--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_RejectedInserts", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "8c903ae3", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Rejected Inserts ('Too many parts')", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_RejectedMutations--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_RejectedMutations", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "74059430", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Rejected Mutations ('Too many mutations')", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "a5bd4adb-1610-43a1-a11e-72dc4e76416d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rejected Writes", "yAxisUnit": "none"}, {"description": "", "fillSpans": false, "id": "a62fdaa8-9193-46c1-a951-8e3d5a6b1cf9", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_Query--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_Query", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "f39fa24e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "feac81df", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "c8516e54", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_AsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_AsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "fabac011", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "9a78e4be-1f4d-4104-b97b-26ba927b100e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rate of Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "1a1a3a6a-83d2-49db-aa4d-d6f166cc65f0", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "7d653034", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedSelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedSelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "18cd347e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "96af023f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedAsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedAsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "1ec63ffc", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "536cb476-340c-4afb-a5cc-538ebe419f8d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Failed Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "653248f0-9658-4be3-ba03-017d804e90c8", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertedRows--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertedRows", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "a7d9e1d3", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Inserted Rows", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "ee3de02b-cb8b-4ebc-8041-245f10943ca6", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Inserted Rows", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "a62fdaa8-9193-46c1-a951-8e3d5a6b1cf9", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_Query--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_Query", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "f39fa24e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "feac81df", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "c8516e54", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_AsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_AsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "fabac011", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "9a78e4be-1f4d-4104-b97b-26ba927b100e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rate of Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "1a1a3a6a-83d2-49db-aa4d-d6f166cc65f0", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "7d653034", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedSelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedSelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "18cd347e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "96af023f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedAsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedAsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "1ec63ffc", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "536cb476-340c-4afb-a5cc-538ebe419f8d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Failed Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "97a2c7bb-f135-412b-a006-167dcc1882c6", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_QueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_QueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "6fd64e65", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "All Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "ce64d4b6", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "ffcd0e01", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_OtherQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_OtherQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "da05a175", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Other Queries (not select or insert)", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "e1a1e8b1-60c0-40b6-8391-151d79e91325", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Total Query Time", "yAxisUnit": "µs"}, {"description": "", "fillSpans": false, "id": "8eb1e295-d5e8-4007-acb1-a9ec385d6f4d", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseMetrics_MemoryTracking--float64--Gauge--true", "isColumn": true, "isJSON": false, "key": "ClickHouseMetrics_MemoryTracking", "type": "Gauge"}, "aggregateOperator": "max", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "5b1aee4f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Memory Used", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "avg", "stepInterval": 60, "timeAggregation": "max"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "3bc4df66-32bd-42fe-9744-71a678107ebb", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Memory Usage", "yAxisUnit": "bytes"}, {"description": "", "fillSpans": false, "id": "a62fdaa8-9193-46c1-a951-8e3d5a6b1cf9", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_Query--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_Query", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "f39fa24e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "feac81df", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "c8516e54", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_AsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_AsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "fabac011", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "9a78e4be-1f4d-4104-b97b-26ba927b100e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rate of Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "1a1a3a6a-83d2-49db-aa4d-d6f166cc65f0", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "7d653034", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedSelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedSelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "18cd347e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "96af023f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedAsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedAsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "1ec63ffc", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "536cb476-340c-4afb-a5cc-538ebe419f8d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Failed Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "653248f0-9658-4be3-ba03-017d804e90c8", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertedRows--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertedRows", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "a7d9e1d3", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Inserted Rows", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "ee3de02b-cb8b-4ebc-8041-245f10943ca6", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Inserted Rows", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "a62fdaa8-9193-46c1-a951-8e3d5a6b1cf9", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_Query--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_Query", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "f39fa24e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "feac81df", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "c8516e54", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_AsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_AsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "fabac011", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "9a78e4be-1f4d-4104-b97b-26ba927b100e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rate of Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "1a1a3a6a-83d2-49db-aa4d-d6f166cc65f0", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "7d653034", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedSelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedSelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "18cd347e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "96af023f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedAsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedAsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "1ec63ffc", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "536cb476-340c-4afb-a5cc-538ebe419f8d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Failed Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "97a2c7bb-f135-412b-a006-167dcc1882c6", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_QueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_QueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "6fd64e65", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "All Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "ce64d4b6", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "ffcd0e01", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_OtherQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_OtherQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "da05a175", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Other Queries (not select or insert)", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "e1a1e8b1-60c0-40b6-8391-151d79e91325", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Total Query Time", "yAxisUnit": "µs"}, {"description": "Writes rejected with \"Too many parts\" for inserts or \"Too many mutations\" for mutations", "fillSpans": false, "id": "7f36d404-8915-4bfb-ac93-b69a9ea1428a", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_RejectedInserts--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_RejectedInserts", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "8c903ae3", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Rejected Inserts", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_RejectedMutations--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_RejectedMutations", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "74059430", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Rejected Mutations", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "a5bd4adb-1610-43a1-a11e-72dc4e76416d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rejected Writes", "yAxisUnit": "none"}, {"description": "", "fillSpans": false, "id": "a62fdaa8-9193-46c1-a951-8e3d5a6b1cf9", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_Query--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_Query", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "f39fa24e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "feac81df", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "c8516e54", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_AsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_AsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "fabac011", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "9a78e4be-1f4d-4104-b97b-26ba927b100e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rate of Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "1a1a3a6a-83d2-49db-aa4d-d6f166cc65f0", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "7d653034", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedSelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedSelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "18cd347e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "96af023f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedAsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedAsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "1ec63ffc", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "536cb476-340c-4afb-a5cc-538ebe419f8d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Failed Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "653248f0-9658-4be3-ba03-017d804e90c8", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertedRows--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertedRows", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "a7d9e1d3", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Inserted Rows", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "ee3de02b-cb8b-4ebc-8041-245f10943ca6", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Inserted Rows", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "a62fdaa8-9193-46c1-a951-8e3d5a6b1cf9", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_Query--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_Query", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "f39fa24e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "feac81df", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "c8516e54", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_AsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_AsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "fabac011", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "9a78e4be-1f4d-4104-b97b-26ba927b100e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Rate of Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "1a1a3a6a-83d2-49db-aa4d-d6f166cc65f0", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "7d653034", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedSelectQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedSelectQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "18cd347e", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "96af023f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_FailedAsyncInsertQuery--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_FailedAsyncInsertQuery", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "1ec63ffc", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Async Insert Queries", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "536cb476-340c-4afb-a5cc-538ebe419f8d", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Failed Queries", "yAxisUnit": "cps"}, {"description": "", "fillSpans": false, "id": "97a2c7bb-f135-412b-a006-167dcc1882c6", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_QueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_QueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "6fd64e65", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "All Queries", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_SelectQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_SelectQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "ce64d4b6", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Select Queries", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "C", "filters": {"items": [{"id": "ffcd0e01", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Insert Queries", "limit": null, "orderBy": [], "queryName": "C", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_OtherQueryTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_OtherQueryTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "D", "filters": {"items": [{"id": "da05a175", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Other Queries (not select or insert)", "limit": null, "orderBy": [], "queryName": "D", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "e1a1e8b1-60c0-40b6-8391-151d79e91325", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Total Query Time", "yAxisUnit": "µs"}, {"description": "", "fillSpans": false, "id": "947053c4-b809-4241-9cb2-90e09868c2a9", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_InsertedBytes--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_InsertedBytes", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "e9d17ef7", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Inserted Data", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "781619c1-2da3-464b-b2af-206645d8da71", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Inserted Data", "yAxisUnit": "binBps"}, {"description": "", "fillSpans": false, "id": "486edfb2-02ce-45a3-ab89-06fce614edcf", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_NetworkReceiveBytes--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_NetworkReceiveBytes", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "cce0dcd3", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Received", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_NetworkSendBytes--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_NetworkSendBytes", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "a29a23a0", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "<PERSON><PERSON>", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "57e90fd3-ddc9-44a3-8349-ddcb543c0d6e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Network Traffic", "yAxisUnit": "binBps"}, {"description": "", "fillSpans": false, "id": "fcb693b5-6786-49e0-8007-4bc6009c70e2", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_OSCPUVirtualTimeMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_OSCPUVirtualTimeMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "d2c354fa", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "OSCPUVirtualTimeMicroseconds", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_OSCPUWaitMicroseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_OSCPUWaitMicroseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "f6af7585", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "OSCPUWaitMicroseconds", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "e1dda6e4-1032-4e95-b7ec-fb292e3d6823", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "CPU", "yAxisUnit": "µs"}, {"description": "", "fillSpans": false, "id": "be48f124-96d1-4327-ae10-6f2c1b81fe50", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_OSReadBytes--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_OSReadBytes", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "75a5ec5f", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Read", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}, {"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_OSWriteBytes--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_OSWriteBytes", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "B", "filters": {"items": [{"id": "98263fc3", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Write", "limit": null, "orderBy": [], "queryName": "B", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "d26049ee-35ff-4f30-b359-6f0d00bb8600", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Disk", "yAxisUnit": "binBps"}, {"description": "", "fillSpans": false, "id": "581eb12e-8c92-4a8d-9800-0f72f350cfd1", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_MergesTimeMilliseconds--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_MergesTimeMilliseconds", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "1ac0d1d1", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Total Time Spent on Merges", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "df0e8faf-801a-4e24-a690-10a6d11651e0", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Total Time spent for Background Merges", "yAxisUnit": "ms"}, {"description": "", "fillSpans": false, "id": "3b96004b-f356-4fc9-a7de-36af5eabad5d", "isStacked": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "ClickHouseProfileEvents_Merge--float64--Sum--true", "isColumn": true, "isJSON": false, "key": "ClickHouseProfileEvents_Merge", "type": "Sum"}, "aggregateOperator": "rate", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "61c9d724", "key": {"dataType": "string", "id": "host_name--string--tag--false", "isColumn": false, "isJSON": false, "key": "host_name", "type": "tag"}, "op": "=", "value": "{{.host_name}}"}], "op": "AND"}, "functions": [], "groupBy": [], "having": [], "legend": "Number of merges launched", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "rate"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "cbeb3c56-5a43-467e-b130-b1cb11c6ee4f", "promql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Background Merges Launched", "yAxisUnit": "cps"}], "uuid": "e74aeb83-ac4b-4313-8a97-216b62c8fc59"}