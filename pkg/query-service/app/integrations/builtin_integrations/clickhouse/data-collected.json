{"logs": [{"name": "Timestamp", "path": "timestamp", "type": "timestamp"}, {"name": "Severity Text", "path": "severity_text", "type": "string"}, {"name": "Severity Number", "path": "severity_number", "type": "number"}, {"name": "Thread ID", "path": "attributes.thread_id", "type": "string"}, {"name": "Query ID", "path": "attributes.query_id", "type": "string"}, {"name": "Clickhouse Component", "path": "attributes.clickhouse_component", "type": "string"}], "metrics": [{"description": "Number of queries to be interpreted and potentially executed. Does not include queries that failed to parse or were rejected due to AST size limits, quota limits or limits on the number of simultaneously running queries. May include internal queries initiated by ClickHouse itself. Does not count subqueries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_Query"}, {"description": "Same as Query, but only for SELECT queries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SelectQuery"}, {"description": "Same as Query, but only for INSERT queries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InsertQuery"}, {"description": "Count queries with all subqueries", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_QueriesWithSubqueries"}, {"description": "Count SELECT queries with all subqueries", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SelectQueriesWithSubqueries"}, {"description": "Count INSERT queries with all subqueries", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InsertQueriesWithSubqueries"}, {"description": "Same as InsertQuery, but only for asynchronous INSERT queries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AsyncInsertQuery"}, {"description": "Data size in bytes of asynchronous INSERT queries.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_AsyncInsertBytes"}, {"description": "Number of rows inserted by asynchronous INSERT queries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AsyncInsertRows"}, {"description": "Number of times a duplicate hash id has been found in asynchronous INSERT hash id cache.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AsyncInsertCacheHits"}, {"description": "Number of failed queries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FailedQuery"}, {"description": "Same as <PERSON><PERSON><PERSON><PERSON><PERSON>, but only for SELECT queries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FailedSelectQuery"}, {"description": "Same as <PERSON><PERSON><PERSON><PERSON><PERSON>, but only for INSERT queries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FailedInsertQuery"}, {"description": "Number of failed ASYNC INSERT queries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FailedAsyncInsertQuery"}, {"description": "Total time of all queries.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_QueryTimeMicroseconds"}, {"description": "Total time of SELECT queries.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_SelectQueryTimeMicroseconds"}, {"description": "Total time of INSERT queries.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_InsertQueryTimeMicroseconds"}, {"description": "Total time of queries that are not SELECT or INSERT.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_OtherQueryTimeMicroseconds"}, {"description": "Number of files opened.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FileOpen"}, {"description": "Number of times the 'lseek' function was called.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_Seek"}, {"description": "Number of reads (read/pread) from a file descriptor. Does not include sockets.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadBufferFromFileDescriptorRead"}, {"description": "Number of times the read (read/pread) from a file descriptor have failed.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadBufferFromFileDescriptorReadFailed"}, {"description": "Number of bytes read from file descriptors. If the file is compressed, this will show the compressed data size.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadBufferFromFileDescriptorReadBytes"}, {"description": "Number of writes (write/pwrite) to a file descriptor. Does not include sockets.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_WriteBufferFromFileDescriptorWrite"}, {"description": "Number of times the write (write/pwrite) to a file descriptor have failed.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_WriteBufferFromFileDescriptorWriteFailed"}, {"description": "Number of bytes written to file descriptors. If the file is compressed, this will show compressed data size.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_WriteBufferFromFileDescriptorWriteBytes"}, {"description": "Number of times the F_FULLFSYNC/fsync/fdatasync function was called for files.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FileSync"}, {"description": "Number of times the F_FULLFSYNC/fsync/fdatasync function was called for directories.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DirectorySync"}, {"description": "Total time spent waiting for F_FULLFSYNC/fsync/fdatasync syscall for files.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSyncElapsedMicroseconds"}, {"description": "Total time spent waiting for F_FULLFSYNC/fsync/fdatasync syscall for directories.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_DirectorySyncElapsedMicroseconds"}, {"description": "Number of bytes (the number of bytes before decompression) read from compressed sources (files, network).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadCompressedBytes"}, {"description": "Number of compressed blocks (the blocks of data that are compressed independent of each other) read from compressed sources (files, network).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CompressedReadBufferBlocks"}, {"description": "Number of uncompressed bytes (the number of bytes after decompression) read from compressed sources (files, network).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CompressedReadBufferBytes"}, {"description": "Number of times a block of data has been found in the uncompressed cache (and decompression was avoided).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_UncompressedCacheHits"}, {"description": "Number of times a block of data has not been found in the uncompressed cache (and required decompression).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_UncompressedCacheMisses"}, {"description": "Number of bytes evicted from the uncompressed cache.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_UncompressedCacheWeightLost"}, {"description": "Number of times a file has been found in the MMap cache (for the 'mmap' read_method), so we didn't have to mmap it again.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MMappedFileCacheHits"}, {"description": "Number of times a file has not been found in the MMap cache (for the 'mmap' read_method), so we had to mmap it again.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MMappedFileCacheMisses"}, {"description": "Number of times a file has been found in the opened file cache, so we didn't have to open it again.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_OpenedFileCacheHits"}, {"description": "Number of times a file has been found in the opened file cache, so we had to open it again.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_OpenedFileCacheMisses"}, {"description": "Amount of time spent executing OpenedFileCache methods.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_OpenedFileCacheMicroseconds"}, {"description": "Number of writes with Linux or FreeBSD AIO interface", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AIOWrite"}, {"description": "Number of bytes written with Linux or FreeBSD AIO interface", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AIOWriteBytes"}, {"description": "Number of reads with Linux or FreeBSD AIO interface", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AIORead"}, {"description": "Number of bytes read with Linux or FreeBSD AIO interface", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AIOReadBytes"}, {"description": "Number of allocations of IO buffers (for ReadBuffer/WriteBuffer).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_IOBufferAllocs"}, {"description": "Number of bytes allocated for IO buffers (for ReadBuffer/WriteBuffer).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_IOBufferAllocBytes"}, {"description": "Number of chunks allocated for memory Arena (used for GROUP BY and similar operations)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ArenaAllocChunks"}, {"description": "Number of bytes allocated for memory Arena (used for GROUP BY and similar operations)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ArenaAllocBytes"}, {"description": "Number of SQL ordinary function calls (SQL functions are called on per-block basis, so this number represents the number of blocks).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FunctionExecute"}, {"description": "Number of table function calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_TableFunctionExecute"}, {"description": "Number of times an entry has been found in the mark cache, so we didn't have to load a mark file.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MarkCacheHits"}, {"description": "Number of times an entry has not been found in the mark cache, so we had to load a mark file in memory, which is a costly operation, adding to query latency.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MarkCacheMisses"}, {"description": "Number of times a query result has been found in the query cache (and query computation was avoided). Only updated for SELECT queries with SETTING use_query_cache = 1.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_QueryCacheHits"}, {"description": "Number of times a query result has not been found in the query cache (and required query computation). Only updated for SELECT queries with SETTING use_query_cache = 1.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_QueryCacheMisses"}, {"description": "Number of times ordinary read buffer was created for reading data (while choosing among other read methods).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CreatedReadBufferOrdinary"}, {"description": "Number of times a read buffer with O_DIRECT was created for reading data (while choosing among other read methods).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CreatedReadBufferDirectIO"}, {"description": "Number of times a read buffer with O_DIRECT was attempted to be created for reading data (while choosing among other read methods), but the OS did not allow it (due to lack of filesystem support or other reasons) and we fallen back to the ordinary reading method.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CreatedReadBufferDirectIOFailed"}, {"description": "Number of times a read buffer using 'mmap' was created for reading data (while choosing among other read methods).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CreatedReadBufferMMap"}, {"description": "Number of times a read buffer with 'mmap' was attempted to be created for reading data (while choosing among other read methods), but the OS did not allow it (due to lack of filesystem support or other reasons) and we fallen back to the ordinary reading method.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CreatedReadBufferMMapFailed"}, {"description": "Total time spent waiting for read syscall. This include reads from page cache.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_DiskReadElapsedMicroseconds"}, {"description": "Total time spent waiting for write syscall. This include writes to page cache.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_DiskWriteElapsedMicroseconds"}, {"description": "Total time spent waiting for data to receive or receiving data from network. Only ClickHouse-related network interaction is included, not by 3rd party libraries.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_NetworkReceiveElapsedMicroseconds"}, {"description": "Total time spent waiting for data to send to network or sending data to network. Only ClickHouse-related network interaction is included, not by 3rd party libraries.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_NetworkSendElapsedMicroseconds"}, {"description": "Total number of bytes received from network. Only ClickHouse-related network interaction is included, not by 3rd party libraries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_NetworkReceiveBytes"}, {"description": "Total number of bytes send to network. Only ClickHouse-related network interaction is included, not by 3rd party libraries.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_NetworkSendBytes"}, {"description": "Number of DiskS3 GET and SELECT requests passed through throttler.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3GetRequestThrottlerCount"}, {"description": "Total time a query was sleeping to conform DiskS3 GET and SELECT request throttling.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_DiskS3GetRequestThrottlerSleepMicroseconds"}, {"description": "Number of DiskS3 PUT, COPY, POST and LIST requests passed through throttler.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3PutRequestThrottlerCount"}, {"description": "Total time a query was sleeping to conform DiskS3 PUT, COPY, POST and LIST request throttling.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_DiskS3PutRequestThrottlerSleepMicroseconds"}, {"description": "Number of S3 GET and SELECT requests passed through throttler.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3GetRequestThrottlerCount"}, {"description": "Total time a query was sleeping to conform S3 GET and SELECT request throttling.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3GetRequestThrottlerSleepMicroseconds"}, {"description": "Number of S3 PUT, COPY, POST and LIST requests passed through throttler.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3PutRequestThrottlerCount"}, {"description": "Total time a query was sleeping to conform S3 PUT, COPY, POST and LIST request throttling.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3PutRequestThrottlerSleepMicroseconds"}, {"description": "Bytes passed through 'max_remote_read_network_bandwidth_for_server'/'max_remote_read_network_bandwidth' throttler.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_RemoteReadThrottlerBytes"}, {"description": "Total time a query was sleeping to conform 'max_remote_read_network_bandwidth_for_server'/'max_remote_read_network_bandwidth' throttling.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_RemoteReadThrottlerSleepMicroseconds"}, {"description": "Bytes passed through 'max_remote_write_network_bandwidth_for_server'/'max_remote_write_network_bandwidth' throttler.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_RemoteWriteThrottlerBytes"}, {"description": "Total time a query was sleeping to conform 'max_remote_write_network_bandwidth_for_server'/'max_remote_write_network_bandwidth' throttling.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_RemoteWriteThrottlerSleepMicroseconds"}, {"description": "Bytes passed through 'max_local_read_bandwidth_for_server'/'max_local_read_bandwidth' throttler.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_LocalReadThrottlerBytes"}, {"description": "Total time a query was sleeping to conform 'max_local_read_bandwidth_for_server'/'max_local_read_bandwidth' throttling.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_LocalReadThrottlerSleepMicroseconds"}, {"description": "Bytes passed through 'max_local_write_bandwidth_for_server'/'max_local_write_bandwidth' throttler.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_LocalWriteThrottlerBytes"}, {"description": "Total time a query was sleeping to conform 'max_local_write_bandwidth_for_server'/'max_local_write_bandwidth' throttling.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_LocalWriteThrottlerSleepMicroseconds"}, {"description": "Total time a query was sleeping to conform all throttling settings.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ThrottlerSleepMicroseconds"}, {"description": "Number of times query masking rules was successfully matched.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_QueryMaskingRulesMatch"}, {"description": "Number of times a data part was downloaded from replica of a ReplicatedMergeTree table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicatedPartFetches"}, {"description": "Number of times a data part was failed to download from replica of a ReplicatedMergeTree table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicatedPartFailedFetches"}, {"description": "Number of times a data part was covered by another data part that has been fetched from a replica (so, we have marked a covered data part as obsolete and no longer needed).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ObsoleteReplicatedParts"}, {"description": "Number of times data parts of ReplicatedMergeTree tables were successfully merged.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicatedPartMerges"}, {"description": "Number of times we prefer to download already merged part from replica of ReplicatedMergeTree table instead of performing a merge ourself (usually we prefer doing a merge ourself to save network traffic). This happens when we have not all source parts to perform a merge or when the data part is old enough.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicatedPartFetchesOfMerged"}, {"description": "Number of times data parts of ReplicatedMergeTree tables were successfully mutated.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicatedPartMutations"}, {"description": "Number of times we had to perform advanced search for a data part on replicas or to clarify the need of an existing data part.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicatedPartChecks"}, {"description": "Number of times the advanced search for a data part on replicas did not give result or when unexpected part has been found and moved away.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicatedPartChecksFailed"}, {"description": "Number of times a data part that we wanted doesn't exist on any replica (even on replicas that are offline right now). That data parts are definitely lost. This is normal due to asynchronous replication (if quorum inserts were not enabled), when the replica on which the data part was written was failed and when it became online after fail it doesn't contain that data part.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicatedDataLoss"}, {"description": "For debugging purposes. Number of parts in ZooKeeper that have a covering part, but doesn't exist on disk. Checked on server start.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicatedCoveredPartsInZooKeeperOnStart"}, {"description": "Number of rows INSERTed to all tables.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InsertedRows"}, {"description": "Number of bytes (uncompressed; for columns as they stored in memory) INSERTed to all tables.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InsertedBytes"}, {"description": "Number of times the INSERT of a block to a MergeTree table was throttled due to high number of active data parts for partition.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DelayedInserts"}, {"description": "Number of times the INSERT of a block to a MergeTree table was rejected with 'Too many parts' exception due to high number of active data parts for partition.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RejectedInserts"}, {"description": "Total number of milliseconds spent while the INSERT of a block to a MergeTree table was throttled due to high number of active data parts for partition.", "type": "counter", "unit": "Milliseconds", "name": "ClickHouseProfileEvents_DelayedInsertsMilliseconds"}, {"description": "Number of times the mutation of a MergeTree table was throttled due to high number of unfinished mutations for table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DelayedMutations"}, {"description": "Number of times the mutation of a MergeTree table was rejected with 'Too many mutations' exception due to high number of unfinished mutations for table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RejectedMutations"}, {"description": "Total number of milliseconds spent while the mutation of a MergeTree table was throttled due to high number of unfinished mutations for table.", "type": "counter", "unit": "Milliseconds", "name": "ClickHouseProfileEvents_DelayedMutationsMilliseconds"}, {"description": "Number of times the INSERT of a block to a Distributed table was throttled due to high number of pending bytes.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedDelayedInserts"}, {"description": "Number of times the INSERT of a block to a Distributed table was rejected with 'Too many bytes' exception due to high number of pending bytes.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedRejectedInserts"}, {"description": "Total number of milliseconds spent while the INSERT of a block to a Distributed table was throttled due to high number of pending bytes.", "type": "counter", "unit": "Milliseconds", "name": "ClickHouseProfileEvents_DistributedDelayedInsertsMilliseconds"}, {"description": "Number of times the INSERTed block to a ReplicatedMergeTree table was deduplicated.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DuplicatedInsertedBlocks"}, {"description": "Number of times connection with ZooKeeper has been established.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperInit"}, {"description": "Number of ZooKeeper operations, which include both read and write operations as well as multi-transactions.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperTransactions"}, {"description": "Number of 'list' (get<PERSON><PERSON><PERSON><PERSON>) requests to <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperList"}, {"description": "Number of 'create' requests to <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperCreate"}, {"description": "Number of 'remove' requests to <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperRemove"}, {"description": "Number of 'exists' requests to <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperExists"}, {"description": "Number of 'get' requests to <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperGet"}, {"description": "Number of 'set' requests to <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperSet"}, {"description": "Number of 'multi' requests to <PERSON><PERSON><PERSON><PERSON> (compound transactions).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperMulti"}, {"description": "Number of 'check' requests to <PERSON><PERSON><PERSON><PERSON>. Usually they don't make sense in isolation, only as part of a complex transaction.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperCheck"}, {"description": "Number of 'sync' requests to <PERSON><PERSON><PERSON><PERSON>. These requests are rarely needed or usable.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperSync"}, {"description": "Number of 'reconfig' requests to <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperReconfig"}, {"description": "Number of times connection with ZooKeeper has been closed voluntary.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperClose"}, {"description": "Number of times watch notification has been received from <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperWatchResponse"}, {"description": "Number of exceptions while working with <PERSON><PERSON><PERSON><PERSON> related to the data (no node, bad version or similar).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperUserExceptions"}, {"description": "Number of exceptions while working with ZooKeeper related to network (connection loss or similar).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperHardwareExceptions"}, {"description": "Number of exceptions while working with ZooKeeper other than ZooKeeperUserExceptions and ZooKeeperHardwareExceptions.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperOtherExceptions"}, {"description": "Number of microseconds spent waiting for responses from <PERSON><PERSON><PERSON><PERSON> after creating a request, summed across all the requesting threads.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ZooKeeperWaitMicroseconds"}, {"description": "Number of bytes send over network while communicating with <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperBytesSent"}, {"description": "Number of bytes received over network while communicating with <PERSON><PERSON><PERSON><PERSON>.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ZooKeeperBytesReceived"}, {"description": "Total count of distributed connection attempts.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedConnectionTries"}, {"description": "Total count of successful distributed connections to a usable server (with required table, but maybe stale).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedConnectionUsable"}, {"description": "Total count when distributed connection fails with retry.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedConnectionFailTry"}, {"description": "Number of times we rejected a replica from a distributed query, because it did not contain a table needed for the query.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedConnectionMissingTable"}, {"description": "Number of times we rejected a replica from a distributed query, because some table needed for a query had replication lag higher than the configured threshold.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedConnectionStaleReplica"}, {"description": "Total count when distributed connection fails after all retries finished.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedConnectionFailAtAll"}, {"description": "Total count when timeout for changing replica expired in hedged requests.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_HedgedRequestsChangeReplica"}, {"description": "Total count when sending query to shard was suspended when async_query_sending_for_remote is enabled.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SuspendSendingQueryToShard"}, {"description": "Number of times a compilation of generated LLVM code (to create fused function for complex expressions) was initiated.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CompileFunction"}, {"description": "Number of times a compiled function was executed.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CompiledFunctionExecute"}, {"description": "Total time spent for compilation of expressions to LLVM code.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_CompileExpressionsMicroseconds"}, {"description": "Number of bytes used for expressions compilation.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CompileExpressionsBytes"}, {"description": "Number of shell command executions.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExecuteShellCommand"}, {"description": "Number of compressed bytes written by external processing (sorting/aggragating/joining)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalProcessingCompressedBytesTotal"}, {"description": "Amount of data (uncompressed, before compression) written by external processing (sorting/aggragating/joining)", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_ExternalProcessingUncompressedBytesTotal"}, {"description": "Number of files used by external processing (sorting/aggragating/joining)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalProcessingFilesTotal"}, {"description": "Number of times a temporary file was written to disk for sorting in external memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalSortWritePart"}, {"description": "Number of times temporary files were merged for sorting in external memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalSortMerge"}, {"description": "Number of compressed bytes written for sorting in external memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalSortCompressedBytes"}, {"description": "Amount of data (uncompressed, before compression) written for sorting in external memory.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_ExternalSortUncompressedBytes"}, {"description": "Number of times a temporary file was written to disk for aggregation in external memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalAggregationWritePart"}, {"description": "Number of times temporary files were merged for aggregation in external memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalAggregationMerge"}, {"description": "Number of bytes written to disk for aggregation in external memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalAggregationCompressedBytes"}, {"description": "Amount of data (uncompressed, before compression) written to disk for aggregation in external memory.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_ExternalAggregationUncompressedBytes"}, {"description": "Number of times a temporary file was written to disk for JOIN in external memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalJoinWritePart"}, {"description": "Number of times temporary files were merged for JOIN in external memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalJoinMerge"}, {"description": "Number of compressed bytes written for JOIN in external memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ExternalJoinCompressedBytes"}, {"description": "Amount of data (uncompressed, before compression) written for JOIN in external memory.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_ExternalJoinUncompressedBytes"}, {"description": "Number of reads from a file that were slow. This indicate system overload. Thresholds are controlled by read_backoff_* settings.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SlowRead"}, {"description": "Number of times the number of query processing threads was lowered due to slow reads.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadBackoff"}, {"description": "How many times Replicated table has to deinitialize its state due to session expiration in ZooKeeper. The state is reinitialized every time when ZooKeeper is available again.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReplicaPartialShutdown"}, {"description": "Number of data parts selected to read from a MergeTree table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SelectedParts"}, {"description": "Number of (non-adjacent) ranges in all data parts selected to read from a MergeTree table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SelectedRanges"}, {"description": "Number of marks (index granules) selected to read from a MergeTree table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SelectedMarks"}, {"description": "Number of rows SELECTed from all tables.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SelectedRows"}, {"description": "Number of bytes (uncompressed; for columns as they stored in memory) SELECTed from all tables.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SelectedBytes"}, {"description": "Time spent loading marks", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_WaitMarksLoadMicroseconds"}, {"description": "Number of background tasks for loading marks", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_BackgroundLoadingMarksTasks"}, {"description": "Number of marks loaded (total across columns).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_LoadedMarksCount"}, {"description": "Size of in-memory representations of loaded marks.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_LoadedMarksMemoryBytes"}, {"description": "Number of launched background merges.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_Merge"}, {"description": "Rows read for background merges. This is the number of rows before merge.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergedRows"}, {"description": "Uncompressed bytes (for columns as they stored in memory) that was read for background merges. This is the number before merge.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_MergedUncompressedBytes"}, {"description": "Total time spent for background merges.", "type": "counter", "unit": "Milliseconds", "name": "ClickHouseProfileEvents_MergesTimeMilliseconds"}, {"description": "Number of rows INSERTed to MergeTree tables.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergeTreeDataWriterRows"}, {"description": "Uncompressed bytes (for columns as they stored in memory) INSERTed to MergeTree tables.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_MergeTreeDataWriterUncompressedBytes"}, {"description": "Bytes written to filesystem for data INSERTed to MergeTree tables.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_MergeTreeDataWriterCompressedBytes"}, {"description": "Number of blocks INSERTed to MergeTree tables. Each block forms a data part of level zero.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergeTreeDataWriterBlocks"}, {"description": "Number of blocks INSERTed to MergeTree tables that appeared to be already sorted.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergeTreeDataWriterBlocksAlreadySorted"}, {"description": "Number of parts inserted in Wide format.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InsertedWideParts"}, {"description": "Number of parts inserted in Compact format.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InsertedCompactParts"}, {"description": "Number of parts merged into Wide format.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergedIntoWideParts"}, {"description": "Number of parts merged into Compact format.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergedIntoCompactParts"}, {"description": "Number of rows INSERTed to MergeTree tables projection.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergeTreeDataProjectionWriterRows"}, {"description": "Uncompressed bytes (for columns as they stored in memory) INSERTed to MergeTree tables projection.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_MergeTreeDataProjectionWriterUncompressedBytes"}, {"description": "Bytes written to filesystem for data INSERTed to MergeTree tables projection.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_MergeTreeDataProjectionWriterCompressedBytes"}, {"description": "Number of blocks INSERTed to MergeTree tables projection. Each block forms a data part of level zero.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergeTreeDataProjectionWriterBlocks"}, {"description": "Number of blocks INSERTed to MergeTree tables projection that appeared to be already sorted.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergeTreeDataProjectionWriterBlocksAlreadySorted"}, {"description": "Number of times an error happened while trying to remove ephemeral node. This is not an issue, because our implementation of ZooKeeper library guarantee that the session will expire and the node will be removed.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CannotRemoveEphemeralNode"}, {"description": "Compiled regular expressions. Identical regular expressions compiled just once and cached forever.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RegexpCreated"}, {"description": "Number of times the lock of Context was acquired or tried to acquire. This is global lock.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ContextLock"}, {"description": "Context lock wait time in microseconds", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ContextLockWaitMicroseconds"}, {"description": "Number of times a buffer in a 'Buffer' table was flushed.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_StorageBufferFlush"}, {"description": "Number of times a buffer in the 'Buffer' table has not been able to flush due to error writing in the destination table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_StorageBufferErrorOnFlush"}, {"description": "Number of times a criteria on min thresholds has been reached to flush a buffer in a 'Buffer' table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_StorageBufferPassedAllMinThresholds"}, {"description": "Number of times a criteria on max time threshold has been reached to flush a buffer in a 'Buffer' table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_StorageBufferPassedTimeMaxThreshold"}, {"description": "Number of times a criteria on max rows threshold has been reached to flush a buffer in a 'Buffer' table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_StorageBufferPassedRowsMaxThreshold"}, {"description": "Number of times a criteria on max bytes threshold has been reached to flush a buffer in a 'Buffer' table.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_StorageBufferPassedBytesMaxThreshold"}, {"description": "Number of times background-only flush threshold on time has been reached to flush a buffer in a 'Buffer' table. This is expert-only metric. If you read this and you are not an expert, stop reading.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_StorageBufferPassedTimeFlushThreshold"}, {"description": "Number of times background-only flush threshold on rows has been reached to flush a buffer in a 'Buffer' table. This is expert-only metric. If you read this and you are not an expert, stop reading.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_StorageBufferPassedRowsFlushThreshold"}, {"description": "Number of times background-only flush threshold on bytes has been reached to flush a buffer in a 'Buffer' table. This is expert-only metric. If you read this and you are not an expert, stop reading.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_StorageBufferPassedBytesFlushThreshold"}, {"description": "Time for waiting for <PERSON><PERSON><PERSON> layer during reading.", "type": "counter", "unit": "Milliseconds", "name": "ClickHouseProfileEvents_StorageBufferLayerLockReadersWaitMilliseconds"}, {"description": "Time for waiting free Buffer layer to write to (can be used to tune Buffer layers).", "type": "counter", "unit": "Milliseconds", "name": "ClickHouseProfileEvents_StorageBufferLayerLockWritersWaitMilliseconds"}, {"description": "Number of keys requested from the data source for the dictionaries of 'cache' types.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheKeysRequested"}, {"description": "Number of keys requested from the data source for dictionaries of 'cache' types but not found in the data source.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheKeysRequestedMiss"}, {"description": "Number of keys requested from the data source for dictionaries of 'cache' types and found in the data source.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheKeysRequestedFound"}, {"description": "Number of keys looked up in the dictionaries of 'cache' types and found in the cache but they were obsolete.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheKeysExpired"}, {"description": "Number of keys looked up in the dictionaries of 'cache' types and not found.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheKeysNotFound"}, {"description": "Number of keys looked up in the dictionaries of 'cache' types and found in the cache.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheKeysHit"}, {"description": "Number of nanoseconds spend in querying the external data sources for the dictionaries of 'cache' types.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheRequestTimeNs"}, {"description": "Number of bulk requests to the external data sources for the dictionaries of 'cache' types.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheRequests"}, {"description": "Number of nanoseconds spend in waiting for write lock to update the data for the dictionaries of 'cache' types.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheLockWriteNs"}, {"description": "Number of nanoseconds spend in waiting for read lock to lookup the data for the dictionaries of 'cache' types.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DictCacheLockReadNs"}, {"description": "A timeout has exceeded while waiting for shards during synchronous insertion into a Distributed table (with 'distributed_foreground_insert' = 1)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedSyncInsertionTimeoutExceeded"}, {"description": "Number of failures for asynchronous insertion into a Distributed table (with 'distributed_foreground_insert' = 0)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DistributedAsyncInsertionFailures"}, {"description": "Number of times data after merge is not byte-identical to the data on another replicas. There could be several reasons: 1. Using newer version of compression library after server update. 2. Using another compression method. 3. Non-deterministic compression algorithm (highly unlikely). 4. Non-deterministic merge algorithm due to logical error in code. 5. Data corruption in memory due to bug in code. 6. Data corruption in memory due to hardware issue. 7. Manual modification of source data after server startup. 8. Manual modification of checksums stored in ZooKeeper. 9. Part format related settings like 'enable_mixed_granularity_parts' are different on different replicas. The server successfully detected this situation and will download merged part from the replica to force the byte-identical result.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DataAfterMergeDiffersFromReplica"}, {"description": "Number of times data after mutation is not byte-identical to the data on other replicas. In addition to the reasons described in 'DataAfterMergeDiffersFromReplica', it is also possible due to non-deterministic mutation.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DataAfterMutationDiffersFromReplica"}, {"description": "A polygon has been added to the cache (pool) for the 'pointInPolygon' function.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PolygonsAddedToPool"}, {"description": "The number of bytes for polygons added to the cache (pool) for the 'pointInPolygon' function.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PolygonsInPoolAllocatedBytes"}, {"description": "Number of vectors added to usearch indexes.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_USearchAddCount"}, {"description": "Number of nodes visited when adding vectors to usearch indexes.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_USearchAddVisitedMembers"}, {"description": "Number of times distance was computed when adding vectors to usearch indexes.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_USearchAddComputedDistances"}, {"description": "Number of search operations performed in usearch indexes.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_USearchSearchCount"}, {"description": "Number of nodes visited when searching in usearch indexes.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_USearchSearchVisitedMembers"}, {"description": "Number of times distance was computed when searching usearch indexes.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_USearchSearchComputedDistances"}, {"description": "Number of times a read lock was acquired (in a heavy RWLock).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RWLockAcquiredReadLocks"}, {"description": "Number of times a write lock was acquired (in a heavy RWLock).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RWLockAcquiredWriteLocks"}, {"description": "Total time spent waiting for a read lock to be acquired (in a heavy RWLock).", "type": "counter", "unit": "Milliseconds", "name": "ClickHouseProfileEvents_RWLockReadersWaitMilliseconds"}, {"description": "Total time spent waiting for a write lock to be acquired (in a heavy RWLock).", "type": "counter", "unit": "Milliseconds", "name": "ClickHouseProfileEvents_RWLockWritersWaitMilliseconds"}, {"description": "Total count of errors in DNS resolution", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DNSError"}, {"description": "Total time spent holding data parts lock in MergeTree tables", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_PartsLockHoldMicroseconds"}, {"description": "Total time spent waiting for data parts lock in MergeTree tables", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_PartsLockWaitMicroseconds"}, {"description": "Total (wall clock) time spent in processing (queries and other tasks) threads (note that this is a sum).", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_RealTimeMicroseconds"}, {"description": "Total time spent in processing (queries and other tasks) threads executing CPU instructions in user mode. This includes time CPU pipeline was stalled due to main memory access, cache misses, branch mispredictions, hyper-threading, etc.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_UserTimeMicroseconds"}, {"description": "Total time spent in processing (queries and other tasks) threads executing CPU instructions in OS kernel mode. This is time spent in syscalls, excluding waiting time during blocking syscalls.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_SystemTimeMicroseconds"}, {"description": "Total time spent in waiting for memory to be freed in OvercommitTracker.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_MemoryOvercommitWaitTimeMicroseconds"}, {"description": "Total number of times memory allocator purge was requested", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MemoryAllocatorPurge"}, {"description": "Total number of times memory allocator purge was requested", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_MemoryAllocatorPurgeTimeMicroseconds"}, {"description": "The number of soft page faults in query execution threads. Soft page fault usually means a miss in the memory allocator cache, which requires a new memory mapping from the OS and subsequent allocation of a page of physical memory.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SoftPageFaults"}, {"description": "The number of hard page faults in query execution threads. High values indicate either that you forgot to turn off swap on your server, or eviction of memory pages of the ClickHouse binary during very high memory pressure, or successful usage of the 'mmap' read method for the tables data.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_HardPageFaults"}, {"description": "Total time a thread spent waiting for a result of IO operation, from the OS point of view. This is real IO that doesn't include page cache.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_OSIOWaitMicroseconds"}, {"description": "Total time a thread was ready for execution but waiting to be scheduled by OS, from the OS point of view.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_OSCPUWaitMicroseconds"}, {"description": "CPU time spent seen by OS. Does not include involuntary waits due to virtualization.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_OSCPUVirtualTimeMicroseconds"}, {"description": "Number of bytes read from disks or block devices. Doesn't include bytes read from page cache. May include excessive data due to block size, readahead, etc.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_OSReadBytes"}, {"description": "Number of bytes written to disks or block devices. Doesn't include bytes that are in page cache dirty pages. May not include data that was written by OS asynchronously.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_OSWriteBytes"}, {"description": "Number of bytes read from filesystem, including page cache.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_OSReadChars"}, {"description": "Number of bytes written to filesystem, including page cache.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_OSWriteChars"}, {"description": "Time spent processing requests for marks from replicas", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ParallelReplicasHandleRequestMicroseconds"}, {"description": "Time spent processing replicas announcements", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ParallelReplicasHandleAnnouncementMicroseconds"}, {"description": "Sum across all replicas of how many of scheduled marks were assigned by consistent hash", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ParallelReplicasReadAssignedMarks"}, {"description": "Sum across all replicas of how many unassigned marks were scheduled", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ParallelReplicasReadUnassignedMarks"}, {"description": "Sum across all replicas of how many of scheduled marks were assigned for stealing by consistent hash", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ParallelReplicasReadAssignedForStealingMarks"}, {"description": "Time spent collecting segments meant for stealing by hash", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ParallelReplicasStealingByHashMicroseconds"}, {"description": "Time spent processing data parts", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ParallelReplicasProcessingPartsMicroseconds"}, {"description": "Time spent collecting orphaned segments", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ParallelReplicasStealingLeftoversMicroseconds"}, {"description": "Time spent collecting segments meant by hash", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ParallelReplicasCollectingOwnedSegmentsMicroseconds"}, {"description": "Total cycles. Be wary of what happens during CPU frequency scaling.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfCpuCycles"}, {"description": "Retired instructions. Be careful, these can be affected by various issues, most notably hardware interrupt counts.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfInstructions"}, {"description": "Cache accesses. Usually, this indicates Last Level Cache accesses, but this may vary depending on your CPU. This may include prefetches and coherency messages; again this depends on the design of your CPU.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfCacheReferences"}, {"description": "Cache misses. Usually this indicates Last Level Cache misses; this is intended to be used in conjunction with the PERFCOUNTHWCACHEREFERENCES event to calculate cache miss rates.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfCacheMisses"}, {"description": "Retired branch instructions. Prior to Linux 2.6.35, this used the wrong event on AMD processors.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfBranchInstructions"}, {"description": "Mispredicted branch instructions.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfBranchMisses"}, {"description": "Bus cycles, which can be different from total cycles.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfBusCycles"}, {"description": "Stalled cycles during issue.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfStalledCyclesFrontend"}, {"description": "Stalled cycles during retirement.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfStalledCyclesBackend"}, {"description": "Total cycles; not affected by CPU frequency scaling.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfRefCpuCycles"}, {"description": "The CPU clock, a high-resolution per-CPU timer", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfCpuClock"}, {"description": "A clock count specific to the task that is running", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfTaskClock"}, {"description": "Number of context switches", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfContextSwitches"}, {"description": "Number of times the process has migrated to a new CPU", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfCpuMigrations"}, {"description": "Number of alignment faults. These happen when unaligned memory accesses happen; the kernel can handle these but it reduces performance. This happens only on some architectures (never on x86).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfAlignmentFaults"}, {"description": "Number of emulation faults. The kernel sometimes traps on unimplemented instructions and emulates them for user space. This can negatively impact performance.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfEmulationFaults"}, {"description": "For all events, minimum time that an event was enabled. Used to track event multiplexing influence", "type": "counter", "unit": "Microsecond", "name": "ClickHouseProfileEvents_PerfMinEnabledTime"}, {"description": "Running time for event with minimum enabled time. Used to track the amount of event multiplexing", "type": "counter", "unit": "Microsecond", "name": "ClickHouseProfileEvents_PerfMinEnabledRunningTime"}, {"description": "Data TLB references", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfDataTLBReferences"}, {"description": "Data TLB misses", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfDataTLBMisses"}, {"description": "Instruction TLB references", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfInstructionTLBReferences"}, {"description": "Instruction TLB misses", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfInstructionTLBMisses"}, {"description": "Local NUMA node memory reads", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfLocalMemoryReferences"}, {"description": "Local NUMA node memory read misses", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_PerfLocalMemoryMisses"}, {"description": "Total amount of created HTTP connections (counter increase every time connection is created).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CreatedHTTPConnections"}, {"description": "Number of stack traces dropped by query profiler or signal handler because pipe is full or cannot write to pipe.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CannotWriteToWriteBufferDiscard"}, {"description": "Number of times we drop processing of a query profiler signal due to overrun plus the number of signals that OS has not delivered due to overrun.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_QueryProfilerSignalOverruns"}, {"description": "Number of times we drop processing of a query profiler signal due to too many concurrent query profilers in other threads, which may indicate overload.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_QueryProfilerConcurrencyOverruns"}, {"description": "Number of times QueryProfiler had been run.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_QueryProfilerRuns"}, {"description": "Successfully created log entry to merge parts in ReplicatedMergeTree.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CreatedLogEntryForMerge"}, {"description": "Log entry to merge parts in ReplicatedMergeTree is not created due to concurrent log update by another replica.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_NotCreatedLogEntryForMerge"}, {"description": "Successfully created log entry to mutate parts in ReplicatedMergeTree.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CreatedLogEntryForMutation"}, {"description": "Log entry to mutate parts in ReplicatedMergeTree is not created due to concurrent log update by another replica.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_NotCreatedLogEntryForMutation"}, {"description": "Time of GET and HEAD requests to S3 storage.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3ReadMicroseconds"}, {"description": "Number of GET and HEAD requests to S3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3ReadRequestsCount"}, {"description": "Number of non-throttling errors in GET and HEAD requests to S3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3ReadRequestsErrors"}, {"description": "Number of 429 and 503 errors in GET and HEAD requests to S3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3ReadRequestsThrottling"}, {"description": "Number of redirects in GET and HEAD requests to S3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3ReadRequestsRedirects"}, {"description": "Time of POST, DELETE, PUT and PATCH requests to S3 storage.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3WriteMicroseconds"}, {"description": "Number of POST, DELETE, PUT and PATCH requests to S3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3WriteRequestsCount"}, {"description": "Number of non-throttling errors in POST, DELETE, PUT and PATCH requests to S3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3WriteRequestsErrors"}, {"description": "Number of 429 and 503 errors in POST, DELETE, PUT and PATCH requests to S3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3WriteRequestsThrottling"}, {"description": "Number of redirects in POST, DELETE, PUT and PATCH requests to S3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3WriteRequestsRedirects"}, {"description": "Time of GET and HEAD requests to DiskS3 storage.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_DiskS3ReadMicroseconds"}, {"description": "Number of GET and HEAD requests to DiskS3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3ReadRequestsCount"}, {"description": "Number of non-throttling errors in GET and HEAD requests to DiskS3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3ReadRequestsErrors"}, {"description": "Number of 429 and 503 errors in GET and HEAD requests to DiskS3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3ReadRequestsThrottling"}, {"description": "Number of redirects in GET and HEAD requests to DiskS3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3ReadRequestsRedirects"}, {"description": "Time of POST, DELETE, PUT and PATCH requests to DiskS3 storage.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_DiskS3WriteMicroseconds"}, {"description": "Number of POST, DELETE, PUT and PATCH requests to DiskS3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3WriteRequestsCount"}, {"description": "Number of non-throttling errors in POST, DELETE, PUT and PATCH requests to DiskS3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3WriteRequestsErrors"}, {"description": "Number of 429 and 503 errors in POST, DELETE, PUT and PATCH requests to DiskS3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3WriteRequestsThrottling"}, {"description": "Number of redirects in POST, DELETE, PUT and PATCH requests to DiskS3 storage.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3WriteRequestsRedirects"}, {"description": "Number of S3 API DeleteObject(s) calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3DeleteObjects"}, {"description": "Number of S3 API CopyObject calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3CopyObject"}, {"description": "Number of S3 API ListObjects calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3ListObjects"}, {"description": "Number of S3 API HeadObject calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3HeadObject"}, {"description": "Number of S3 API GetObjectAttributes calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3GetObjectAttributes"}, {"description": "Number of S3 API CreateMultipartUpload calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3CreateMultipartUpload"}, {"description": "Number of S3 API UploadPartCopy calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3UploadPartCopy"}, {"description": "Number of S3 API UploadPart calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3UploadPart"}, {"description": "Number of S3 API AbortMultipartUpload calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3AbortMultipartUpload"}, {"description": "Number of S3 API CompleteMultipartUpload calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3CompleteMultipartUpload"}, {"description": "Number of S3 API PutObject calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3PutObject"}, {"description": "Number of S3 API GetObject calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3GetObject"}, {"description": "Number of Azure blob storage API DeleteObject(s) calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AzureDeleteObjects"}, {"description": "Number of Azure blob storage API ListObjects calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AzureListObjects"}, {"description": "Number of DiskS3 API DeleteObject(s) calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3DeleteObjects"}, {"description": "Number of DiskS3 API CopyObject calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3CopyObject"}, {"description": "Number of DiskS3 API ListObjects calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3ListObjects"}, {"description": "Number of DiskS3 API HeadObject calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3HeadObject"}, {"description": "Number of DiskS3 API GetObjectAttributes calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3GetObjectAttributes"}, {"description": "Number of DiskS3 API CreateMultipartUpload calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3CreateMultipartUpload"}, {"description": "Number of DiskS3 API UploadPartCopy calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3UploadPartCopy"}, {"description": "Number of DiskS3 API UploadPart calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3UploadPart"}, {"description": "Number of DiskS3 API AbortMultipartUpload calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3AbortMultipartUpload"}, {"description": "Number of DiskS3 API CompleteMultipartUpload calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3CompleteMultipartUpload"}, {"description": "Number of DiskS3 API PutObject calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3PutObject"}, {"description": "Number of DiskS3 API GetObject calls.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_DiskS3GetObject"}, {"description": "Number of created S3 clients.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_S3Clients"}, {"description": "Number of S3 clients copies which reuse an existing auth provider from another client.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_TinyS3Clients"}, {"description": "Number of files read in table engines working with files (like File/S3/URL/HDFS).", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_EngineFileLikeReadFiles"}, {"description": "Time spent on reading from S3.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ReadBufferFromS3Microseconds"}, {"description": "Time spent initializing connection to S3.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ReadBufferFromS3InitMicroseconds"}, {"description": "Bytes read from S3.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_ReadBufferFromS3Bytes"}, {"description": "Number of exceptions while reading from S3.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadBufferFromS3RequestsErrors"}, {"description": "Number of HTTP sessions that were reset in ReadBufferFromS3.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadBufferFromS3ResetSessions"}, {"description": "Number of HTTP sessions that were preserved in ReadBufferFromS3.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadBufferFromS3PreservedSessions"}, {"description": "Number of HTTP sessions that were preserved in ReadWriteBufferFromHTTP.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadWriteBufferFromHTTPPreservedSessions"}, {"description": "Time spent on writing to S3.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_WriteBufferFromS3Microseconds"}, {"description": "Bytes written to S3.", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_WriteBufferFromS3Bytes"}, {"description": "Number of exceptions while writing to S3.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_WriteBufferFromS3RequestsErrors"}, {"description": "Time spent on waiting while some of the current requests are done when its number reached the limit defined by s3_max_inflight_parts_for_one_file.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_WriteBufferFromS3WaitInflightLimitMicroseconds"}, {"description": "Number of times when memory limit exceeded for query.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_QueryMemoryLimitExceeded"}, {"description": "Number of times the read from filesystem cache hit the cache.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CachedReadBufferReadFromCacheHits"}, {"description": "Number of times the read from filesystem cache miss the cache.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_CachedReadBufferReadFromCacheMisses"}, {"description": "Time reading from filesystem cache source (from remote filesystem, etc)", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_CachedReadBufferReadFromSourceMicroseconds"}, {"description": "Time reading from filesystem cache", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_CachedReadBufferReadFromCacheMicroseconds"}, {"description": "Bytes read from filesystem cache source (from remote fs, etc)", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_CachedReadBufferReadFromSourceBytes"}, {"description": "Bytes read from filesystem cache", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_CachedReadBufferReadFromCacheBytes"}, {"description": "Bytes written from source (remote fs, etc) to filesystem cache", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_CachedReadBufferCacheWriteBytes"}, {"description": "Time spent writing data into filesystem cache", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_CachedReadBufferCacheWriteMicroseconds"}, {"description": "Prepare buffer time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_CachedReadBufferCreateBufferMicroseconds"}, {"description": "Bytes written from source (remote fs, etc) to filesystem cache", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_CachedWriteBufferCacheWriteBytes"}, {"description": "Time spent writing data into filesystem cache", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_CachedWriteBufferCacheWriteMicroseconds"}, {"description": "Time spent loading filesystem cache metadata", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FilesystemCacheLoadMetadataMicroseconds"}, {"description": "Number of bytes evicted from filesystem cache", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FilesystemCacheEvictedBytes"}, {"description": "Number of file segments evicted from filesystem cache", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FilesystemCacheEvictedFileSegments"}, {"description": "Number of file segments skipped for eviction because of being unreleasable", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FilesystemCacheEvictionSkippedFileSegments"}, {"description": "Number of filesystem cache eviction attempts", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FilesystemCacheEvictionTries"}, {"description": "Lock cache key time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FilesystemCacheLockKeyMicroseconds"}, {"description": "Lock filesystem cache metadata time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FilesystemCacheLockMetadataMicroseconds"}, {"description": "Lock filesystem cache time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FilesystemCacheLockCacheMicroseconds"}, {"description": "Filesystem cache space reservation time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FilesystemCacheReserveMicroseconds"}, {"description": "Filesystem cache eviction time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FilesystemCacheEvictMicroseconds"}, {"description": "Filesystem cache getOrSet() time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FilesystemCacheGetOrSetMicroseconds"}, {"description": "Filesystem cache get() time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FilesystemCacheGetMicroseconds"}, {"description": "Wait on DOWNLOADING state", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentWaitMicroseconds"}, {"description": "Duration of FileSegment::complete() in filesystem cache", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentCompleteMicroseconds"}, {"description": "Lock file segment time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentLockMicroseconds"}, {"description": "File segment write() time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentWriteMicroseconds"}, {"description": "File segment use() time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentUseMicroseconds"}, {"description": "File segment remove() time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentRemoveMicroseconds"}, {"description": "File segments holder complete() time", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentHolderCompleteMicroseconds"}, {"description": "Filesystem cache file segments count, which were hold", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FilesystemCacheHoldFileSegments"}, {"description": "Filesystem cache file segments count, which were hold, but not used (because of seek or LIMIT n, etc)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_FilesystemCacheUnusedHoldFileSegments"}, {"description": "Total number of seeks for async buffer", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSSeeks"}, {"description": "Number of prefetches made with asynchronous reading from remote filesystem", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSPrefetches"}, {"description": "Number of cancelled prefecthes (because of seek)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSCancelledPrefetches"}, {"description": "Number of prefetches pending at buffer destruction", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSUnusedPrefetches"}, {"description": "Number of reads from prefecthed buffer", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSPrefetchedReads"}, {"description": "Number of bytes from prefecthed buffer", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSPrefetchedBytes"}, {"description": "Number of reads from unprefetched buffer", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSUnprefetchedReads"}, {"description": "Number of bytes from unprefetched buffer", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSUnprefetchedBytes"}, {"description": "Number of lazy seeks", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSLazySeeks"}, {"description": "Number of seeks which lead to a new connection", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSSeeksWithReset"}, {"description": "Number of buffers created for asynchronous reading from remote filesystem", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_RemoteFSBuffers"}, {"description": "Time spent preparing tasks in MergeTreePrefetchedReadPool", "type": "counter", "unit": "Microsecond", "name": "ClickHouseProfileEvents_MergeTreePrefetchedReadPoolInit"}, {"description": "Time spend waiting for prefetched reader", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_WaitPrefetchTaskMicroseconds"}, {"description": "Time spent getting the data in asynchronous reading", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ThreadpoolReaderTaskMicroseconds"}, {"description": "Time spent on preparation (e.g. call to reader seek() method)", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ThreadpoolReaderPrepareMicroseconds"}, {"description": "Bytes read from a threadpool task in asynchronous reading", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_ThreadpoolReaderReadBytes"}, {"description": "Bytes read from a threadpool task in asynchronous reading", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_ThreadpoolReaderSubmit"}, {"description": "How many times we haven't scheduled a task on the thread pool and read synchronously instead", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ThreadpoolReaderSubmitReadSynchronously"}, {"description": "How many bytes were read synchronously", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_ThreadpoolReaderSubmitReadSynchronouslyBytes"}, {"description": "How much time we spent reading synchronously", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ThreadpoolReaderSubmitReadSynchronouslyMicroseconds"}, {"description": "How much time we spent checking if content is cached", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ThreadpoolReaderSubmitLookupInCacheMicroseconds"}, {"description": "Number of bytes ignored during asynchronous reading", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AsynchronousReaderIgnoredBytes"}, {"description": "Metric per file segment. Time spend waiting for internal read buffer (includes cache waiting)", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentWaitReadBufferMicroseconds"}, {"description": "Metric per file segment. Time spend reading from file", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentReadMicroseconds"}, {"description": "Metric per file segment. Time spend writing data to cache", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentCacheWriteMicroseconds"}, {"description": "Metric per file segment. Time spent pre-downloading data to cache (pre-downloading - finishing file segment download (after someone who failed to do that) up to the point current thread was requested to do)", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_FileSegmentPredownloadMicroseconds"}, {"description": "Metric per file segment. How many bytes were actually used from current file segment", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_FileSegmentUsedBytes"}, {"description": "Number of seeks which lead to new connection (s3, http)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadBufferSeekCancelConnection"}, {"description": "Number of times a sleep function (sleep, sleepEachRow) has been called.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SleepFunctionCalls"}, {"description": "Time set to sleep in a sleep function (sleep, sleepEachRow).", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_SleepFunctionMicroseconds"}, {"description": "Time spent sleeping in a sleep function (sleep, sleepEachRow).", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_SleepFunctionElapsedMicroseconds"}, {"description": "Number of times the read inside ThreadPoolReader was done from the page cache.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ThreadPoolReaderPageCacheHit"}, {"description": "Number of bytes read inside ThreadPoolReader when it was done from the page cache.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ThreadPoolReaderPageCacheHitBytes"}, {"description": "Time spent reading data from page cache in ThreadPoolReader.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ThreadPoolReaderPageCacheHitElapsedMicroseconds"}, {"description": "Number of times the read inside ThreadPoolReader was not done from page cache and was hand off to thread pool.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ThreadPoolReaderPageCacheMiss"}, {"description": "Number of bytes read inside ThreadPoolReader when read was not done from page cache and was hand off to thread pool.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ThreadPoolReaderPageCacheMissBytes"}, {"description": "Time spent reading data inside the asynchronous job in ThreadPoolReader - when read was not done from the page cache.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ThreadPoolReaderPageCacheMissElapsedMicroseconds"}, {"description": "Time spent in waiting for asynchronous reads in asynchronous local read.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_AsynchronousReadWaitMicroseconds"}, {"description": "Time spent in waiting for synchronous reads in asynchronous local read.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_SynchronousReadWaitMicroseconds"}, {"description": "Time spent in waiting for asynchronous remote reads.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_AsynchronousRemoteReadWaitMicroseconds"}, {"description": "Time spent in waiting for synchronous remote reads.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_SynchronousRemoteReadWaitMicroseconds"}, {"description": "Bytes read from local cache buffer in RemoteReadBufferCache", "type": "counter", "unit": "Bytes", "name": "ClickHouseProfileEvents_ExternalDataSourceLocalCacheReadBytes"}, {"description": "Number of times the main configuration was reloaded.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MainConfigLoads"}, {"description": "How many elements were preallocated in hash tables for aggregation.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AggregationPreallocatedElementsInHashTables"}, {"description": "How many hash tables were inited as two-level for aggregation.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_AggregationHashTablesInitializedAsTwoLevel"}, {"description": "Number of partition revocations (the first stage of consumer group rebalance)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaRebalanceRevocations"}, {"description": "Number of partition assignments (the final stage of consumer group rebalance)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaRebalanceAssignments"}, {"description": "Number of failed consumer group rebalances", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaRebalanceErrors"}, {"description": "Number of Kafka messages polled from librdkafka to ClickHouse", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaMessagesPolled"}, {"description": "Number of Kafka messages already processed by ClickHouse", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaMessagesRead"}, {"description": "Number of Kafka messages ClickHouse failed to parse", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaMessagesFailed"}, {"description": "Number of rows parsed from Kafka messages", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaRowsRead"}, {"description": "Number of parsed rows which were later rejected (due to rebalances / errors or similar reasons). Those rows will be consumed again after the rebalance.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaRowsRejected"}, {"description": "Number of direct selects from Kafka tables since server start", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaDirectReads"}, {"description": "Number of background reads populating materialized views from Kafka since server start", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaBackgroundReads"}, {"description": "Number of successful commits of consumed offsets to Kafka (normally should be the same as KafkaBackgroundReads)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaCommits"}, {"description": "Number of failed commits of consumed offsets to Kafka (usually is a sign of some data duplication)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaCommitFailures"}, {"description": "Number of errors reported by librdkafka during polls", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaConsumerErrors"}, {"description": "Number of writes (inserts) to Kafka tables", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaWrites"}, {"description": "Number of rows inserted into Kafka tables", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaRowsWritten"}, {"description": "Number of explicit flushes to Kafka producer", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaProducerFlushes"}, {"description": "Number of messages produced to Kafka", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaMessagesProduced"}, {"description": "Number of errors during producing the messages to <PERSON><PERSON><PERSON>", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KafkaProducerErrors"}, {"description": "Number of times a read from a scalar subquery was done using the global cache", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ScalarSubqueriesGlobalCacheHit"}, {"description": "Number of times a read from a scalar subquery was done using the local cache", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ScalarSubqueriesLocalCacheHit"}, {"description": "Number of times a read from a scalar subquery was not cached and had to be calculated completely", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ScalarSubqueriesCacheMiss"}, {"description": "Number of times the requested source is found in schema cache", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SchemaInferenceCacheHits"}, {"description": "Number of times the schema is found in schema cache during schema inference", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SchemaInferenceCacheSchemaHits"}, {"description": "Number of times the number of rows is found in schema cache during count from files", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SchemaInferenceCacheNumRowsHits"}, {"description": "Number of times the requested source is not in schema cache", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SchemaInferenceCacheMisses"}, {"description": "Number of times the requested source is in cache but the schema is not in cache during schema inference", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SchemaInferenceCacheSchemaMisses"}, {"description": "Number of times the requested source is in cache but the number of rows is not in cache while count from files", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SchemaInferenceCacheNumRowsMisses"}, {"description": "Number of times a schema from cache was evicted due to overflow", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SchemaInferenceCacheEvictions"}, {"description": "Number of times a schema in cache became invalid due to changes in data", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_SchemaInferenceCacheInvalidations"}, {"description": "Packets sent by keeper server", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperPacketsSent"}, {"description": "Packets received by keeper server", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperPacketsReceived"}, {"description": "Total requests number on keeper server", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperRequestTotal"}, {"description": "Keeper latency", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperLatency"}, {"description": "Number of successful commits", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperCommits"}, {"description": "Number of failed commits", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperCommitsFailed"}, {"description": "Number of snapshots creations", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperSnapshotCreations"}, {"description": "Number of failed snapshot creations", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperSnapshotCreationsFailed"}, {"description": "Number of snapshot applying", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperSnapshotApplys"}, {"description": "Number of failed snapshot applying", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperSnapshotApplysFailed"}, {"description": "Number of snapshot read(serialization)", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperReadSnapshot"}, {"description": "Number of snapshot save", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperSaveSnapshot"}, {"description": "Number of create requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperCreateRequest"}, {"description": "Number of remove requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperRemoveRequest"}, {"description": "Number of set requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperSetRequest"}, {"description": "Number of reconfig requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperReconfigRequest"}, {"description": "Number of check requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperCheckRequest"}, {"description": "Number of multi requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperMultiRequest"}, {"description": "Number of multi read requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperMultiReadRequest"}, {"description": "Number of get requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperGetRequest"}, {"description": "Number of list requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperListRequest"}, {"description": "Number of exists requests", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_KeeperExistsRequest"}, {"description": "Number of times, data processing was cancelled by query complexity limitation with setting '*_overflow_mode' = 'break' and the result is incomplete.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_OverflowBreak"}, {"description": "Number of times, data processing was cancelled by query complexity limitation with setting '*_overflow_mode' = 'throw' and exception was thrown.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_OverflowThrow"}, {"description": "Number of times approximate GROUP BY was in effect: when aggregation was performed only on top of first 'max_rows_to_group_by' unique keys and other keys were ignored due to 'group_by_overflow_mode' = 'any'.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_OverflowAny"}, {"description": "Time spent to set file as processing", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3QueueSetFileProcessingMicroseconds"}, {"description": "Time spent to set file as processed", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3QueueSetFileProcessedMicroseconds"}, {"description": "Time spent to set file as failed", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3QueueSetFileFailedMicroseconds"}, {"description": "Time spent to set file as failed", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3QueueCleanupMaxSetSizeOrTTLMicroseconds"}, {"description": "Time spent to read file data", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3QueuePullMicroseconds"}, {"description": "Time spent to lock local file statuses", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_S3QueueLockLocalFileStatusesMicroseconds"}, {"description": "Time elapsed from starting server to listening to sockets in milliseconds", "type": "counter", "unit": "Milliseconds", "name": "ClickHouseProfileEvents_ServerStartupMilliseconds"}, {"description": "Total number of io_uring SQEs submitted", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_IOUringSQEsSubmitted"}, {"description": "Total number of io_uring SQE resubmits performed", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_IOUringSQEsResubmits"}, {"description": "Total number of successfully completed io_uring CQEs", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_IOUringCQEsCompleted"}, {"description": "Total number of completed io_uring CQEs with failures", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_IOUringCQEsFailed"}, {"description": "Number of backups opened for reading", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_BackupsOpenedForRead"}, {"description": "Number of backups opened for writing", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_BackupsOpenedForWrite"}, {"description": "Time spent reading backup metadata from .backup file", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_BackupReadMetadataMicroseconds"}, {"description": "Time spent writing backup metadata to .backup file", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_BackupWriteMetadataMicroseconds"}, {"description": "Time spent making backup entries", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_BackupEntriesCollectorMicroseconds"}, {"description": "Time spent making backup entries for tables data", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_BackupEntriesCollectorForTablesDataMicroseconds"}, {"description": "Time spent running post tasks after making backup entries", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_BackupEntriesCollectorRunPostTasksMicroseconds"}, {"description": "The number of callbacks requested from the remote server back to the initiator server to choose the read task (for s3Cluster table function and similar). Measured on the initiator server side.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadTaskRequestsReceived"}, {"description": "The number of callbacks requested from the remote server back to the initiator server to choose the read task (for MergeTree tables). Measured on the initiator server side.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergeTreeReadTaskRequestsReceived"}, {"description": "The number of callbacks requested from the remote server back to the initiator server to choose the read task (for s3Cluster table function and similar). Measured on the remote server side.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ReadTaskRequestsSent"}, {"description": "The number of callbacks requested from the remote server back to the initiator server to choose the read task (for MergeTree tables). Measured on the remote server side.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergeTreeReadTaskRequestsSent"}, {"description": "The number of announcements sent from the remote server to the initiator server about the set of data parts (for MergeTree tables). Measured on the remote server side.", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_MergeTreeAllRangesAnnouncementsSent"}, {"description": "Time spent in callbacks requested from the remote server back to the initiator server to choose the read task (for s3Cluster table function and similar). Measured on the remote server side.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ReadTaskRequestsSentElapsedMicroseconds"}, {"description": "Time spent in callbacks requested from the remote server back to the initiator server to choose the read task (for MergeTree tables). Measured on the remote server side.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_MergeTreeReadTaskRequestsSentElapsedMicroseconds"}, {"description": "Time spent in sending the announcement from the remote server to the initiator server about the set of data parts (for MergeTree tables). Measured on the remote server side.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_MergeTreeAllRangesAnnouncementsSentElapsedMicroseconds"}, {"description": "Total time spent waiting for a slot in connection pool.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_ConnectionPoolIsFullMicroseconds"}, {"description": "Total time a query was waiting for async loader jobs.", "type": "counter", "unit": "Microseconds", "name": "ClickHouseProfileEvents_AsyncLoaderWaitMicroseconds"}, {"description": "Number of log messages with level Test", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_LogTest"}, {"description": "Number of log messages with level Trace", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_LogTrace"}, {"description": "Number of log messages with level Debug", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_LogDebug"}, {"description": "Number of log messages with level Info", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_LogInfo"}, {"description": "Number of log messages with level Warning", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_LogWarning"}, {"description": "Number of log messages with level Error", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_LogError"}, {"description": "Number of log messages with level Fatal", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_LogFatal"}, {"description": "Number of bytes sent through HTTP interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfaceHTTPSendBytes"}, {"description": "Number of bytes received through HTTP interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfaceHTTPReceiveBytes"}, {"description": "Number of bytes sent through native interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfaceNativeSendBytes"}, {"description": "Number of bytes received through native interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfaceNativeReceiveBytes"}, {"description": "Number of bytes sent through Prometheus interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfacePrometheusSendBytes"}, {"description": "Number of bytes received through Prometheus interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfacePrometheusReceiveBytes"}, {"description": "Number of bytes sent through interserver interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfaceInterserverSendBytes"}, {"description": "Number of bytes received through interserver interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfaceInterserverReceiveBytes"}, {"description": "Number of bytes sent through MySQL interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfaceMySQLSendBytes"}, {"description": "Number of bytes received through MySQL interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfaceMySQLReceiveBytes"}, {"description": "Number of bytes sent through PostgreSQL interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfacePostgreSQLSendBytes"}, {"description": "Number of bytes received through PostgreSQL interfaces", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_InterfacePostgreSQLReceiveBytes"}, {"description": "Number of replicas used to execute a query with task-based parallel replicas", "type": "counter", "unit": "Number", "name": "ClickHouseProfileEvents_ParallelReplicasUsedCount"}, {"description": "Number of executing queries", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_Query"}, {"description": "Number of executing background merges", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_Merge"}, {"description": "Number of currently executing moves", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_Move"}, {"description": "Number of mutations (ALTER DELETE/UPDATE)", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartMutation"}, {"description": "Number of data parts being fetched from replica", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ReplicatedFetch"}, {"description": "Number of data parts being sent to replicas", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ReplicatedSend"}, {"description": "Number of data parts checking for consistency", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ReplicatedChecks"}, {"description": "Number of active merges and mutations in an associated background pool", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundMergesAndMutationsPoolTask"}, {"description": "Limit on number of active merges and mutations in an associated background pool", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundMergesAndMutationsPoolSize"}, {"description": "Number of active fetches in an associated background pool", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundFetchesPoolTask"}, {"description": "Limit on number of simultaneous fetches in an associated background pool", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundFetchesPoolSize"}, {"description": "Number of active tasks in an associated background pool", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundCommonPoolTask"}, {"description": "Limit on number of tasks in an associated background pool", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundCommonPoolSize"}, {"description": "Number of active tasks in BackgroundProcessingPool for moves", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundMovePoolTask"}, {"description": "Limit on number of tasks in BackgroundProcessingPool for moves", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundMovePoolSize"}, {"description": "Number of active tasks in BackgroundSchedulePool. This pool is used for periodic ReplicatedMergeTree tasks, like cleaning old data parts, altering data parts, replica re-initialization, etc.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundSchedulePoolTask"}, {"description": "Limit on number of tasks in BackgroundSchedulePool. This pool is used for periodic ReplicatedMergeTree tasks, like cleaning old data parts, altering data parts, replica re-initialization, etc.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundSchedulePoolSize"}, {"description": "Number of active tasks in BackgroundBufferFlushSchedulePool. This pool is used for periodic Buffer flushes", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundBufferFlushSchedulePoolTask"}, {"description": "Limit on number of tasks in BackgroundBufferFlushSchedulePool", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundBufferFlushSchedulePoolSize"}, {"description": "Number of active tasks in BackgroundDistributedSchedulePool. This pool is used for distributed sends that is done in background.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundDistributedSchedulePoolTask"}, {"description": "Limit on number of tasks in BackgroundDistributedSchedulePool", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundDistributedSchedulePoolSize"}, {"description": "Number of active tasks in BackgroundProcessingPool for message streaming", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundMessageBrokerSchedulePoolTask"}, {"description": "Limit on number of tasks in BackgroundProcessingPool for message streaming", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackgroundMessageBrokerSchedulePoolSize"}, {"description": "Number of 'batches' (a set of keys) in update queue in CacheDictionaries.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_CacheDictionaryUpdateQueueBatches"}, {"description": "Exact number of keys in update queue in CacheDictionaries.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_CacheDictionaryUpdateQueueKeys"}, {"description": "Disk space reserved for currently running background merges. It is slightly more than the total size of currently merging parts.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseMetrics_DiskSpaceReservedForMerge"}, {"description": "Number of connections to remote servers sending data that was INSERTed into Distributed tables. Both synchronous and asynchronous mode.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DistributedSend"}, {"description": "Number of queries that are stopped and waiting due to 'priority' setting.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_QueryPreempted"}, {"description": "Number of connections to TCP server (clients with native interface), also included server-server distributed query connections", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TCPConnection"}, {"description": "Number of client connections using MySQL protocol", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MySQLConnection"}, {"description": "Number of connections to HTTP server", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_HTTPConnection"}, {"description": "Number of connections from other replicas to fetch parts", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_InterserverConnection"}, {"description": "Number of client connections using PostgreSQL protocol", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PostgreSQLConnection"}, {"description": "Number of files open for reading", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_OpenFileForRead"}, {"description": "Number of files open for writing", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_OpenFileForWrite"}, {"description": "Number of temporary files created", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TotalTemporaryFiles"}, {"description": "Number of temporary files created for external sorting", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TemporaryFilesForSort"}, {"description": "Number of temporary files created for external aggregation", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TemporaryFilesForAggregation"}, {"description": "Number of temporary files created for JOIN", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TemporaryFilesForJoin"}, {"description": "Number of temporary files created without known purpose", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TemporaryFilesUnknown"}, {"description": "Number of read (read, pread, io_getevents, etc.) syscalls in fly", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_Read"}, {"description": "Number of read with remote reader in fly", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RemoteRead"}, {"description": "Number of write (write, pwrite, io_getevents, etc.) syscalls in fly", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_Write"}, {"description": "Number of threads receiving data from network. Only ClickHouse-related network interaction is included, not by 3rd party libraries.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_NetworkReceive"}, {"description": "Number of threads sending data to network. Only ClickHouse-related network interaction is included, not by 3rd party libraries.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_NetworkSend"}, {"description": "Number of connections that are sending data for scalars to remote servers.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_SendScalars"}, {"description": "Number of connections that are sending data for external tables to remote servers. External tables are used to implement GLOBAL IN and GLOBAL JOIN operators with distributed subqueries.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_SendExternalTables"}, {"description": "Number of query processing threads", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_QueryThread"}, {"description": "Number of Replicated tables that are currently in readonly state due to re-initialization after ZooKeeper session loss or due to startup without ZooKeeper configured.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ReadonlyReplica"}, {"description": "Total amount of memory (bytes) allocated by the server.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseMetrics_MemoryTracking"}, {"description": "Total amount of memory (bytes) allocated by background tasks (merges and mutations).", "type": "gauge", "unit": "Bytes", "name": "ClickHouseMetrics_MergesMutationsMemoryTracking"}, {"description": "Number of ephemeral nodes hold in <PERSON><PERSON><PERSON><PERSON>.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_EphemeralNode"}, {"description": "Number of sessions (connections) to ZooKeeper. Should be no more than one, because using more than one connection to ZooKeeper may lead to bugs due to lack of linearizability (stale reads) that ZooKeeper consistency model allows.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ZooKeeperSession"}, {"description": "Number of watches (event subscriptions) in ZooKeeper.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ZooKeeperWatch"}, {"description": "Number of requests to <PERSON><PERSON><PERSON><PERSON> in fly.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ZooKeeperRequest"}, {"description": "Number of INSERT queries that are throttled due to high number of active data parts for partition in a MergeTree table.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DelayedInserts"}, {"description": "Number of threads waiting for lock in Context. This is global lock.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ContextLockWait"}, {"description": "Number of rows in buffers of Buffer tables", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageBufferRows"}, {"description": "Number of bytes in buffers of Buffer tables", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageBufferBytes"}, {"description": "Number of requests in fly to data sources of dictionaries of cache type.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DictCacheRequests"}, {"description": "Revision of the server. It is a number incremented for every release or release candidate except patch releases.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_Revision"}, {"description": "Version of the server in a single integer number in base-1000. For example, version 11.22.33 is translated to 11022033.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_VersionInteger"}, {"description": "Number of threads waiting for read on a table RWLock.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RWLockWaitingReaders"}, {"description": "Number of threads waiting for write on a table RWLock.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RWLockWaitingWriters"}, {"description": "Number of threads holding read lock in a table RWLock.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RWLockActiveReaders"}, {"description": "Number of threads holding write lock in a table RWLock.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RWLockActiveWriters"}, {"description": "Number of threads in global thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_GlobalThread"}, {"description": "Number of threads in global thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_GlobalThreadActive"}, {"description": "Number of queued or active jobs in global thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_GlobalThreadScheduled"}, {"description": "Number of threads in local thread pools. The threads in local thread pools are taken from the global thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_LocalThread"}, {"description": "Number of threads in local thread pools running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_LocalThreadActive"}, {"description": "Number of queued or active jobs in local thread pools.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_LocalThreadScheduled"}, {"description": "Number of threads in the MergeTreeDataSelectExecutor thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeDataSelectExecutorThreads"}, {"description": "Number of threads in the MergeTreeDataSelectExecutor thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeDataSelectExecutorThreadsActive"}, {"description": "Number of queued or active jobs in the MergeTreeDataSelectExecutor thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeDataSelectExecutorThreadsScheduled"}, {"description": "Number of threads in the thread pool for BACKUP.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackupsThreads"}, {"description": "Number of threads in thread pool for BACKUP running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackupsThreadsActive"}, {"description": "Number of queued or active jobs for BACKUP.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackupsThreadsScheduled"}, {"description": "Number of threads in the thread pool for RESTORE.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RestoreThreads"}, {"description": "Number of threads in the thread pool for RESTORE running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RestoreThreadsActive"}, {"description": "Number of queued or active jobs for RESTORE.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RestoreThreadsScheduled"}, {"description": "Number of threads in thread pool for loading marks.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MarksLoaderThreads"}, {"description": "Number of threads in the thread pool for loading marks running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MarksLoaderThreadsActive"}, {"description": "Number of queued or active jobs in the thread pool for loading marks.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MarksLoaderThreadsScheduled"}, {"description": "Number of threads in the IO prefetch thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOPrefetchThreads"}, {"description": "Number of threads in the IO prefetch thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOPrefetchThreadsActive"}, {"description": "Number of queued or active jobs in the IO prefetch thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOPrefetchThreadsScheduled"}, {"description": "Number of threads in the IO writer thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOWriterThreads"}, {"description": "Number of threads in the IO writer thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOWriterThreadsActive"}, {"description": "Number of queued or active jobs in the IO writer thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOWriterThreadsScheduled"}, {"description": "Number of threads in the IO thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOThreads"}, {"description": "Number of threads in the IO thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOThreadsActive"}, {"description": "Number of queued or active jobs in the IO thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOThreadsScheduled"}, {"description": "Number of threads in the thread pool for remote_filesystem_read_method=threadpool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ThreadPoolRemoteFSReaderThreads"}, {"description": "Number of threads in the thread pool for remote_filesystem_read_method=threadpool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ThreadPoolRemoteFSReaderThreadsActive"}, {"description": "Number of queued or active jobs in the thread pool for remote_filesystem_read_method=threadpool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ThreadPoolRemoteFSReaderThreadsScheduled"}, {"description": "Number of threads in the thread pool for local_filesystem_read_method=threadpool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ThreadPoolFSReaderThreads"}, {"description": "Number of threads in the thread pool for local_filesystem_read_method=threadpool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ThreadPoolFSReaderThreadsActive"}, {"description": "Number of queued or active jobs in the thread pool for local_filesystem_read_method=threadpool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ThreadPoolFSReaderThreadsScheduled"}, {"description": "Number of threads in the BackupsIO thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackupsIOThreads"}, {"description": "Number of threads in the BackupsIO thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackupsIOThreadsActive"}, {"description": "Number of queued or active jobs in the BackupsIO thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BackupsIOThreadsScheduled"}, {"description": "Number of threads in the StorageHive thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageHiveThreads"}, {"description": "Number of threads in the StorageHive thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageHiveThreadsActive"}, {"description": "Number of queued or active jobs in the StorageHive thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageHiveThreadsScheduled"}, {"description": "Number of threads in the tables loader background thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TablesLoaderBackgroundThreads"}, {"description": "Number of threads in the tables loader background thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TablesLoaderBackgroundThreadsActive"}, {"description": "Number of queued or active jobs in the tables loader background thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TablesLoaderBackgroundThreadsScheduled"}, {"description": "Number of threads in the tables loader foreground thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TablesLoaderForegroundThreads"}, {"description": "Number of threads in the tables loader foreground thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TablesLoaderForegroundThreadsActive"}, {"description": "Number of queued or active jobs in the tables loader foreground thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TablesLoaderForegroundThreadsScheduled"}, {"description": "Number of threads in the DatabaseOnDisk thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DatabaseOnDiskThreads"}, {"description": "Number of threads in the DatabaseOnDisk thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DatabaseOnDiskThreadsActive"}, {"description": "Number of queued or active jobs in the DatabaseOnDisk thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DatabaseOnDiskThreadsScheduled"}, {"description": "Number of threads in the DatabaseCatalog thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DatabaseCatalogThreads"}, {"description": "Number of threads in the DatabaseCatalog thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DatabaseCatalogThreadsActive"}, {"description": "Number of queued or active jobs in the DatabaseCatalog thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DatabaseCatalogThreadsScheduled"}, {"description": "Number of threads in the thread pool for destroy aggregate states.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DestroyAggregatesThreads"}, {"description": "Number of threads in the thread pool for destroy aggregate states running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DestroyAggregatesThreadsActive"}, {"description": "Number of queued or active jobs in the thread pool for destroy aggregate states.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DestroyAggregatesThreadsScheduled"}, {"description": "Number of threads in the HashedDictionary thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_HashedDictionaryThreads"}, {"description": "Number of threads in the HashedDictionary thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_HashedDictionaryThreadsActive"}, {"description": "Number of queued or active jobs in the HashedDictionary thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_HashedDictionaryThreadsScheduled"}, {"description": "Number of threads in the CacheDictionary thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_CacheDictionaryThreads"}, {"description": "Number of threads in the CacheDictionary thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_CacheDictionaryThreadsActive"}, {"description": "Number of queued or active jobs in the CacheDictionary thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_CacheDictionaryThreadsScheduled"}, {"description": "Number of threads in the ParallelFormattingOutputFormatThreads thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParallelFormattingOutputFormatThreads"}, {"description": "Number of threads in the ParallelFormattingOutputFormatThreads thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParallelFormattingOutputFormatThreadsActive"}, {"description": "Number of queued or active jobs in the ParallelFormattingOutputFormatThreads thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParallelFormattingOutputFormatThreadsScheduled"}, {"description": "Number of threads in the ParallelParsingInputFormat thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParallelParsingInputFormatThreads"}, {"description": "Number of threads in the ParallelParsingInputFormat thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParallelParsingInputFormatThreadsActive"}, {"description": "Number of queued or active jobs in the ParallelParsingInputFormat thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParallelParsingInputFormatThreadsScheduled"}, {"description": "Number of threads in the MergeTreeBackgroundExecutor thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeBackgroundExecutorThreads"}, {"description": "Number of threads in the MergeTreeBackgroundExecutor thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeBackgroundExecutorThreadsActive"}, {"description": "Number of queued or active jobs in the MergeTreeBackgroundExecutor thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeBackgroundExecutorThreadsScheduled"}, {"description": "Number of threads in the AsynchronousInsert thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AsynchronousInsertThreads"}, {"description": "Number of threads in the AsynchronousInsert thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AsynchronousInsertThreadsActive"}, {"description": "Number of queued or active jobs in the AsynchronousInsert thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AsynchronousInsertThreadsScheduled"}, {"description": "Number of threads in the StartupSystemTables thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StartupSystemTablesThreads"}, {"description": "Number of threads in the StartupSystemTables thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StartupSystemTablesThreadsActive"}, {"description": "Number of queued or active jobs in the StartupSystemTables thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StartupSystemTablesThreadsScheduled"}, {"description": "Number of threads in the Aggregator thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AggregatorThreads"}, {"description": "Number of threads in the Aggregator thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AggregatorThreadsActive"}, {"description": "Number of queued or active jobs in the Aggregator thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AggregatorThreadsScheduled"}, {"description": "Number of threads in the DDLWorker thread pool for ON CLUSTER queries.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DDLWorkerThreads"}, {"description": "Number of threads in the DDLWORKER thread pool for ON CLUSTER queries running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DDLWorkerThreadsActive"}, {"description": "Number of queued or active jobs in the DDLWORKER thread pool for ON CLUSTER queries.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DDLWorkerThreadsScheduled"}, {"description": "Number of threads in the StorageDistributed thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageDistributedThreads"}, {"description": "Number of threads in the StorageDistributed thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageDistributedThreadsActive"}, {"description": "Number of queued or active jobs in the StorageDistributed thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageDistributedThreadsScheduled"}, {"description": "Number of threads used for INSERT into Distributed.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DistributedInsertThreads"}, {"description": "Number of threads used for INSERT into Distributed running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DistributedInsertThreadsActive"}, {"description": "Number of queued or active jobs used for INSERT into Distributed.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DistributedInsertThreadsScheduled"}, {"description": "Number of threads in the StorageS3 thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageS3Threads"}, {"description": "Number of threads in the StorageS3 thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageS3ThreadsActive"}, {"description": "Number of queued or active jobs in the StorageS3 thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_StorageS3ThreadsScheduled"}, {"description": "Number of threads in the S3ObjectStorage thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ObjectStorageS3Threads"}, {"description": "Number of threads in the S3ObjectStorage thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ObjectStorageS3ThreadsActive"}, {"description": "Number of queued or active jobs in the S3ObjectStorage thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ObjectStorageS3ThreadsScheduled"}, {"description": "Number of threads in the AzureObjectStorage thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ObjectStorageAzureThreads"}, {"description": "Number of threads in the AzureObjectStorage thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ObjectStorageAzureThreadsActive"}, {"description": "Number of queued or active jobs in the AzureObjectStorage thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ObjectStorageAzureThreadsScheduled"}, {"description": "Number of threads in the MergeTree parts loader thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreePartsLoaderThreads"}, {"description": "Number of threads in the MergeTree parts loader thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreePartsLoaderThreadsActive"}, {"description": "Number of queued or active jobs in the MergeTree parts loader thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreePartsLoaderThreadsScheduled"}, {"description": "Number of threads in the threadpool for loading Outdated data parts.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeOutdatedPartsLoaderThreads"}, {"description": "Number of active threads in the threadpool for loading Outdated data parts.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeOutdatedPartsLoaderThreadsActive"}, {"description": "Number of queued or active jobs in the threadpool for loading Outdated data parts.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeOutdatedPartsLoaderThreadsScheduled"}, {"description": "Number of threads in the MergeTree parts cleaner thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreePartsCleanerThreads"}, {"description": "Number of threads in the MergeTree parts cleaner thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreePartsCleanerThreadsActive"}, {"description": "Number of queued or active jobs in the MergeTree parts cleaner thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreePartsCleanerThreadsScheduled"}, {"description": "Number of threads for copying data between disks of different types.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IDiskCopierThreads"}, {"description": "Number of threads for copying data between disks of different types running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IDiskCopierThreadsActive"}, {"description": "Number of queued or active jobs for copying data between disks of different types.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IDiskCopierThreadsScheduled"}, {"description": "Number of threads in the system.replicas thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_SystemReplicasThreads"}, {"description": "Number of threads in the system.replicas thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_SystemReplicasThreadsActive"}, {"description": "Number of queued or active jobs in the system.replicas thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_SystemReplicasThreadsScheduled"}, {"description": "Number of threads in the RESTART REPLICA thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RestartReplicaThreads"}, {"description": "Number of threads in the RESTART REPLICA thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RestartReplicaThreadsActive"}, {"description": "Number of queued or active jobs in the RESTART REPLICA thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RestartReplicaThreadsScheduled"}, {"description": "Number of threads in the PipelineExecutor thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_QueryPipelineExecutorThreads"}, {"description": "Number of threads in the PipelineExecutor thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_QueryPipelineExecutorThreadsActive"}, {"description": "Number of queued or active jobs in the PipelineExecutor thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_QueryPipelineExecutorThreadsScheduled"}, {"description": "Number of threads in the ParquetBlockInputFormat thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParquetDecoderThreads"}, {"description": "Number of threads in the ParquetBlockInputFormat thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParquetDecoderThreadsActive"}, {"description": "Number of queued or active jobs in the ParquetBlockInputFormat thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParquetDecoderThreadsScheduled"}, {"description": "Number of threads in ParquetBlockOutputFormat thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParquetEncoderThreads"}, {"description": "Number of threads in ParquetBlockOutputFormat thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParquetEncoderThreadsActive"}, {"description": "Number of queued or active jobs in ParquetBlockOutputFormat thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ParquetEncoderThreadsScheduled"}, {"description": "Number of threads in the DWARFBlockInputFormat thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DWARFReaderThreads"}, {"description": "Number of threads in the DWARFBlockInputFormat thread pool running a task.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DWARFReaderThreadsActive"}, {"description": "Number of queued or active jobs in the DWARFBlockInputFormat thread pool.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DWARFReaderThreadsScheduled"}, {"description": "Number of threads in the threadpool for loading Outdated data parts.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_OutdatedPartsLoadingThreads"}, {"description": "Number of active threads in the threadpool for loading Outdated data parts.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_OutdatedPartsLoadingThreadsActive"}, {"description": "Number of queued or active jobs in the threadpool for loading Outdated data parts.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_OutdatedPartsLoadingThreadsScheduled"}, {"description": "Number of pending bytes to process for asynchronous insertion into Distributed tables. Number of bytes for every shard is summed.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DistributedBytesToInsert"}, {"description": "Number of bytes for asynchronous insertion into Distributed tables that has been marked as broken. Number of bytes for every shard is summed.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BrokenDistributedBytesToInsert"}, {"description": "Number of pending files to process for asynchronous insertion into Distributed tables. Number of files for every shard is summed.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_DistributedFilesToInsert"}, {"description": "Number of files for asynchronous insertion into Distributed tables that has been marked as broken. Number of files for every shard is summed.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_BrokenDistributedFilesToInsert"}, {"description": "Number of dropped tables, that are waiting for background data removal.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_TablesToDropQueueSize"}, {"description": "Max processed DDL entry of DDLWorker.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MaxDDLEntryID"}, {"description": "Max DDL entry of DDLWorker that pushed to zookeeper.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MaxPushedDDLEntryID"}, {"description": "The part is generating now, it is not in data_parts list.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartsTemporary"}, {"description": "The part is in data_parts, but not used for SELECTs.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartsPreActive"}, {"description": "Active data part, used by current and upcoming SELECTs.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartsActive"}, {"description": "Active database, used by current and upcoming SELECTs.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AttachedDatabase"}, {"description": "Active table, used by current and upcoming SELECTs.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AttachedTable"}, {"description": "Not active data part, but could be used by only current SELECTs, could be deleted after SELECTs finishes.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartsOutdated"}, {"description": "Not active data part with identity refcounter, it is deleting right now by a cleaner.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartsDeleting"}, {"description": "Part was moved to another disk and should be deleted in own destructor.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartsDeleteOnDestroy"}, {"description": "Wide parts.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartsWide"}, {"description": "Compact parts.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartsCompact"}, {"description": "In-memory parts.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PartsInMemory"}, {"description": "Total number of mmapped files.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MMappedFiles"}, {"description": "Sum size of mmapped file regions.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseMetrics_MMappedFileBytes"}, {"description": "Number of threads waiting for asynchronous read.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AsynchronousReadWait"}, {"description": "Number of asynchronous inserts that are waiting for flush.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_PendingAsyncInsert"}, {"description": "Number of active Kafka consumers", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KafkaConsumers"}, {"description": "Number of active Kafka consumers which have some partitions assigned.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KafkaConsumersWithAssignment"}, {"description": "Number of active Kafka producer created", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KafkaProducers"}, {"description": "Number of active librdkafka threads", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KafkaLibrdkafkaThreads"}, {"description": "Number of background reads currently working (populating materialized views from Kafka)", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KafkaBackgroundReads"}, {"description": "Number of consumers which are currently used by direct or background reads", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KafkaConsumersInUse"}, {"description": "Number of currently running inserts to Kafka", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KafkaWrites"}, {"description": "Number of partitions Kafka tables currently assigned to", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KafkaAssignedPartitions"}, {"description": "Number of active cache buffers", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_FilesystemCacheReadBuffers"}, {"description": "Number of existing cache file segments", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_CacheFileSegments"}, {"description": "Number of existing detached cache file segments", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_CacheDetachedFileSegments"}, {"description": "Filesystem cache size in bytes", "type": "gauge", "unit": "Bytes", "name": "ClickHouseMetrics_FilesystemCacheSize"}, {"description": "Filesystem cache size limit in bytes", "type": "gauge", "unit": "Bytes", "name": "ClickHouseMetrics_FilesystemCacheSizeLimit"}, {"description": "Filesystem cache elements (file segments)", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_FilesystemCacheElements"}, {"description": "Filesystem cache elements in download queue", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_FilesystemCacheDownloadQueueElements"}, {"description": "Filesystem cache elements in background cleanup queue", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_FilesystemCacheDelayedCleanupElements"}, {"description": "Filesystem cache file segment which are currently hold as unreleasable", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_FilesystemCacheHoldFileSegments"}, {"description": "Number of async insert hash id in cache", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_AsyncInsertCacheSize"}, {"description": "S3 requests count", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_S3Requests"}, {"description": "Number of alive connections", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KeeperAliveConnections"}, {"description": "Number of outstanding requests", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_KeeperOutstandingRequets"}, {"description": "Number of waiting threads inside of OvercommitTracker", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ThreadsInOvercommitTracker"}, {"description": "Number of io_uring SQEs waiting to be submitted", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOUringPendingEvents"}, {"description": "Number of io_uring SQEs in flight", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_IOUringInFlightEvents"}, {"description": "The current number of callback requests in flight from the remote server back to the initiator server to choose the read task (for s3Cluster table function and similar). Measured on the remote server side.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ReadTaskRequestsSent"}, {"description": "The current number of callback requests in flight from the remote server back to the initiator server to choose the read task (for MergeTree tables). Measured on the remote server side.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeReadTaskRequestsSent"}, {"description": "The current number of announcement being sent in flight from the remote server to the initiator server about the set of data parts (for MergeTree tables). Measured on the remote server side.", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_MergeTreeAllRangesAnnouncementsSent"}, {"description": "Number of Created thread local timers in QueryProfiler", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_CreatedTimersInQueryProfiler"}, {"description": "Number of Active thread local timers in QueryProfiler", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_ActiveTimersInQueryProfiler"}, {"description": "Number materialized views with periodic refreshing (REFRESH)", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RefreshableViews"}, {"description": "Number of materialized views currently executing a refresh", "type": "gauge", "unit": "Number", "name": "ClickHouseMetrics_RefreshingViews"}, {"description": "Time in seconds spent for calculation of asynchronous metrics (this is the overhead of asynchronous metrics).", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_AsynchronousMetricsCalculationTimeSpent"}, {"description": "The total number of parts detached from MergeTree tables by users with the `ALTER TABLE DETACH` query (as opposed to unexpected, broken or ignored parts). The server does not care about detached parts and they can be removed.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NumberOfDetachedByUserParts"}, {"description": "Heavy (tables related) metrics update interval", "type": "gauge", "unit": "Microseconds", "name": "ClickHouseAsyncMetrics_AsynchronousHeavyMetricsUpdateInterval"}, {"description": "The total amount of memory (in bytes) reserved for primary key values (only takes active parts into account).", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_TotalPrimaryKeyBytesInMemoryAllocated"}, {"description": "The total amount of memory (in bytes) used by primary key values (only takes active parts into account).", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_TotalPrimaryKeyBytesInMemory"}, {"description": "Total amount of rows (records) stored in tables of MergeTree family in the system database.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_TotalRowsOfMergeTreeTablesSystem"}, {"description": "Total amount of data parts in all tables of MergeTree family. Numbers larger than 10 000 will negatively affect the server startup time and it may indicate unreasonable choice of the partition key.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_TotalPartsOfMergeTreeTables"}, {"description": "Total number of tables summed across the databases on the server, excluding the databases that cannot contain MergeTree tables. The excluded database engines are those who generate the set of tables on the fly, like `Lazy`, `MySQL`, `PostgreSQL`, `SQlite`.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NumberOfTables"}, {"description": "Maximum difference between the replica delay and the delay of the most up-to-date replica of the same table, across Replicated tables.", "type": "gauge", "unit": "Microseconds", "name": "ClickHouseAsyncMetrics_ReplicasMaxRelativeDelay"}, {"description": "Maximum difference in seconds between the most fresh replicated part and the most fresh data part still to be replicated, across Replicated tables. A very high value indicates a replica with no data.", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_ReplicasMaxAbsoluteDelay"}, {"description": "Sum of merge operations in the queue (still to be applied) across Replicated tables.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_ReplicasSumMergesInQueue"}, {"description": "Available bytes on the disk (virtual filesystem) without the reservations for merges, fetches, and moves. Remote filesystems may not provide this information.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_DiskUnreserved_default"}, {"description": "Used bytes on the disk (virtual filesystem). Remote filesystems not always provide this information.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_DiskUsed_default"}, {"description": "Used bytes on the volume where ClickHouse logs path is mounted.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_FilesystemLogsPathUsedBytes"}, {"description": "The total size in bytes of the disk (virtual filesystem). Remote filesystems may not provide this information.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_DiskTotal_default"}, {"description": "Available bytes on the volume where ClickHouse logs path is mounted. If this value approaches zero, you should tune the log rotation in the configuration file.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_FilesystemLogsPathAvailableBytes"}, {"description": "The size of the volume where ClickHouse logs path is mounted, in bytes. It's recommended to have at least 10 GB for logs.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_FilesystemLogsPathTotalBytes"}, {"description": "The number of available inodes on the volume where the main ClickHouse path is mounted. If it is close to zero, it indicates a misconfiguration, and you will get 'no space left on device' even when the disk is not full.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_FilesystemMainPathAvailableINodes"}, {"description": "Available bytes on the volume where the main ClickHouse path is mounted.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_FilesystemMainPathAvailableBytes"}, {"description": "Total entries in the cache of JIT-compiled code.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_CompiledExpressionCacheCount"}, {"description": "Total amount of bytes (compressed, including data and indices) stored in all tables of MergeTree family.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_TotalBytesOfMergeTreeTables"}, {"description": "Total bytes used for the cache of JIT-compiled code.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_CompiledExpressionCacheBytes"}, {"description": "Total number of cached file segments in the `cache` virtual filesystem. This cache is hold on disk.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_FilesystemCacheFiles"}, {"description": "Total bytes in the `cache` virtual filesystem. This cache is hold on disk.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_FilesystemCacheBytes"}, {"description": "Total number of entries in the query cache.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_QueryCacheEntries"}, {"description": "Total size of the query cache in bytes.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_QueryCacheBytes"}, {"description": "Total size of mark cache for secondary indices in bytes.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_IndexMarkCacheBytes"}, {"description": "Total number of entries in the uncompressed cache. Each entry represents a decompressed block of data. Uncompressed cache does not usually improve performance and should be mostly avoided.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_UncompressedCacheCells"}, {"description": "Total size of uncompressed cache in bytes. Uncompressed cache does not usually improve the performance and should be mostly avoided.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_UncompressedCacheBytes"}, {"description": "Number of threads in the server of the HTTP interface (without TLS).", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_HTTPThreads"}, {"description": "Total number of entries in the uncompressed cache for secondary indices. Each entry represents a decompressed block of data. Uncompressed cache does not usually improve performance and should be mostly avoided.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_IndexUncompressedCacheCells"}, {"description": "Number of threads in the server of the replicas communication protocol (without TLS).", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_InterserverThreads"}, {"description": "The temperature reported by the corresponding hardware monitor and the corresponding sensor in ℃. A sensor can return an unrealistic value. Source: `/sys/class/hwmon`", "type": "gauge", "unit": "℃.", "name": "ClickHouseAsyncMetrics_Temperature_nvme_Composite"}, {"description": "The temperature reported by the corresponding hardware monitor and the corresponding sensor in ℃. A sensor can return an unrealistic value. Source: `/sys/class/hwmon`", "type": "gauge", "unit": "℃.", "name": "ClickHouseAsyncMetrics_Temperature_k10temp_Tdie"}, {"description": "Total number of databases on the server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NumberOfDatabases"}, {"description": "Number of times a packed was dropped while sending via the network interface. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NetworkSendDrop_eth0"}, {"description": "Number of times error (e.g. TCP retransmit) happened while sending via the network interface. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NetworkSendErrors_eth0"}, {"description": "Number of network packets sent via the network interface. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NetworkSendPackets_eth0"}, {"description": "Number of bytes sent via the network interface. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NetworkSendBytes_eth0"}, {"description": "Number of bytes a packet was dropped while received via the network interface. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NetworkReceiveDrop_eth0"}, {"description": "Number of network packets received via the network interface. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NetworkReceivePackets_eth0"}, {"description": "This value counts the number of milliseconds that IO requests have waited on this block device. If there are multiple IO requests waiting, this value will increase as the product of the number of milliseconds times the number of requests waiting. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockQueueTime_nvme0n1"}, {"description": "Available bytes on the disk (virtual filesystem). Remote filesystems may not provide this information.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_DiskAvailable_default"}, {"description": "Time in seconds the block device had the IO requests queued. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockActiveTime_nvme0n1"}, {"description": "This value counts the number of I/O requests that have been issued to the device driver but have not yet completed. It does not include IO requests that are in the queue but not yet issued to the device driver. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockInFlightOps_nvme0n1"}, {"description": "Time in seconds spend in write operations requested from the block device, summed across all the operations. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockWriteTime_nvme0n1"}, {"description": "Number of bytes written to the block device. It can be lower than the number of bytes written to the filesystem due to the usage of the OS page cache, that saves IO. A write to the block device may happen later than the corresponding write to the filesystem due to write-through caching. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteBytes_nvme0n1"}, {"description": "Number of write operations requested from the block device and merged together by the OS IO scheduler. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteMerges_nvme0n1"}, {"description": "Number of discard operations requested from the block device. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardOps_nvme0n1"}, {"description": "Number of read operations requested from the block device. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadOps_nvme0n1"}, {"description": "The server uptime in seconds. It includes the time spent for server initialization before accepting connections.", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_Uptime"}, {"description": "The number of times the prediction of a hash table size was incorrect.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_HashTableStatsCacheMisses"}, {"description": "Time in seconds spend in write operations requested from the block device, summed across all the operations. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockWriteTime_sdb"}, {"description": "Maximum number of merge operations in the queue (still to be applied) across Replicated tables.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_ReplicasMaxMergesInQueue"}, {"description": "Time in seconds spend in read operations requested from the block device, summed across all the operations. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockReadTime_sdb"}, {"description": "Number of discarded bytes on the block device. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardBytes_sdb"}, {"description": "Maximum number of INSERT operations in the queue (still to be replicated) across Replicated tables.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_ReplicasMaxInsertsInQueue"}, {"description": "Number of bytes written to the block device. It can be lower than the number of bytes written to the filesystem due to the usage of the OS page cache, that saves IO. A write to the block device may happen later than the corresponding write to the filesystem due to write-through caching. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteBytes_sdb"}, {"description": "Number of read operations requested from the block device and merged together by the OS IO scheduler. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadMerges_sdb"}, {"description": "Number of write operations requested from the block device. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteOps_sdb"}, {"description": "Number of read operations requested from the block device. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadOps_sdb"}, {"description": "Time in seconds the block device had the IO requests queued. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockActiveTime_sdc"}, {"description": "This value counts the number of I/O requests that have been issued to the device driver but have not yet completed. It does not include IO requests that are in the queue but not yet issued to the device driver. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockInFlightOps_sdc"}, {"description": "Maximum queue size (in the number of operations like get, merge) across Replicated tables.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_ReplicasMaxQueueSize"}, {"description": "Time in seconds spend in read operations requested from the block device, summed across all the operations. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockReadTime_sdc"}, {"description": "Number of discarded bytes on the block device. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardBytes_sdc"}, {"description": "Number of bytes written to the block device. It can be lower than the number of bytes written to the filesystem due to the usage of the OS page cache, that saves IO. A write to the block device may happen later than the corresponding write to the filesystem due to write-through caching. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteBytes_sdc"}, {"description": "Number of discard operations requested from the block device and merged together by the OS IO scheduler. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardMerges_sdc"}, {"description": "Number of times error happened receiving via the network interface. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NetworkReceiveErrors_eth0"}, {"description": "Number of read operations requested from the block device and merged together by the OS IO scheduler. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadMerges_sdc"}, {"description": "Number of write operations requested from the block device. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteOps_sdc"}, {"description": "The total number of inodes on the volume where ClickHouse logs path is mounted.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_FilesystemLogsPathTotalINodes"}, {"description": "This value counts the number of milliseconds that IO requests have waited on this block device. If there are multiple IO requests waiting, this value will increase as the product of the number of milliseconds times the number of requests waiting. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockQueueTime_sda"}, {"description": "Time in seconds the block device had the IO requests queued. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockActiveTime_sda"}, {"description": "Time in seconds spend in discard operations requested from the block device, summed across all the operations. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockDiscardTime_sda"}, {"description": "Number of bytes read from the block device. It can be lower than the number of bytes read from the filesystem due to the usage of the OS page cache, that saves IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadBytes_sda"}, {"description": "Number of discard operations requested from the block device and merged together by the OS IO scheduler. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardMerges_sda"}, {"description": "Time in seconds the block device had the IO requests queued. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockActiveTime_sdb"}, {"description": "Number of read operations requested from the block device and merged together by the OS IO scheduler. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadMerges_sda"}, {"description": "Number of discard operations requested from the block device. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardOps_sda"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_15"}, {"description": "Number of write operations requested from the block device. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteOps_sda"}, {"description": "Number of write operations requested from the block device and merged together by the OS IO scheduler. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteMerges_sdb"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_14"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_11"}, {"description": "This value counts the number of milliseconds that IO requests have waited on this block device. If there are multiple IO requests waiting, this value will increase as the product of the number of milliseconds times the number of requests waiting. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockQueueTime_sdb"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_10"}, {"description": "Time in seconds spend in write operations requested from the block device, summed across all the operations. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockWriteTime_sda"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_7"}, {"description": "Total size of uncompressed cache in bytes for secondary indices. Uncompressed cache does not usually improve the performance and should be mostly avoided.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_IndexUncompressedCacheBytes"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_6"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_5"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_4"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_3"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_2"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_1"}, {"description": "Number of bytes read from the block device. It can be lower than the number of bytes read from the filesystem due to the usage of the OS page cache, that saves IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadBytes_sdb"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_0"}, {"description": "The amount of free memory plus OS page cache memory on the host system, in bytes. This memory is available to be used by programs. The value should be very similar to `OSMemoryAvailable`. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_OSMemoryFreePlusCached"}, {"description": "The amount of memory used by the OS page cache, in bytes. Typically, almost all available memory is used by the OS page cache - high values of this metric are normal and expected. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_OSMemoryCached"}, {"description": "The amount of memory used by OS kernel buffers, in bytes. This should be typically small, and large values may indicate a misconfiguration of the OS. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_OSMemoryBuffers"}, {"description": "The amount of memory available to be used by programs, in bytes. This is very similar to the `OSMemoryFreePlusCached` metric. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_OSMemoryAvailable"}, {"description": "The total amount of memory in cgroup, in bytes. If stated zero, the limit is the same as OSMemoryTotal.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_CGroupMemoryTotal"}, {"description": "Number of discarded bytes on the block device. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardBytes_nvme0n1"}, {"description": "The value is similar to `OSGuestTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSGuestTimeNormalized"}, {"description": "The value is similar to `OSSoftIrqTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeNormalized"}, {"description": "The value is similar to `OSIrqTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSIrqTimeNormalized"}, {"description": "The value is similar to `OSIOWaitTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeNormalized"}, {"description": "The value is similar to `OSIdleTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSIdleTimeNormalized"}, {"description": "The value is similar to `OSSystemTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSSystemTimeNormalized"}, {"description": "The number of used inodes on the volume where the main ClickHouse path is mounted. This value mostly corresponds to the number of files.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_FilesystemMainPathUsedINodes"}, {"description": "The value is similar to `OSUserTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSUserTimeNormalized"}, {"description": "The number of processes created. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSProcessesCreated"}, {"description": "The number of context switches that the system underwent on the host machine. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSContextSwitches"}, {"description": "The number of interrupts on the host machine. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSInterrupts"}, {"description": "The number of runnable (running or ready to run) threads by the operating system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSProcessesRunning"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU15"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_9"}, {"description": "This value counts the number of I/O requests that have been issued to the device driver but have not yet completed. It does not include IO requests that are in the queue but not yet issued to the device driver. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockInFlightOps_sda"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU15"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU15"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU15"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU15"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU15"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU14"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU14"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU13"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU13"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_12"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU13"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU12"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU5"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU15"}, {"description": "Maximum amount of physical memory used by the server process, in bytes.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_MemoryResidentMax"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU12"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU9"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU12"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU12"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTime"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU12"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU2"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU11"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU10"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU11"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU11"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU11"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU10"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU11"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU11"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU8"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU0"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU10"}, {"description": "Number of threads in the server of the TCP protocol (without TLS).", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_TCPThreads"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU10"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU14"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU10"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU10"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU11"}, {"description": "Sum queue size (in the number of operations like get, merge) across Replicated tables.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_ReplicasSumQueueSize"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU13"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU14"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU1"}, {"description": "Number of discard operations requested from the block device. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardOps_sdc"}, {"description": "The number of files opened with `mmap` (mapped in memory). This is used for queries with the setting `local_filesystem_read_method` set to `mmap`. The files opened with `mmap` are kept in the cache to avoid costly TLB flushes.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_MMapCacheCells"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU10"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU14"}, {"description": "Time in seconds spent for calculation of asynchronous heavy (tables related) metrics (this is the overhead of asynchronous metrics).", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_AsynchronousHeavyMetricsCalculationTimeSpent"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU10"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU14"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU6"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU13"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU9"}, {"description": "Number of discard operations requested from the block device and merged together by the OS IO scheduler. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardMerges_nvme0n1"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU12"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU9"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU9"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU9"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU9"}, {"description": "The value is similar to `OSNiceTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSNiceTimeNormalized"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU13"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU9"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU13"}, {"description": "Sum of INSERT operations in the queue (still to be replicated) across Replicated tables.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_ReplicasSumInsertsInQueue"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU12"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU8"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU8"}, {"description": "Time in seconds spend in discard operations requested from the block device, summed across all the operations. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockDiscardTime_sdc"}, {"description": "Number of read operations requested from the block device and merged together by the OS IO scheduler. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadMerges_nvme0n1"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU8"}, {"description": "Number of write operations requested from the block device. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteOps_nvme0n1"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU8"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU4"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU8"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU8"}, {"description": "Time in seconds spend in discard operations requested from the block device, summed across all the operations. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockDiscardTime_nvme0n1"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU9"}, {"description": "This value counts the number of milliseconds that IO requests have waited on this block device. If there are multiple IO requests waiting, this value will increase as the product of the number of milliseconds times the number of requests waiting. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockQueueTime_sdc"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU7"}, {"description": "The total amount of memory on the host system, in bytes.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_OSMemoryTotal"}, {"description": "Number of write operations requested from the block device and merged together by the OS IO scheduler. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteMerges_sdc"}, {"description": "Number of bytes read from the block device. It can be lower than the number of bytes read from the filesystem due to the usage of the OS page cache, that saves IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadBytes_sdc"}, {"description": "Number of threads in the server of the Prometheus endpoint. Note: prometheus endpoints can be also used via the usual HTTP/HTTPs ports.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_PrometheusThreads"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU7"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU14"}, {"description": "Time in seconds spend in discard operations requested from the block device, summed across all the operations. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockDiscardTime_sdb"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU7"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU8"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU7"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU2"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU7"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU7"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU8"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU11"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU8"}, {"description": "Total amount of data parts in tables of MergeTree family in the system database.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_TotalPartsOfMergeTreeTablesSystem"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU6"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU6"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU12"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU2"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU2"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU10"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU9"}, {"description": "The whole system load, averaged with exponential smoothing over 5 minutes. The load represents the number of threads across all the processes (the scheduling entities of the OS kernel), that are currently running by CPU or waiting for IO, or ready to run but not being scheduled at this point of time. This number includes all the processes, not only clickhouse-server. The number can be greater than the number of CPU cores, if the system is overloaded, and many processes are ready to run but waiting for CPU or IO.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_LoadAverage5"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU6"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU7"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU6"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_8"}, {"description": "The number of times the prediction of a hash table size was correct.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_HashTableStatsCacheHits"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU6"}, {"description": "Total size of mark cache in bytes", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_MarkCacheBytes"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU6"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU5"}, {"description": "The amount of virtual memory mapped for the use of stack and for the allocated memory, in bytes. It is unspecified whether it includes the per-thread stacks and most of the allocated memory, that is allocated with the 'mmap' system call. This metric exists only for completeness reasons. I recommend to use the `MemoryResident` metric for monitoring.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_MemoryDataAndStack"}, {"description": "The total number of inodes on the volume where the main ClickHouse path is mounted. If it is less than 25 million, it indicates a misconfiguration.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_FilesystemMainPathTotalINodes"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU3"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU5"}, {"description": "The value is similar to `OSStealTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSStealTimeNormalized"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU5"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU4"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU4"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU0"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTime"}, {"description": "Total number of tables in the system database on the server stored in tables of MergeTree family.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NumberOfTablesSystem"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU1"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU1"}, {"description": "The amount of memory used by the server process, that is also shared by another processes, in bytes. ClickHouse does not use shared memory, but some memory can be labeled by OS as shared for its own reasons. This metric does not make a lot of sense to watch, and it exists only for completeness reasons.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_MemoryShared"}, {"description": "The amount of memory used in cgroup, in bytes.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_CGroupMemoryUsed"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU3"}, {"description": "Total amount of rows (records) stored in all tables of MergeTree family.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_TotalRowsOfMergeTreeTables"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU4"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU4"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU11"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU15"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU4"}, {"description": "Number of bytes received via the network interface. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NetworkReceiveBytes_eth0"}, {"description": "Maximum number of parts per partition across all partitions of all tables of MergeTree family. Values larger than 300 indicates misconfiguration, overload, or massive data loading.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_MaxPartCountForPartition"}, {"description": "The total number of parts detached from MergeTree tables. A part can be detached by a user with the `ALTER TABLE DETACH` query or by the server itself it the part is broken, unexpected or unneeded. The server does not care about detached parts and they can be removed.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_NumberOfDetachedParts"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU4"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU15"}, {"description": "The temperature reported by the corresponding hardware monitor and the corresponding sensor in ℃. A sensor can return an unrealistic value. Source: `/sys/class/hwmon`", "type": "gauge", "unit": "℃.", "name": "ClickHouseAsyncMetrics_Temperature_k10temp_Tctl"}, {"description": "Total number of mark files cached in the mark cache for secondary indices.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_IndexMarkCacheFiles"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU4"}, {"description": "The number of used inodes on the volume where ClickHouse logs path is mounted.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_FilesystemLogsPathUsedINodes"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU3"}, {"description": "Number of read operations requested from the block device. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadOps_sdc"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU3"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU3"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU5"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTime"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU5"}, {"description": "The total number of opened files on the host machine. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSOpenFiles"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU10"}, {"description": "This value counts the number of I/O requests that have been issued to the device driver but have not yet completed. It does not include IO requests that are in the queue but not yet issued to the device driver. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockInFlightOps_sdb"}, {"description": "Number of threads in the server of the PostgreSQL compatibility protocol.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_PostgreSQLThreads"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU2"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU2"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU3"}, {"description": "The amount of free memory on the host system, in bytes. This does not include the memory used by the OS page cache memory, in bytes. The page cache memory is also available for usage by programs, so the value of this metric can be confusing. See the `OSMemoryAvailable` metric instead. For convenience we also provide the `OSMemoryFreePlusCached` metric, that should be somewhat similar to OSMemoryAvailable. See also https://www.linuxatemyram.com/. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_OSMemoryFreeWithoutCached"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU2"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU12"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU1"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU5"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU1"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU12"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU3"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU1"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU14"}, {"description": "The value is similar to `OSGuestNiceTime` but divided to the number of CPU cores to be measured in the [0..1] interval regardless of the number of cores. This allows you to average the values of this metric across multiple servers in a cluster even if the number of cores is non-uniform, and still get the average resource utilization metric.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeNormalized"}, {"description": "Total amount of bytes (compressed, including data and indices) stored in tables of MergeTree family in the system database.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_TotalBytesOfMergeTreeTablesSystem"}, {"description": "The amount of physical memory used by the server process, in bytes.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_MemoryResident"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU5"}, {"description": "Time in seconds spend in read operations requested from the block device, summed across all the operations. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockReadTime_sda"}, {"description": "Number of discard operations requested from the block device and merged together by the OS IO scheduler. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardMerges_sdb"}, {"description": "The total number of threads, as the OS kernel scheduler seeing it.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSThreadsTotal"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU0"}, {"description": "The current frequency of the CPU, in MHz. Most of the modern CPUs adjust the frequency dynamically for power saving and Turbo Boosting.", "type": "gauge", "unit": "Mhz", "name": "ClickHouseAsyncMetrics_CPUFrequencyMHz_13"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU9"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU3"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU3"}, {"description": "The size of the virtual address space allocated by the server process, in bytes. The size of the virtual address space is usually much greater than the physical memory consumption, and should not be used as an estimate for the memory consumption. The large values of this metric are totally normal, and makes only technical sense.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_MemoryVirtual"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU14"}, {"description": "Used bytes on the volume where the main ClickHouse path is mounted.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_FilesystemMainPathUsedBytes"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU0"}, {"description": "The number of available inodes on the volume where ClickHouse logs path is mounted.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_FilesystemLogsPathAvailableINodes"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU0"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU2"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU1"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU0"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU6"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU6"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTime"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU3"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTime"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU11"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU5"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTime"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTime"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU1"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU0"}, {"description": "The whole system load, averaged with exponential smoothing over 15 minutes. The load represents the number of threads across all the processes (the scheduling entities of the OS kernel), that are currently running by CPU or waiting for IO, or ready to run but not being scheduled at this point of time. This number includes all the processes, not only clickhouse-server. The number can be greater than the number of CPU cores, if the system is overloaded, and many processes are ready to run but waiting for CPU or IO.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_LoadAverage15"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU4"}, {"description": "Total number of mark files cached in the mark cache", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_MarkCacheFiles"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTime"}, {"description": "Number of write operations requested from the block device and merged together by the OS IO scheduler. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteMerges_sda"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU13"}, {"description": "The ratio of time spent for running hardware interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate hardware misconfiguration or a very high network load. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIrqTimeCPU0"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU13"}, {"description": "The uptime of the host server (the machine where ClickHouse is running), in seconds.", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_OSUptime"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel, when a guest was set to a higher priority (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestNiceTimeCPU14"}, {"description": "Time in seconds spend in read operations requested from the block device, summed across all the operations. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockReadTime_nvme0n1"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTime"}, {"description": "Number of threads blocked waiting for <PERSON><PERSON><PERSON> to complete (`man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSProcessesBlocked"}, {"description": "Time in seconds spend in write operations requested from the block device, summed across all the operations. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Seconds", "name": "ClickHouseAsyncMetrics_BlockWriteTime_sdc"}, {"description": "The total number of 'runnable' threads, as the OS kernel scheduler seeing it.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_OSThreadsRunnable"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTimeCPU5"}, {"description": "The whole system load, averaged with exponential smoothing over 1 minute. The load represents the number of threads across all the processes (the scheduling entities of the OS kernel), that are currently running by CPU or waiting for IO, or ready to run but not being scheduled at this point of time. This number includes all the processes, not only clickhouse-server. The number can be greater than the number of CPU cores, if the system is overloaded, and many processes are ready to run but waiting for CPU or IO.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_LoadAverage1"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU7"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU7"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU6"}, {"description": "Number of discarded bytes on the block device. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardBytes_sda"}, {"description": "The ratio of time the CPU core was running OS kernel (system) code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSystemTimeCPU4"}, {"description": "The amount of virtual memory mapped for the pages of machine code of the server process, in bytes.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_MemoryCode"}, {"description": "The ratio of time spent in other operating systems by the CPU when running in a virtualized environment. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Not every virtualized environments present this metric, and most of them don't. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSStealTime"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU2"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU1"}, {"description": "Number of discard operations requested from the block device. These operations are relevant for SSD. Discard operations are not used by ClickHouse, but can be used by other processes on the system. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockDiscardOps_sdb"}, {"description": "The ratio of time spent for running software interrupt requests on the CPU. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. A high number of this metric may indicate inefficient software running on the system. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSSoftIrqTimeCPU15"}, {"description": "Metrics update interval", "type": "gauge", "unit": "Microseconds", "name": "ClickHouseAsyncMetrics_AsynchronousMetricsUpdateInterval"}, {"description": "Number of bytes read from the block device. It can be lower than the number of bytes read from the filesystem due to the usage of the OS page cache, that saves IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadBytes_nvme0n1"}, {"description": "The number of entries in the cache of hash table sizes. The cache for hash table sizes is used for predictive optimization of GROUP BY.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_HashTableStatsCacheEntries"}, {"description": "The difference in time the thread for calculation of the asynchronous metrics was scheduled to wake up and the time it was in fact, woken up. A proxy-indicator of overall system latency and responsiveness.", "type": "gauge", "unit": "Microseconds", "name": "ClickHouseAsyncMetrics_Jitter"}, {"description": "Number of threads in the server of the MySQL compatibility protocol.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_MySQLThreads"}, {"description": "The ratio of time the CPU core was running userspace code with higher priority. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSNiceTimeCPU0"}, {"description": "The size of the volume where the main ClickHouse path is mounted, in bytes.", "type": "gauge", "unit": "Bytes", "name": "ClickHouseAsyncMetrics_FilesystemMainPathTotalBytes"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU13"}, {"description": "The ratio of time the CPU core was running userspace code. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This includes also the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSUserTimeCPU7"}, {"description": "An internal incremental update number of the statistics of jemalloc (<PERSON>' memory allocator), used in all other `jemalloc` metrics.", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_jemalloc_epoch"}, {"description": "Number of read operations requested from the block device. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockReadOps_sda"}, {"description": "The ratio of time the CPU core was not running the code but when the OS kernel did not run any other process on this CPU as the processes were waiting for IO. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIOWaitTimeCPU0"}, {"description": "The ratio of time the CPU core was idle (not even ready to run a process waiting for I<PERSON>) from the OS kernel standpoint. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This does not include the time when the CPU was under-utilized due to the reasons internal to the CPU (memory loads, pipeline stalls, branch mispredictions, running another SMT core). The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSIdleTimeCPU2"}, {"description": "The ratio of time spent running a virtual CPU for guest operating systems under the control of the Linux kernel (See `man procfs`). This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. This metric is irrelevant for ClickHouse, but still exists for completeness. The value for a single CPU core will be in the interval [0..1]. The value for all CPU cores is calculated as a sum across them [0..num cores].", "type": "gauge", "unit": "Fraction", "name": "ClickHouseAsyncMetrics_OSGuestTimeCPU1"}, {"description": "Number of bytes written to the block device. It can be lower than the number of bytes written to the filesystem due to the usage of the OS page cache, that saves IO. A write to the block device may happen later than the corresponding write to the filesystem due to write-through caching. This is a system-wide metric, it includes all the processes on the host machine, not just clickhouse-server. Source: `/sys/block`. See https://www.kernel.org/doc/Documentation/block/stat.txt", "type": "gauge", "unit": "Number", "name": "ClickHouseAsyncMetrics_BlockWriteBytes_sda"}, {"description": "The number of OK errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_OK"}, {"description": "The number of UNSUPPORTED_METHOD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNSUPPORTED_METHOD"}, {"description": "The number of UNSUPPORTED_PARAMETER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNSUPPORTED_PARAMETER"}, {"description": "The number of UNEXPECTED_END_OF_FILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNEXPECTED_END_OF_FILE"}, {"description": "The number of EXPECTED_END_OF_FILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EXPECTED_END_OF_FILE"}, {"description": "The number of CANNOT_PARSE_TEXT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_TEXT"}, {"description": "The number of INCORRECT_NUMBER_OF_COLUMNS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_NUMBER_OF_COLUMNS"}, {"description": "The number of THERE_IS_NO_COLUMN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_THERE_IS_NO_COLUMN"}, {"description": "The number of SIZES_OF_COLUMNS_DOESNT_MATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SIZES_OF_COLUMNS_DOESNT_MATCH"}, {"description": "The number of NOT_FOUND_COLUMN_IN_BLOCK errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NOT_FOUND_COLUMN_IN_BLOCK"}, {"description": "The number of POSITION_OUT_OF_BOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_POSITION_OUT_OF_BOUND"}, {"description": "The number of PARAMETER_OUT_OF_BOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PARAMETER_OUT_OF_BOUND"}, {"description": "The number of SIZES_OF_COLUMNS_IN_TUPLE_DOESNT_MATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SIZES_OF_COLUMNS_IN_TUPLE_DOESNT_MATCH"}, {"description": "The number of DUPLICATE_COLUMN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DUPLICATE_COLUMN"}, {"description": "The number of NO_SUCH_COLUMN_IN_TABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_SUCH_COLUMN_IN_TABLE"}, {"description": "The number of SIZE_OF_FIXED_STRING_DOESNT_MATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SIZE_OF_FIXED_STRING_DOESNT_MATCH"}, {"description": "The number of NUMBER_OF_COLUMNS_DOESNT_MATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NUMBER_OF_COLUMNS_DOESNT_MATCH"}, {"description": "The number of CANNOT_READ_FROM_ISTREAM errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_READ_FROM_ISTREAM"}, {"description": "The number of CANNOT_WRITE_TO_OSTREAM errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_WRITE_TO_OSTREAM"}, {"description": "The number of CANNOT_PARSE_ESCAPE_SEQUENCE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_ESCAPE_SEQUENCE"}, {"description": "The number of CANNOT_PARSE_QUOTED_STRING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_QUOTED_STRING"}, {"description": "The number of CANNOT_PARSE_INPUT_ASSERTION_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_INPUT_ASSERTION_FAILED"}, {"description": "The number of CANNOT_PRINT_FLOAT_OR_DOUBLE_NUMBER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PRINT_FLOAT_OR_DOUBLE_NUMBER"}, {"description": "The number of ATTEMPT_TO_READ_AFTER_EOF errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ATTEMPT_TO_READ_AFTER_EOF"}, {"description": "The number of CANNOT_READ_ALL_DATA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_READ_ALL_DATA"}, {"description": "The number of TOO_MANY_ARGUMENTS_FOR_FUNCTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_ARGUMENTS_FOR_FUNCTION"}, {"description": "The number of TOO_FEW_ARGUMENTS_FOR_FUNCTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_FEW_ARGUMENTS_FOR_FUNCTION"}, {"description": "The number of BAD_ARGUMENTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_ARGUMENTS"}, {"description": "The number of UNKNOWN_ELEMENT_IN_AST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_ELEMENT_IN_AST"}, {"description": "The number of CANNOT_PARSE_DATE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_DATE"}, {"description": "The number of TOO_LARGE_SIZE_COMPRESSED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_LARGE_SIZE_COMPRESSED"}, {"description": "The number of CHECKSUM_DOESNT_MATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CHECKSUM_DOESNT_MATCH"}, {"description": "The number of CANNOT_PARSE_DATETIME errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_DATETIME"}, {"description": "The number of NUMBER_OF_ARGUMENTS_DOESNT_MATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NUMBER_OF_ARGUMENTS_DOESNT_MATCH"}, {"description": "The number of ILLEGAL_TYPE_OF_ARGUMENT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_TYPE_OF_ARGUMENT"}, {"description": "The number of ILLEGAL_COLUMN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_COLUMN"}, {"description": "The number of UNKNOWN_FUNCTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_FUNCTION"}, {"description": "The number of UNKNOWN_IDENTIFIER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_IDENTIFIER"}, {"description": "The number of NOT_IMPLEMENTED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NOT_IMPLEMENTED"}, {"description": "The number of LOGICAL_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_LOGICAL_ERROR"}, {"description": "The number of UNKNOWN_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_TYPE"}, {"description": "The number of EMPTY_LIST_OF_COLUMNS_QUERIED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EMPTY_LIST_OF_COLUMNS_QUERIED"}, {"description": "The number of COLUMN_QUERIED_MORE_THAN_ONCE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_COLUMN_QUERIED_MORE_THAN_ONCE"}, {"description": "The number of TYPE_MISMATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TYPE_MISMATCH"}, {"description": "The number of STORAGE_REQUIRES_PARAMETER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_STORAGE_REQUIRES_PARAMETER"}, {"description": "The number of UNKNOWN_STORAGE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_STORAGE"}, {"description": "The number of TABLE_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TABLE_ALREADY_EXISTS"}, {"description": "The number of TABLE_METADATA_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TABLE_METADATA_ALREADY_EXISTS"}, {"description": "The number of ILLEGAL_TYPE_OF_COLUMN_FOR_FILTER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_TYPE_OF_COLUMN_FOR_FILTER"}, {"description": "The number of UNKNOWN_TABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_TABLE"}, {"description": "The number of SYNTAX_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SYNTAX_ERROR"}, {"description": "The number of UNKNOWN_AGGREGATE_FUNCTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_AGGREGATE_FUNCTION"}, {"description": "The number of CANNOT_GET_SIZE_OF_FIELD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_GET_SIZE_OF_FIELD"}, {"description": "The number of ARGUMENT_OUT_OF_BOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ARGUMENT_OUT_OF_BOUND"}, {"description": "The number of CANNOT_CONVERT_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CONVERT_TYPE"}, {"description": "The number of CANNOT_WRITE_AFTER_END_OF_BUFFER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_WRITE_AFTER_END_OF_BUFFER"}, {"description": "The number of CANNOT_PARSE_NUMBER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_NUMBER"}, {"description": "The number of UNKNOWN_FORMAT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_FORMAT"}, {"description": "The number of CANNOT_READ_FROM_FILE_DESCRIPTOR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_READ_FROM_FILE_DESCRIPTOR"}, {"description": "The number of CANNOT_WRITE_TO_FILE_DESCRIPTOR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_WRITE_TO_FILE_DESCRIPTOR"}, {"description": "The number of CANNOT_OPEN_FILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_OPEN_FILE"}, {"description": "The number of CANNOT_CLOSE_FILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CLOSE_FILE"}, {"description": "The number of UNKNOWN_TYPE_OF_QUERY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_TYPE_OF_QUERY"}, {"description": "The number of INCORRECT_FILE_NAME errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_FILE_NAME"}, {"description": "The number of INCORRECT_QUERY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_QUERY"}, {"description": "The number of UNKNOWN_DATABASE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_DATABASE"}, {"description": "The number of DATABASE_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATABASE_ALREADY_EXISTS"}, {"description": "The number of DIRECTORY_DOESNT_EXIST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DIRECTORY_DOESNT_EXIST"}, {"description": "The number of DIRECTORY_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DIRECTORY_ALREADY_EXISTS"}, {"description": "The number of FORMAT_IS_NOT_SUITABLE_FOR_INPUT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FORMAT_IS_NOT_SUITABLE_FOR_INPUT"}, {"description": "The number of RECEIVED_ERROR_FROM_REMOTE_IO_SERVER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_RECEIVED_ERROR_FROM_REMOTE_IO_SERVER"}, {"description": "The number of CANNOT_SEEK_THROUGH_FILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SEEK_THROUGH_FILE"}, {"description": "The number of CANNOT_TRUNCATE_FILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_TRUNCATE_FILE"}, {"description": "The number of UNKNOWN_COMPRESSION_METHOD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_COMPRESSION_METHOD"}, {"description": "The number of EMPTY_LIST_OF_COLUMNS_PASSED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EMPTY_LIST_OF_COLUMNS_PASSED"}, {"description": "The number of SIZES_OF_MARKS_FILES_ARE_INCONSISTENT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SIZES_OF_MARKS_FILES_ARE_INCONSISTENT"}, {"description": "The number of EMPTY_DATA_PASSED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EMPTY_DATA_PASSED"}, {"description": "The number of UNKNOWN_AGGREGATED_DATA_VARIANT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_AGGREGATED_DATA_VARIANT"}, {"description": "The number of CANNOT_MERGE_DIFFERENT_AGGREGATED_DATA_VARIANTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_MERGE_DIFFERENT_AGGREGATED_DATA_VARIANTS"}, {"description": "The number of CANNOT_READ_FROM_SOCKET errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_READ_FROM_SOCKET"}, {"description": "The number of CANNOT_WRITE_TO_SOCKET errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_WRITE_TO_SOCKET"}, {"description": "The number of UNKNOWN_PACKET_FROM_CLIENT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_PACKET_FROM_CLIENT"}, {"description": "The number of UNKNOWN_PACKET_FROM_SERVER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_PACKET_FROM_SERVER"}, {"description": "The number of UNEXPECTED_PACKET_FROM_CLIENT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNEXPECTED_PACKET_FROM_CLIENT"}, {"description": "The number of UNEXPECTED_PACKET_FROM_SERVER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNEXPECTED_PACKET_FROM_SERVER"}, {"description": "The number of TOO_SMALL_BUFFER_SIZE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_SMALL_BUFFER_SIZE"}, {"description": "The number of FILE_DOESNT_EXIST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FILE_DOESNT_EXIST"}, {"description": "The number of NO_DATA_TO_INSERT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_DATA_TO_INSERT"}, {"description": "The number of CANNOT_BLOCK_SIGNAL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_BLOCK_SIGNAL"}, {"description": "The number of CANNOT_UNBLOCK_SIGNAL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_UNBLOCK_SIGNAL"}, {"description": "The number of CANNOT_MANIPULATE_SIGSET errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_MANIPULATE_SIGSET"}, {"description": "The number of CANNOT_WAIT_FOR_SIGNAL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_WAIT_FOR_SIGNAL"}, {"description": "The number of THERE_IS_NO_SESSION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_THERE_IS_NO_SESSION"}, {"description": "The number of CANNOT_CLOCK_GETTIME errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CLOCK_GETTIME"}, {"description": "The number of UNKNOWN_SETTING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_SETTING"}, {"description": "The number of THERE_IS_NO_DEFAULT_VALUE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_THERE_IS_NO_DEFAULT_VALUE"}, {"description": "The number of INCORRECT_DATA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_DATA"}, {"description": "The number of ENGINE_REQUIRED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ENGINE_REQUIRED"}, {"description": "The number of CANNOT_INSERT_VALUE_OF_DIFFERENT_SIZE_INTO_TUPLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_INSERT_VALUE_OF_DIFFERENT_SIZE_INTO_TUPLE"}, {"description": "The number of UNSUPPORTED_JOIN_KEYS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNSUPPORTED_JOIN_KEYS"}, {"description": "The number of INCOMPATIBLE_COLUMNS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCOMPATIBLE_COLUMNS"}, {"description": "The number of UNKNOWN_TYPE_OF_AST_NODE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_TYPE_OF_AST_NODE"}, {"description": "The number of INCORRECT_ELEMENT_OF_SET errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_ELEMENT_OF_SET"}, {"description": "The number of INCORRECT_RESULT_OF_SCALAR_SUBQUERY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_RESULT_OF_SCALAR_SUBQUERY"}, {"description": "The number of ILLEGAL_INDEX errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_INDEX"}, {"description": "The number of TOO_LARGE_ARRAY_SIZE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_LARGE_ARRAY_SIZE"}, {"description": "The number of FUNCTION_IS_SPECIAL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FUNCTION_IS_SPECIAL"}, {"description": "The number of CANNOT_READ_ARRAY_FROM_TEXT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_READ_ARRAY_FROM_TEXT"}, {"description": "The number of TOO_LARGE_STRING_SIZE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_LARGE_STRING_SIZE"}, {"description": "The number of AGGREGATE_FUNCTION_DOESNT_ALLOW_PARAMETERS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_AGGREGATE_FUNCTION_DOESNT_ALLOW_PARAMETERS"}, {"description": "The number of PARAMETERS_TO_AGGREGATE_FUNCTIONS_MUST_BE_LITERALS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PARAMETERS_TO_AGGREGATE_FUNCTIONS_MUST_BE_LITERALS"}, {"description": "The number of ZERO_ARRAY_OR_TUPLE_INDEX errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ZERO_ARRAY_OR_TUPLE_INDEX"}, {"description": "The number of UNKNOWN_ELEMENT_IN_CONFIG errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_ELEMENT_IN_CONFIG"}, {"description": "The number of EXCESSIVE_ELEMENT_IN_CONFIG errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EXCESSIVE_ELEMENT_IN_CONFIG"}, {"description": "The number of NO_ELEMENTS_IN_CONFIG errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_ELEMENTS_IN_CONFIG"}, {"description": "The number of SAMPLING_NOT_SUPPORTED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SAMPLING_NOT_SUPPORTED"}, {"description": "The number of NOT_FOUND_NODE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NOT_FOUND_NODE"}, {"description": "The number of UNKNOWN_OVERFLOW_MODE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_OVERFLOW_MODE"}, {"description": "The number of UNKNOWN_DIRECTION_OF_SORTING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_DIRECTION_OF_SORTING"}, {"description": "The number of ILLEGAL_DIVISION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_DIVISION"}, {"description": "The number of DICTIONARIES_WAS_NOT_LOADED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DICTIONARIES_WAS_NOT_LOADED"}, {"description": "The number of TOO_MANY_ROWS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_ROWS"}, {"description": "The number of TIMEOUT_EXCEEDED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TIMEOUT_EXCEEDED"}, {"description": "The number of TOO_SLOW errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_SLOW"}, {"description": "The number of TOO_MANY_COLUMNS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_COLUMNS"}, {"description": "The number of TOO_DEEP_SUBQUERIES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_DEEP_SUBQUERIES"}, {"description": "The number of READONLY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_READONLY"}, {"description": "The number of TOO_MANY_TEMPORARY_COLUMNS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_TEMPORARY_COLUMNS"}, {"description": "The number of TOO_MANY_TEMPORARY_NON_CONST_COLUMNS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_TEMPORARY_NON_CONST_COLUMNS"}, {"description": "The number of TOO_DEEP_AST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_DEEP_AST"}, {"description": "The number of TOO_BIG_AST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_BIG_AST"}, {"description": "The number of BAD_TYPE_OF_FIELD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_TYPE_OF_FIELD"}, {"description": "The number of BAD_GET errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_GET"}, {"description": "The number of CANNOT_CREATE_DIRECTORY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CREATE_DIRECTORY"}, {"description": "The number of CANNOT_ALLOCATE_MEMORY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_ALLOCATE_MEMORY"}, {"description": "The number of CYCLIC_ALIASES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CYCLIC_ALIASES"}, {"description": "The number of MULTIPLE_EXPRESSIONS_FOR_ALIAS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MULTIPLE_EXPRESSIONS_FOR_ALIAS"}, {"description": "The number of THERE_IS_NO_PROFILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_THERE_IS_NO_PROFILE"}, {"description": "The number of ILLEGAL_FINAL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_FINAL"}, {"description": "The number of ILLEGAL_PREWHERE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_PREWHERE"}, {"description": "The number of UNEXPECTED_EXPRESSION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNEXPECTED_EXPRESSION"}, {"description": "The number of ILLEGAL_AGGREGATION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_AGGREGATION"}, {"description": "The number of UNSUPPORTED_COLLATION_LOCALE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNSUPPORTED_COLLATION_LOCALE"}, {"description": "The number of COLLATION_COMPARISON_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_COLLATION_COMPARISON_FAILED"}, {"description": "The number of SIZES_OF_ARRAYS_DONT_MATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SIZES_OF_ARRAYS_DONT_MATCH"}, {"description": "The number of SET_SIZE_LIMIT_EXCEEDED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SET_SIZE_LIMIT_EXCEEDED"}, {"description": "The number of UNKNOWN_USER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_USER"}, {"description": "The number of WRONG_PASSWORD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_WRONG_PASSWORD"}, {"description": "The number of REQUIRED_PASSWORD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_REQUIRED_PASSWORD"}, {"description": "The number of IP_ADDRESS_NOT_ALLOWED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_IP_ADDRESS_NOT_ALLOWED"}, {"description": "The number of UNKNOWN_ADDRESS_PATTERN_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_ADDRESS_PATTERN_TYPE"}, {"description": "The number of DNS_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DNS_ERROR"}, {"description": "The number of UNKNOWN_QUOTA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_QUOTA"}, {"description": "The number of QUOTA_EXCEEDED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUOTA_EXCEEDED"}, {"description": "The number of TOO_MANY_SIMULTANEOUS_QUERIES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_SIMULTANEOUS_QUERIES"}, {"description": "The number of NO_FREE_CONNECTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_FREE_CONNECTION"}, {"description": "The number of CANNOT_FSYNC errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_FSYNC"}, {"description": "The number of ALIAS_REQUIRED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ALIAS_REQUIRED"}, {"description": "The number of AMBIGUOUS_IDENTIFIER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_AMBIGUOUS_IDENTIFIER"}, {"description": "The number of EMPTY_NESTED_TABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EMPTY_NESTED_TABLE"}, {"description": "The number of SOCKET_TIMEOUT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SOCKET_TIMEOUT"}, {"description": "The number of NETWORK_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NETWORK_ERROR"}, {"description": "The number of EMPTY_QUERY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EMPTY_QUERY"}, {"description": "The number of UNKNOWN_LOAD_BALANCING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_LOAD_BALANCING"}, {"description": "The number of UNKNOWN_TOTALS_MODE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_TOTALS_MODE"}, {"description": "The number of CANNOT_STATVFS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_STATVFS"}, {"description": "The number of NOT_AN_AGGREGATE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NOT_AN_AGGREGATE"}, {"description": "The number of QUERY_WITH_SAME_ID_IS_ALREADY_RUNNING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUERY_WITH_SAME_ID_IS_ALREADY_RUNNING"}, {"description": "The number of CLIENT_HAS_CONNECTED_TO_WRONG_PORT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CLIENT_HAS_CONNECTED_TO_WRONG_PORT"}, {"description": "The number of TABLE_IS_DROPPED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TABLE_IS_DROPPED"}, {"description": "The number of DATABASE_NOT_EMPTY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATABASE_NOT_EMPTY"}, {"description": "The number of DUPLICATE_INTERSERVER_IO_ENDPOINT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DUPLICATE_INTERSERVER_IO_ENDPOINT"}, {"description": "The number of NO_SUCH_INTERSERVER_IO_ENDPOINT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_SUCH_INTERSERVER_IO_ENDPOINT"}, {"description": "The number of UNEXPECTED_AST_STRUCTURE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNEXPECTED_AST_STRUCTURE"}, {"description": "The number of REPLICA_IS_ALREADY_ACTIVE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_REPLICA_IS_ALREADY_ACTIVE"}, {"description": "The number of NO_ZOOKEEPER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_ZOOKEEPER"}, {"description": "The number of NO_FILE_IN_DATA_PART errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_FILE_IN_DATA_PART"}, {"description": "The number of UNEXPECTED_FILE_IN_DATA_PART errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNEXPECTED_FILE_IN_DATA_PART"}, {"description": "The number of BAD_SIZE_OF_FILE_IN_DATA_PART errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_SIZE_OF_FILE_IN_DATA_PART"}, {"description": "The number of QUERY_IS_TOO_LARGE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUERY_IS_TOO_LARGE"}, {"description": "The number of NOT_FOUND_EXPECTED_DATA_PART errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NOT_FOUND_EXPECTED_DATA_PART"}, {"description": "The number of TOO_MANY_UNEXPECTED_DATA_PARTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_UNEXPECTED_DATA_PARTS"}, {"description": "The number of NO_SUCH_DATA_PART errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_SUCH_DATA_PART"}, {"description": "The number of BAD_DATA_PART_NAME errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_DATA_PART_NAME"}, {"description": "The number of NO_REPLICA_HAS_PART errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_REPLICA_HAS_PART"}, {"description": "The number of DUPLICATE_DATA_PART errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DUPLICATE_DATA_PART"}, {"description": "The number of ABORTED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ABORTED"}, {"description": "The number of NO_REPLICA_NAME_GIVEN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_REPLICA_NAME_GIVEN"}, {"description": "The number of FORMAT_VERSION_TOO_OLD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FORMAT_VERSION_TOO_OLD"}, {"description": "The number of CANNOT_MUNMAP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_MUNMAP"}, {"description": "The number of CANNOT_MREMAP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_MREMAP"}, {"description": "The number of MEMORY_LIMIT_EXCEEDED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MEMORY_LIMIT_EXCEEDED"}, {"description": "The number of TABLE_IS_READ_ONLY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TABLE_IS_READ_ONLY"}, {"description": "The number of NOT_ENOUGH_SPACE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NOT_ENOUGH_SPACE"}, {"description": "The number of UNEXPECTED_ZOOKEEPER_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNEXPECTED_ZOOKEEPER_ERROR"}, {"description": "The number of CORRUPTED_DATA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CORRUPTED_DATA"}, {"description": "The number of INVALID_PARTITION_VALUE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_PARTITION_VALUE"}, {"description": "The number of NO_SUCH_REPLICA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_SUCH_REPLICA"}, {"description": "The number of TOO_MANY_PARTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_PARTS"}, {"description": "The number of REPLICA_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_REPLICA_ALREADY_EXISTS"}, {"description": "The number of NO_ACTIVE_REPLICAS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_ACTIVE_REPLICAS"}, {"description": "The number of TOO_MANY_RETRIES_TO_FETCH_PARTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_RETRIES_TO_FETCH_PARTS"}, {"description": "The number of PARTITION_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PARTITION_ALREADY_EXISTS"}, {"description": "The number of PARTITION_DOESNT_EXIST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PARTITION_DOESNT_EXIST"}, {"description": "The number of UNION_ALL_RESULT_STRUCTURES_MISMATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNION_ALL_RESULT_STRUCTURES_MISMATCH"}, {"description": "The number of CLIENT_OUTPUT_FORMAT_SPECIFIED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CLIENT_OUTPUT_FORMAT_SPECIFIED"}, {"description": "The number of UNKNOWN_BLOCK_INFO_FIELD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_BLOCK_INFO_FIELD"}, {"description": "The number of BAD_COLLATION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_COLLATION"}, {"description": "The number of CANNOT_COMPILE_CODE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_COMPILE_CODE"}, {"description": "The number of INCOMPATIBLE_TYPE_OF_JOIN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCOMPATIBLE_TYPE_OF_JOIN"}, {"description": "The number of NO_AVAILABLE_REPLICA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_AVAILABLE_REPLICA"}, {"description": "The number of MISMATCH_REPLICAS_DATA_SOURCES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MISMATCH_REPLICAS_DATA_SOURCES"}, {"description": "The number of INFINITE_LOOP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INFINITE_LOOP"}, {"description": "The number of CANNOT_COMPRESS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_COMPRESS"}, {"description": "The number of CANNOT_DECOMPRESS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_DECOMPRESS"}, {"description": "The number of CANNOT_IO_SUBMIT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_IO_SUBMIT"}, {"description": "The number of CANNOT_IO_GETEVENTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_IO_GETEVENTS"}, {"description": "The number of AIO_READ_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_AIO_READ_ERROR"}, {"description": "The number of AIO_WRITE_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_AIO_WRITE_ERROR"}, {"description": "The number of INDEX_NOT_USED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INDEX_NOT_USED"}, {"description": "The number of ALL_CONNECTION_TRIES_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ALL_CONNECTION_TRIES_FAILED"}, {"description": "The number of NO_AVAILABLE_DATA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_AVAILABLE_DATA"}, {"description": "The number of DICTIONARY_IS_EMPTY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DICTIONARY_IS_EMPTY"}, {"description": "The number of INCORRECT_INDEX errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_INDEX"}, {"description": "The number of UNKNOWN_DISTRIBUTED_PRODUCT_MODE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_DISTRIBUTED_PRODUCT_MODE"}, {"description": "The number of WRONG_GLOBAL_SUBQUERY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_WRONG_GLOBAL_SUBQUERY"}, {"description": "The number of TOO_FEW_LIVE_REPLICAS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_FEW_LIVE_REPLICAS"}, {"description": "The number of UNSATISFIED_QUORUM_FOR_PREVIOUS_WRITE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNSATISFIED_QUORUM_FOR_PREVIOUS_WRITE"}, {"description": "The number of UNKNOWN_FORMAT_VERSION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_FORMAT_VERSION"}, {"description": "The number of DISTRIBUTED_IN_JOIN_SUBQUERY_DENIED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DISTRIBUTED_IN_JOIN_SUBQUERY_DENIED"}, {"description": "The number of REPLICA_IS_NOT_IN_QUORUM errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_REPLICA_IS_NOT_IN_QUORUM"}, {"description": "The number of LIMIT_EXCEEDED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_LIMIT_EXCEEDED"}, {"description": "The number of DATABASE_ACCESS_DENIED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATABASE_ACCESS_DENIED"}, {"description": "The number of MONGODB_CANNOT_AUTHENTICATE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MONGODB_CANNOT_AUTHENTICATE"}, {"description": "The number of RECEIVED_EMPTY_DATA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_RECEIVED_EMPTY_DATA"}, {"description": "The number of SHARD_HAS_NO_CONNECTIONS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SHARD_HAS_NO_CONNECTIONS"}, {"description": "The number of CANNOT_PIPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PIPE"}, {"description": "The number of CANNOT_FORK errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_FORK"}, {"description": "The number of CANNOT_DLSYM errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_DLSYM"}, {"description": "The number of CANNOT_CREATE_CHILD_PROCESS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CREATE_CHILD_PROCESS"}, {"description": "The number of CHILD_WAS_NOT_EXITED_NORMALLY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CHILD_WAS_NOT_EXITED_NORMALLY"}, {"description": "The number of CANNOT_SELECT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SELECT"}, {"description": "The number of CANNOT_WAITPID errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_WAITPID"}, {"description": "The number of TABLE_WAS_NOT_DROPPED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TABLE_WAS_NOT_DROPPED"}, {"description": "The number of TOO_DEEP_RECURSION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_DEEP_RECURSION"}, {"description": "The number of TOO_MANY_BYTES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_BYTES"}, {"description": "The number of UNEXPECTED_NODE_IN_ZOOKEEPER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNEXPECTED_NODE_IN_ZOOKEEPER"}, {"description": "The number of FUNCTION_CANNOT_HAVE_PARAMETERS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FUNCTION_CANNOT_HAVE_PARAMETERS"}, {"description": "The number of INVALID_CONFIG_PARAMETER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_CONFIG_PARAMETER"}, {"description": "The number of UNKNOWN_STATUS_OF_INSERT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_STATUS_OF_INSERT"}, {"description": "The number of VALUE_IS_OUT_OF_RANGE_OF_DATA_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_VALUE_IS_OUT_OF_RANGE_OF_DATA_TYPE"}, {"description": "The number of UNKNOWN_DATABASE_ENGINE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_DATABASE_ENGINE"}, {"description": "The number of UNFINISHED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNFINISHED"}, {"description": "The number of METADATA_MISMATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_METADATA_MISMATCH"}, {"description": "The number of SUPPORT_IS_DISABLED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SUPPORT_IS_DISABLED"}, {"description": "The number of TABLE_DIFFERS_TOO_MUCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TABLE_DIFFERS_TOO_MUCH"}, {"description": "The number of CANNOT_CONVERT_CHARSET errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CONVERT_CHARSET"}, {"description": "The number of CANNOT_LOAD_CONFIG errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_LOAD_CONFIG"}, {"description": "The number of CANNOT_INSERT_NULL_IN_ORDINARY_COLUMN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_INSERT_NULL_IN_ORDINARY_COLUMN"}, {"description": "The number of AMBIGUOUS_COLUMN_NAME errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_AMBIGUOUS_COLUMN_NAME"}, {"description": "The number of INDEX_OF_POSITIONAL_ARGUMENT_IS_OUT_OF_RANGE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INDEX_OF_POSITIONAL_ARGUMENT_IS_OUT_OF_RANGE"}, {"description": "The number of ZLIB_INFLATE_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ZLIB_INFLATE_FAILED"}, {"description": "The number of ZLIB_DEFLATE_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ZLIB_DEFLATE_FAILED"}, {"description": "The number of INTO_OUTFILE_NOT_ALLOWED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INTO_OUTFILE_NOT_ALLOWED"}, {"description": "The number of TABLE_SIZE_EXCEEDS_MAX_DROP_SIZE_LIMIT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TABLE_SIZE_EXCEEDS_MAX_DROP_SIZE_LIMIT"}, {"description": "The number of CANNOT_CREATE_CHARSET_CONVERTER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CREATE_CHARSET_CONVERTER"}, {"description": "The number of SEEK_POSITION_OUT_OF_BOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SEEK_POSITION_OUT_OF_BOUND"}, {"description": "The number of CURRENT_WRITE_BUFFER_IS_EXHAUSTED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CURRENT_WRITE_BUFFER_IS_EXHAUSTED"}, {"description": "The number of CANNOT_CREATE_IO_BUFFER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CREATE_IO_BUFFER"}, {"description": "The number of RECEIVED_ERROR_TOO_MANY_REQUESTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_RECEIVED_ERROR_TOO_MANY_REQUESTS"}, {"description": "The number of SIZES_OF_NESTED_COLUMNS_ARE_INCONSISTENT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SIZES_OF_NESTED_COLUMNS_ARE_INCONSISTENT"}, {"description": "The number of ALL_REPLICAS_ARE_STALE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ALL_REPLICAS_ARE_STALE"}, {"description": "The number of DATA_TYPE_CANNOT_BE_USED_IN_TABLES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATA_TYPE_CANNOT_BE_USED_IN_TABLES"}, {"description": "The number of INCONSISTENT_CLUSTER_DEFINITION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCONSISTENT_CLUSTER_DEFINITION"}, {"description": "The number of SESSION_NOT_FOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SESSION_NOT_FOUND"}, {"description": "The number of SESSION_IS_LOCKED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SESSION_IS_LOCKED"}, {"description": "The number of INVALID_SESSION_TIMEOUT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_SESSION_TIMEOUT"}, {"description": "The number of CANNOT_DLOPEN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_DLOPEN"}, {"description": "The number of CANNOT_PARSE_UUID errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_UUID"}, {"description": "The number of ILLEGAL_SYNTAX_FOR_DATA_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_SYNTAX_FOR_DATA_TYPE"}, {"description": "The number of DATA_TYPE_CANNOT_HAVE_ARGUMENTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATA_TYPE_CANNOT_HAVE_ARGUMENTS"}, {"description": "The number of CANNOT_KILL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_KILL"}, {"description": "The number of HTTP_LENGTH_REQUIRED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_HTTP_LENGTH_REQUIRED"}, {"description": "The number of CANNOT_LOAD_CATBOOST_MODEL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_LOAD_CATBOOST_MODEL"}, {"description": "The number of CANNOT_APPLY_CATBOOST_MODEL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_APPLY_CATBOOST_MODEL"}, {"description": "The number of PART_IS_TEMPORARILY_LOCKED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PART_IS_TEMPORARILY_LOCKED"}, {"description": "The number of MULTIPLE_STREAMS_REQUIRED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MULTIPLE_STREAMS_REQUIRED"}, {"description": "The number of NO_COMMON_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_COMMON_TYPE"}, {"description": "The number of DICTIONARY_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DICTIONARY_ALREADY_EXISTS"}, {"description": "The number of CANNOT_ASSIGN_OPTIMIZE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_ASSIGN_OPTIMIZE"}, {"description": "The number of INSERT_WAS_DEDUPLICATED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INSERT_WAS_DEDUPLICATED"}, {"description": "The number of CANNOT_GET_CREATE_TABLE_QUERY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_GET_CREATE_TABLE_QUERY"}, {"description": "The number of EXTERNAL_LIBRARY_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EXTERNAL_LIBRARY_ERROR"}, {"description": "The number of QUERY_IS_PROHIBITED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUERY_IS_PROHIBITED"}, {"description": "The number of THERE_IS_NO_QUERY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_THERE_IS_NO_QUERY"}, {"description": "The number of QUERY_WAS_CANCELLED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUERY_WAS_CANCELLED"}, {"description": "The number of FUNCTION_THROW_IF_VALUE_IS_NON_ZERO errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FUNCTION_THROW_IF_VALUE_IS_NON_ZERO"}, {"description": "The number of TOO_MANY_ROWS_OR_BYTES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_ROWS_OR_BYTES"}, {"description": "The number of QUERY_IS_NOT_SUPPORTED_IN_MATERIALIZED_VIEW errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUERY_IS_NOT_SUPPORTED_IN_MATERIALIZED_VIEW"}, {"description": "The number of UNKNOWN_MUTATION_COMMAND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_MUTATION_COMMAND"}, {"description": "The number of FORMAT_IS_NOT_SUITABLE_FOR_OUTPUT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FORMAT_IS_NOT_SUITABLE_FOR_OUTPUT"}, {"description": "The number of CANNOT_STAT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_STAT"}, {"description": "The number of FEATURE_IS_NOT_ENABLED_AT_BUILD_TIME errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FEATURE_IS_NOT_ENABLED_AT_BUILD_TIME"}, {"description": "The number of CANNOT_IOSETUP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_IOSETUP"}, {"description": "The number of INVALID_JOIN_ON_EXPRESSION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_JOIN_ON_EXPRESSION"}, {"description": "The number of BAD_ODBC_CONNECTION_STRING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_ODBC_CONNECTION_STRING"}, {"description": "The number of TOP_AND_LIMIT_TOGETHER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOP_AND_LIMIT_TOGETHER"}, {"description": "The number of DECIMAL_OVERFLOW errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DECIMAL_OVERFLOW"}, {"description": "The number of BAD_REQUEST_PARAMETER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_REQUEST_PARAMETER"}, {"description": "The number of EXTERNAL_SERVER_IS_NOT_RESPONDING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EXTERNAL_SERVER_IS_NOT_RESPONDING"}, {"description": "The number of PTHREAD_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PTHREAD_ERROR"}, {"description": "The number of NETLINK_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NETLINK_ERROR"}, {"description": "The number of CANNOT_SET_SIGNAL_HANDLER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SET_SIGNAL_HANDLER"}, {"description": "The number of ALL_REPLICAS_LOST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ALL_REPLICAS_LOST"}, {"description": "The number of REPLICA_STATUS_CHANGED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_REPLICA_STATUS_CHANGED"}, {"description": "The number of EXPECTED_ALL_OR_ANY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EXPECTED_ALL_OR_ANY"}, {"description": "The number of UNKNOWN_JOIN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_JOIN"}, {"description": "The number of MULTIPLE_ASSIGNMENTS_TO_COLUMN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MULTIPLE_ASSIGNMENTS_TO_COLUMN"}, {"description": "The number of CANNOT_UPDATE_COLUMN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_UPDATE_COLUMN"}, {"description": "The number of CANNOT_ADD_DIFFERENT_AGGREGATE_STATES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_ADD_DIFFERENT_AGGREGATE_STATES"}, {"description": "The number of UNSUPPORTED_URI_SCHEME errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNSUPPORTED_URI_SCHEME"}, {"description": "The number of CANNOT_GETTIMEOFDAY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_GETTIMEOFDAY"}, {"description": "The number of CANNOT_LINK errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_LINK"}, {"description": "The number of SYSTEM_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SYSTEM_ERROR"}, {"description": "The number of CANNOT_COMPILE_REGEXP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_COMPILE_REGEXP"}, {"description": "The number of FAILED_TO_GETPWUID errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FAILED_TO_GETPWUID"}, {"description": "The number of MISMATCHING_USERS_FOR_PROCESS_AND_DATA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MISMATCHING_USERS_FOR_PROCESS_AND_DATA"}, {"description": "The number of ILLEGAL_SYNTAX_FOR_CODEC_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_SYNTAX_FOR_CODEC_TYPE"}, {"description": "The number of UNKNOWN_CODEC errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_CODEC"}, {"description": "The number of ILLEGAL_CODEC_PARAMETER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_CODEC_PARAMETER"}, {"description": "The number of CANNOT_PARSE_PROTOBUF_SCHEMA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_PROTOBUF_SCHEMA"}, {"description": "The number of NO_COLUMN_SERIALIZED_TO_REQUIRED_PROTOBUF_FIELD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_COLUMN_SERIALIZED_TO_REQUIRED_PROTOBUF_FIELD"}, {"description": "The number of PROTOBUF_BAD_CAST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PROTOBUF_BAD_CAST"}, {"description": "The number of PROTOBUF_FIELD_NOT_REPEATED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PROTOBUF_FIELD_NOT_REPEATED"}, {"description": "The number of DATA_TYPE_CANNOT_BE_PROMOTED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATA_TYPE_CANNOT_BE_PROMOTED"}, {"description": "The number of CANNOT_SCHEDULE_TASK errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SCHEDULE_TASK"}, {"description": "The number of INVALID_LIMIT_EXPRESSION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_LIMIT_EXPRESSION"}, {"description": "The number of CANNOT_PARSE_DOMAIN_VALUE_FROM_STRING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_DOMAIN_VALUE_FROM_STRING"}, {"description": "The number of BAD_DATABASE_FOR_TEMPORARY_TABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_DATABASE_FOR_TEMPORARY_TABLE"}, {"description": "The number of NO_COLUMNS_SERIALIZED_TO_PROTOBUF_FIELDS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_COLUMNS_SERIALIZED_TO_PROTOBUF_FIELDS"}, {"description": "The number of UNKNOWN_PROTOBUF_FORMAT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_PROTOBUF_FORMAT"}, {"description": "The number of CANNOT_MPROTECT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_MPROTECT"}, {"description": "The number of FUNCTION_NOT_ALLOWED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FUNCTION_NOT_ALLOWED"}, {"description": "The number of HYPERSCAN_CANNOT_SCAN_TEXT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_HYPERSCAN_CANNOT_SCAN_TEXT"}, {"description": "The number of BROTLI_READ_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BROTLI_READ_FAILED"}, {"description": "The number of BROTLI_WRITE_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BROTLI_WRITE_FAILED"}, {"description": "The number of BAD_TTL_EXPRESSION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_TTL_EXPRESSION"}, {"description": "The number of BAD_TTL_FILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_TTL_FILE"}, {"description": "The number of SETTING_CONSTRAINT_VIOLATION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SETTING_CONSTRAINT_VIOLATION"}, {"description": "The number of MYSQL_CLIENT_INSUFFICIENT_CAPABILITIES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MYSQL_CLIENT_INSUFFICIENT_CAPABILITIES"}, {"description": "The number of OPENSSL_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_OPENSSL_ERROR"}, {"description": "The number of SUSPICIOUS_TYPE_FOR_LOW_CARDINALITY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SUSPICIOUS_TYPE_FOR_LOW_CARDINALITY"}, {"description": "The number of UNKNOWN_QUERY_PARAMETER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_QUERY_PARAMETER"}, {"description": "The number of BAD_QUERY_PARAMETER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_QUERY_PARAMETER"}, {"description": "The number of CANNOT_UNLINK errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_UNLINK"}, {"description": "The number of CANNOT_SET_THREAD_PRIORITY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SET_THREAD_PRIORITY"}, {"description": "The number of CANNOT_CREATE_TIMER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CREATE_TIMER"}, {"description": "The number of CANNOT_SET_TIMER_PERIOD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SET_TIMER_PERIOD"}, {"description": "The number of CANNOT_FCNTL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_FCNTL"}, {"description": "The number of CANNOT_PARSE_ELF errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_ELF"}, {"description": "The number of CANNOT_PARSE_DWARF errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_DWARF"}, {"description": "The number of INSECURE_PATH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INSECURE_PATH"}, {"description": "The number of CANNOT_PARSE_BOOL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_BOOL"}, {"description": "The number of CANNOT_PTHREAD_ATTR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PTHREAD_ATTR"}, {"description": "The number of VIOLATED_CONSTRAINT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_VIOLATED_CONSTRAINT"}, {"description": "The number of QUERY_IS_NOT_SUPPORTED_IN_LIVE_VIEW errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUERY_IS_NOT_SUPPORTED_IN_LIVE_VIEW"}, {"description": "The number of INVALID_SETTING_VALUE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_SETTING_VALUE"}, {"description": "The number of READONLY_SETTING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_READONLY_SETTING"}, {"description": "The number of DEADLOCK_AVOIDED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DEADLOCK_AVOIDED"}, {"description": "The number of INVALID_TEMPLATE_FORMAT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_TEMPLATE_FORMAT"}, {"description": "The number of INVALID_WITH_FILL_EXPRESSION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_WITH_FILL_EXPRESSION"}, {"description": "The number of WITH_TIES_WITHOUT_ORDER_BY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_WITH_TIES_WITHOUT_ORDER_BY"}, {"description": "The number of INVALID_USAGE_OF_INPUT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_USAGE_OF_INPUT"}, {"description": "The number of UNKNOWN_POLICY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_POLICY"}, {"description": "The number of UNKNOWN_DISK errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_DISK"}, {"description": "The number of UNKNOWN_PROTOCOL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_PROTOCOL"}, {"description": "The number of PATH_ACCESS_DENIED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PATH_ACCESS_DENIED"}, {"description": "The number of DICTIONARY_ACCESS_DENIED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DICTIONARY_ACCESS_DENIED"}, {"description": "The number of TOO_MANY_REDIRECTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_REDIRECTS"}, {"description": "The number of INTERNAL_REDIS_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INTERNAL_REDIS_ERROR"}, {"description": "The number of CANNOT_GET_CREATE_DICTIONARY_QUERY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_GET_CREATE_DICTIONARY_QUERY"}, {"description": "The number of INCORRECT_DICTIONARY_DEFINITION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_DICTIONARY_DEFINITION"}, {"description": "The number of CANNOT_FORMAT_DATETIME errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_FORMAT_DATETIME"}, {"description": "The number of UNACCEPTABLE_URL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNACCEPTABLE_URL"}, {"description": "The number of ACCESS_ENTITY_NOT_FOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ACCESS_ENTITY_NOT_FOUND"}, {"description": "The number of ACCESS_ENTITY_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ACCESS_ENTITY_ALREADY_EXISTS"}, {"description": "The number of ACCESS_STORAGE_READONLY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ACCESS_STORAGE_READONLY"}, {"description": "The number of QUOTA_REQUIRES_CLIENT_KEY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUOTA_REQUIRES_CLIENT_KEY"}, {"description": "The number of ACCESS_DENIED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ACCESS_DENIED"}, {"description": "The number of LIMIT_BY_WITH_TIES_IS_NOT_SUPPORTED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_LIMIT_BY_WITH_TIES_IS_NOT_SUPPORTED"}, {"description": "The number of S3_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_S3_ERROR"}, {"description": "The number of AZURE_BLOB_STORAGE_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_AZURE_BLOB_STORAGE_ERROR"}, {"description": "The number of CANNOT_CREATE_DATABASE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CREATE_DATABASE"}, {"description": "The number of CANNOT_SIGQUEUE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SIGQUEUE"}, {"description": "The number of AGGREGATE_FUNCTION_THROW errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_AGGREGATE_FUNCTION_THROW"}, {"description": "The number of FILE_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FILE_ALREADY_EXISTS"}, {"description": "The number of UNABLE_TO_SKIP_UNUSED_SHARDS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNABLE_TO_SKIP_UNUSED_SHARDS"}, {"description": "The number of UNKNOWN_ACCESS_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_ACCESS_TYPE"}, {"description": "The number of INVALID_GRANT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_GRANT"}, {"description": "The number of CACHE_DICTIONARY_UPDATE_FAIL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CACHE_DICTIONARY_UPDATE_FAIL"}, {"description": "The number of UNKNOWN_ROLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_ROLE"}, {"description": "The number of SET_NON_GRANTED_ROLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SET_NON_GRANTED_ROLE"}, {"description": "The number of UNKNOWN_PART_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_PART_TYPE"}, {"description": "The number of ACCESS_STORAGE_FOR_INSERTION_NOT_FOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ACCESS_STORAGE_FOR_INSERTION_NOT_FOUND"}, {"description": "The number of INCORRECT_ACCESS_ENTITY_DEFINITION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_ACCESS_ENTITY_DEFINITION"}, {"description": "The number of AUTHENTICATION_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_AUTHENTICATION_FAILED"}, {"description": "The number of CANNOT_ASSIGN_ALTER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_ASSIGN_ALTER"}, {"description": "The number of CANNOT_COMMIT_OFFSET errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_COMMIT_OFFSET"}, {"description": "The number of NO_REMOTE_SHARD_AVAILABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_REMOTE_SHARD_AVAILABLE"}, {"description": "The number of CANNOT_DETACH_DICTIONARY_AS_TABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_DETACH_DICTIONARY_AS_TABLE"}, {"description": "The number of ATOMIC_RENAME_FAIL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ATOMIC_RENAME_FAIL"}, {"description": "The number of UNKNOWN_ROW_POLICY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_ROW_POLICY"}, {"description": "The number of ALTER_OF_COLUMN_IS_FORBIDDEN errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ALTER_OF_COLUMN_IS_FORBIDDEN"}, {"description": "The number of INCORRECT_DISK_INDEX errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_DISK_INDEX"}, {"description": "The number of NO_SUITABLE_FUNCTION_IMPLEMENTATION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_SUITABLE_FUNCTION_IMPLEMENTATION"}, {"description": "The number of CASSANDRA_INTERNAL_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CASSANDRA_INTERNAL_ERROR"}, {"description": "The number of NOT_A_LEADER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NOT_A_LEADER"}, {"description": "The number of CANNOT_CONNECT_RABBITMQ errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CONNECT_RABBITMQ"}, {"description": "The number of CANNOT_FSTAT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_FSTAT"}, {"description": "The number of LDAP_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_LDAP_ERROR"}, {"description": "The number of UNKNOWN_RAID_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_RAID_TYPE"}, {"description": "The number of CANNOT_RESTORE_FROM_FIELD_DUMP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_RESTORE_FROM_FIELD_DUMP"}, {"description": "The number of ILLEGAL_MYSQL_VARIABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_MYSQL_VARIABLE"}, {"description": "The number of MYSQL_SYNTAX_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MYSQL_SYNTAX_ERROR"}, {"description": "The number of CANNOT_BIND_RABBITMQ_EXCHANGE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_BIND_RABBITMQ_EXCHANGE"}, {"description": "The number of CANNOT_DECLARE_RABBITMQ_EXCHANGE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_DECLARE_RABBITMQ_EXCHANGE"}, {"description": "The number of CANNOT_CREATE_RABBITMQ_QUEUE_BINDING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CREATE_RABBITMQ_QUEUE_BINDING"}, {"description": "The number of CANNOT_REMOVE_RABBITMQ_EXCHANGE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_REMOVE_RABBITMQ_EXCHANGE"}, {"description": "The number of UNKNOWN_MYSQL_DATATYPES_SUPPORT_LEVEL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_MYSQL_DATATYPES_SUPPORT_LEVEL"}, {"description": "The number of ROW_AND_ROWS_TOGETHER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ROW_AND_ROWS_TOGETHER"}, {"description": "The number of FIRST_AND_NEXT_TOGETHER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FIRST_AND_NEXT_TOGETHER"}, {"description": "The number of NO_ROW_DELIMITER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_ROW_DELIMITER"}, {"description": "The number of INVALID_RAID_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_RAID_TYPE"}, {"description": "The number of UNKNOWN_VOLUME errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_VOLUME"}, {"description": "The number of DATA_TYPE_CANNOT_BE_USED_IN_KEY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATA_TYPE_CANNOT_BE_USED_IN_KEY"}, {"description": "The number of UNRECOGNIZED_ARGUMENTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNRECOGNIZED_ARGUMENTS"}, {"description": "The number of LZMA_STREAM_ENCODER_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_LZMA_STREAM_ENCODER_FAILED"}, {"description": "The number of LZMA_STREAM_DECODER_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_LZMA_STREAM_DECODER_FAILED"}, {"description": "The number of ROCKSDB_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ROCKSDB_ERROR"}, {"description": "The number of SYNC_MYSQL_USER_ACCESS_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SYNC_MYSQL_USER_ACCESS_ERROR"}, {"description": "The number of UNKNOWN_UNION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_UNION"}, {"description": "The number of EXPECTED_ALL_OR_DISTINCT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EXPECTED_ALL_OR_DISTINCT"}, {"description": "The number of INVALID_GRPC_QUERY_INFO errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_GRPC_QUERY_INFO"}, {"description": "The number of ZSTD_ENCODER_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ZSTD_ENCODER_FAILED"}, {"description": "The number of ZSTD_DECODER_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ZSTD_DECODER_FAILED"}, {"description": "The number of TLD_LIST_NOT_FOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TLD_LIST_NOT_FOUND"}, {"description": "The number of CANNOT_READ_MAP_FROM_TEXT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_READ_MAP_FROM_TEXT"}, {"description": "The number of INTERSERVER_SCHEME_DOESNT_MATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INTERSERVER_SCHEME_DOESNT_MATCH"}, {"description": "The number of TOO_MANY_PARTITIONS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_PARTITIONS"}, {"description": "The number of CANNOT_RMDIR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_RMDIR"}, {"description": "The number of DUPLICATED_PART_UUIDS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DUPLICATED_PART_UUIDS"}, {"description": "The number of RAFT_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_RAFT_ERROR"}, {"description": "The number of MULTIPLE_COLUMNS_SERIALIZED_TO_SAME_PROTOBUF_FIELD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MULTIPLE_COLUMNS_SERIALIZED_TO_SAME_PROTOBUF_FIELD"}, {"description": "The number of DATA_TYPE_INCOMPATIBLE_WITH_PROTOBUF_FIELD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATA_TYPE_INCOMPATIBLE_WITH_PROTOBUF_FIELD"}, {"description": "The number of DATABASE_REPLICATION_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATABASE_REPLICATION_FAILED"}, {"description": "The number of TOO_MANY_QUERY_PLAN_OPTIMIZATIONS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_QUERY_PLAN_OPTIMIZATIONS"}, {"description": "The number of EPOLL_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_EPOLL_ERROR"}, {"description": "The number of DISTRIBUTED_TOO_MANY_PENDING_BYTES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DISTRIBUTED_TOO_MANY_PENDING_BYTES"}, {"description": "The number of UNKNOWN_SNAPSHOT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_SNAPSHOT"}, {"description": "The number of KERBEROS_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_KERBEROS_ERROR"}, {"description": "The number of INVALID_SHARD_ID errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_SHARD_ID"}, {"description": "The number of INVALID_FORMAT_INSERT_QUERY_WITH_DATA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_FORMAT_INSERT_QUERY_WITH_DATA"}, {"description": "The number of INCORRECT_PART_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCORRECT_PART_TYPE"}, {"description": "The number of CANNOT_SET_ROUNDING_MODE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SET_ROUNDING_MODE"}, {"description": "The number of TOO_LARGE_DISTRIBUTED_DEPTH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_LARGE_DISTRIBUTED_DEPTH"}, {"description": "The number of NO_SUCH_PROJECTION_IN_TABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_SUCH_PROJECTION_IN_TABLE"}, {"description": "The number of ILLEGAL_PROJECTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_PROJECTION"}, {"description": "The number of PROJECTION_NOT_USED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_PROJECTION_NOT_USED"}, {"description": "The number of CANNOT_PARSE_YAML errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_YAML"}, {"description": "The number of CANNOT_CREATE_FILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CREATE_FILE"}, {"description": "The number of CONCURRENT_ACCESS_NOT_SUPPORTED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CONCURRENT_ACCESS_NOT_SUPPORTED"}, {"description": "The number of DISTRIBUTED_BROKEN_BATCH_INFO errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DISTRIBUTED_BROKEN_BATCH_INFO"}, {"description": "The number of DISTRIBUTED_BROKEN_BATCH_FILES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DISTRIBUTED_BROKEN_BATCH_FILES"}, {"description": "The number of CANNOT_SYSCONF errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SYSCONF"}, {"description": "The number of SQLITE_ENGINE_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SQLITE_ENGINE_ERROR"}, {"description": "The number of DATA_ENCRYPTION_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_DATA_ENCRYPTION_ERROR"}, {"description": "The number of ZERO_COPY_REPLICATION_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ZERO_COPY_REPLICATION_ERROR"}, {"description": "The number of BZIP2_STREAM_DECODER_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BZIP2_STREAM_DECODER_FAILED"}, {"description": "The number of BZIP2_STREAM_ENCODER_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BZIP2_STREAM_ENCODER_FAILED"}, {"description": "The number of INTERSECT_OR_EXCEPT_RESULT_STRUCTURES_MISMATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INTERSECT_OR_EXCEPT_RESULT_STRUCTURES_MISMATCH"}, {"description": "The number of NO_SUCH_ERROR_CODE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_SUCH_ERROR_CODE"}, {"description": "The number of BACKUP_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BACKUP_ALREADY_EXISTS"}, {"description": "The number of BACKUP_NOT_FOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BACKUP_NOT_FOUND"}, {"description": "The number of BACKUP_VERSION_NOT_SUPPORTED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BACKUP_VERSION_NOT_SUPPORTED"}, {"description": "The number of BACKUP_DAMAGED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BACKUP_DAMAGED"}, {"description": "The number of NO_BASE_BACKUP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_BASE_BACKUP"}, {"description": "The number of WRONG_BASE_BACKUP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_WRONG_BASE_BACKUP"}, {"description": "The number of BACKUP_ENTRY_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BACKUP_ENTRY_ALREADY_EXISTS"}, {"description": "The number of BACKUP_ENTRY_NOT_FOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BACKUP_ENTRY_NOT_FOUND"}, {"description": "The number of BACKUP_IS_EMPTY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BACKUP_IS_EMPTY"}, {"description": "The number of CANNOT_RESTORE_DATABASE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_RESTORE_DATABASE"}, {"description": "The number of CANNOT_RESTORE_TABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_RESTORE_TABLE"}, {"description": "The number of FUNCTION_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FUNCTION_ALREADY_EXISTS"}, {"description": "The number of CANNOT_DROP_FUNCTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_DROP_FUNCTION"}, {"description": "The number of CANNOT_CREATE_RECURSIVE_FUNCTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CREATE_RECURSIVE_FUNCTION"}, {"description": "The number of POSTGRESQL_CONNECTION_FAILURE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_POSTGRESQL_CONNECTION_FAILURE"}, {"description": "The number of CANNOT_ADVISE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_ADVISE"}, {"description": "The number of UNKNOWN_READ_METHOD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_READ_METHOD"}, {"description": "The number of LZ4_ENCODER_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_LZ4_ENCODER_FAILED"}, {"description": "The number of LZ4_DECODER_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_LZ4_DECODER_FAILED"}, {"description": "The number of POSTGRESQL_REPLICATION_INTERNAL_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_POSTGRESQL_REPLICATION_INTERNAL_ERROR"}, {"description": "The number of QUERY_NOT_ALLOWED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUERY_NOT_ALLOWED"}, {"description": "The number of CANNOT_NORMALIZE_STRING errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_NORMALIZE_STRING"}, {"description": "The number of CANNOT_PARSE_CAPN_PROTO_SCHEMA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_CAPN_PROTO_SCHEMA"}, {"description": "The number of CAPN_PROTO_BAD_CAST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CAPN_PROTO_BAD_CAST"}, {"description": "The number of BAD_FILE_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BAD_FILE_TYPE"}, {"description": "The number of IO_SETUP_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_IO_SETUP_ERROR"}, {"description": "The number of CANNOT_SKIP_UNKNOWN_FIELD errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SKIP_UNKNOWN_FIELD"}, {"description": "The number of BACKUP_ENGINE_NOT_FOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_BACKUP_ENGINE_NOT_FOUND"}, {"description": "The number of OFFSET_FETCH_WITHOUT_ORDER_BY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_OFFSET_FETCH_WITHOUT_ORDER_BY"}, {"description": "The number of HTTP_RANGE_NOT_SATISFIABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_HTTP_RANGE_NOT_SATISFIABLE"}, {"description": "The number of HAVE_DEPENDENT_OBJECTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_HAVE_DEPENDENT_OBJECTS"}, {"description": "The number of UNKNOWN_FILE_SIZE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_FILE_SIZE"}, {"description": "The number of UNEXPECTED_DATA_AFTER_PARSED_VALUE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNEXPECTED_DATA_AFTER_PARSED_VALUE"}, {"description": "The number of QUERY_IS_NOT_SUPPORTED_IN_WINDOW_VIEW errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUERY_IS_NOT_SUPPORTED_IN_WINDOW_VIEW"}, {"description": "The number of MONGODB_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MONGODB_ERROR"}, {"description": "The number of CANNOT_POLL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_POLL"}, {"description": "The number of CANNOT_EXTRACT_TABLE_STRUCTURE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_EXTRACT_TABLE_STRUCTURE"}, {"description": "The number of INVALID_TABLE_OVERRIDE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_TABLE_OVERRIDE"}, {"description": "The number of SNAPPY_UNCOMPRESS_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SNAPPY_UNCOMPRESS_FAILED"}, {"description": "The number of SNAPPY_COMPRESS_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SNAPPY_COMPRESS_FAILED"}, {"description": "The number of NO_HIVEMETASTORE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NO_HIVEMETASTORE"}, {"description": "The number of CANNOT_APPEND_TO_FILE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_APPEND_TO_FILE"}, {"description": "The number of CANNOT_PACK_ARCHIVE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PACK_ARCHIVE"}, {"description": "The number of CANNOT_UNPACK_ARCHIVE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_UNPACK_ARCHIVE"}, {"description": "The number of NUMBER_OF_DIMENSIONS_MISMATCHED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NUMBER_OF_DIMENSIONS_MISMATCHED"}, {"description": "The number of CANNOT_BACKUP_TABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_BACKUP_TABLE"}, {"description": "The number of WRONG_DDL_RENAMING_SETTINGS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_WRONG_DDL_RENAMING_SETTINGS"}, {"description": "The number of INVALID_TRANSACTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_TRANSACTION"}, {"description": "The number of SERIALIZATION_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_SERIALIZATION_ERROR"}, {"description": "The number of CAPN_PROTO_BAD_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CAPN_PROTO_BAD_TYPE"}, {"description": "The number of ONLY_NULLS_WHILE_READING_SCHEMA errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ONLY_NULLS_WHILE_READING_SCHEMA"}, {"description": "The number of CANNOT_PARSE_BACKUP_SETTINGS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_BACKUP_SETTINGS"}, {"description": "The number of WRONG_BACKUP_SETTINGS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_WRONG_BACKUP_SETTINGS"}, {"description": "The number of FAILED_TO_SYNC_BACKUP_OR_RESTORE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FAILED_TO_SYNC_BAC<PERSON>UP_OR_RESTORE"}, {"description": "The number of UNKNOWN_STATUS_OF_TRANSACTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_STATUS_OF_TRANSACTION"}, {"description": "The number of HDFS_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_HDFS_ERROR"}, {"description": "The number of CANNOT_SEND_SIGNAL errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_SEND_SIGNAL"}, {"description": "The number of FS_METADATA_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FS_METADATA_ERROR"}, {"description": "The number of INCONSISTENT_METADATA_FOR_BACKUP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INCONSISTENT_METADATA_FOR_BACKUP"}, {"description": "The number of ACCESS_STORAGE_DOESNT_ALLOW_BACKUP errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ACCESS_STORAGE_DOESNT_ALLOW_BACKUP"}, {"description": "The number of CANNOT_CONNECT_NATS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_CONNECT_NATS"}, {"description": "The number of NOT_INITIALIZED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NOT_INITIALIZED"}, {"description": "The number of INVALID_STATE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_STATE"}, {"description": "The number of NAMED_COLLECTION_DOESNT_EXIST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NAMED_COLLECTION_DOESNT_EXIST"}, {"description": "The number of NAMED_COLLECTION_ALREADY_EXISTS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NAMED_COLLECTION_ALREADY_EXISTS"}, {"description": "The number of NAMED_COLLECTION_IS_IMMUTABLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_NAMED_COLLECTION_IS_IMMUTABLE"}, {"description": "The number of INVALID_SCHEDULER_NODE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_SCHEDULER_NODE"}, {"description": "The number of RESOURCE_ACCESS_DENIED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_RESOURCE_ACCESS_DENIED"}, {"description": "The number of RESOURCE_NOT_FOUND errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_RESOURCE_NOT_FOUND"}, {"description": "The number of CANNOT_PARSE_IPV4 errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_IPV4"}, {"description": "The number of CANNOT_PARSE_IPV6 errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_PARSE_IPV6"}, {"description": "The number of THREAD_WAS_CANCELED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_THREAD_WAS_CANCELED"}, {"description": "The number of IO_URING_INIT_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_IO_URING_INIT_FAILED"}, {"description": "The number of IO_URING_SUBMIT_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_IO_URING_SUBMIT_ERROR"}, {"description": "The number of MIXED_ACCESS_PARAMETER_TYPES errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_MIXED_ACCESS_PARAMETER_TYPES"}, {"description": "The number of UNKNOWN_ELEMENT_OF_ENUM errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_UNKNOWN_ELEMENT_OF_ENUM"}, {"description": "The number of TOO_MANY_MUTATIONS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_MUTATIONS"}, {"description": "The number of AWS_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_AWS_ERROR"}, {"description": "The number of ASYNC_LOAD_CYCLE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ASYNC_LOAD_CYCLE"}, {"description": "The number of ASYNC_LOAD_FAILED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ASYNC_LOAD_FAILED"}, {"description": "The number of ASYNC_LOAD_CANCELED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ASYNC_LOAD_CANCELED"}, {"description": "The number of CANNOT_RESTORE_TO_NONENCRYPTED_DISK errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_RESTORE_TO_NONENCRYPTED_DISK"}, {"description": "The number of INVALID_REDIS_STORAGE_TYPE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_REDIS_STORAGE_TYPE"}, {"description": "The number of INVALID_REDIS_TABLE_STRUCTURE errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_REDIS_TABLE_STRUCTURE"}, {"description": "The number of USER_SESSION_LIMIT_EXCEEDED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_USER_SESSION_LIMIT_EXCEEDED"}, {"description": "The number of CLUSTER_DOESNT_EXIST errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CLUSTER_DOESNT_EXIST"}, {"description": "The number of CLIENT_INFO_DOES_NOT_MATCH errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CLIENT_INFO_DOES_NOT_MATCH"}, {"description": "The number of INVALID_IDENTIFIER errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_INVALID_IDENTIFIER"}, {"description": "The number of QUERY_CACHE_USED_WITH_NONDETERMINISTIC_FUNCTIONS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_QUERY_CACHE_USED_WITH_NONDETERMINISTIC_FUNCTIONS"}, {"description": "The number of TABLE_NOT_EMPTY errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TABLE_NOT_EMPTY"}, {"description": "The number of LIBSSH_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_LIBSSH_ERROR"}, {"description": "The number of GCP_ERROR errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_GCP_ERROR"}, {"description": "The number of ILLEGAL_STATISTIC errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ILLEGAL_STATISTIC"}, {"description": "The number of CANNOT_GET_REPLICATED_DATABASE_SNAPSHOT errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_CANNOT_GET_REPLICATED_DATABASE_SNAPSHOT"}, {"description": "The number of FAULT_INJECTED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FAULT_INJECTED"}, {"description": "The number of FILECACHE_ACCESS_DENIED errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_FILECACHE_ACCESS_DENIED"}, {"description": "The number of TOO_MANY_MATERIALIZED_VIEWS errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_TOO_MANY_MATERIALIZED_VIEWS"}, {"description": "The number of KEEPER_EXCEPTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_KEEPER_EXCEPTION"}, {"description": "The number of POCO_EXCEPTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_POCO_EXCEPTION"}, {"description": "The number of STD_EXCEPTION errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_STD_EXCEPTION"}, {"description": "The total number of errors since last server restart", "type": "counter", "unit": "Number", "name": "ClickHouseErrorMetric_ALL"}]}