{"description": "Overview of AWS Lambda functions", "image": "data:image/svg+xml,%3Csvg%20width%3D%22800px%22%20height%3D%22800px%22%20viewBox%3D%220%200%2016%2016%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%3E%3Cpath%20fill%3D%22%23FA7E14%22%20d%3D%22M7.983%208.37c-.053.073-.098.133-.141.194L5.775%2011.5c-.64.91-1.282%201.82-1.924%202.73a.128.128%200%2001-.092.051c-.906-.007-1.813-.017-2.719-.028-.01%200-.02-.003-.04-.006a.455.455%200%2001.025-.053%2013977.496%2013977.496%200%20015.446-8.146c.092-.138.188-.273.275-.413a.165.165%200%2000.018-.124c-.167-.515-.338-1.03-.508-1.543-.073-.22-.15-.44-.218-.66-.022-.072-.059-.094-.134-.093-.57.002-1.136.001-1.704.001-.108%200-.108%200-.108-.103%200-.674%200-1.347-.002-2.021%200-.075.026-.092.099-.092%201.143.002%202.286.002%203.43%200a.113.113%200%2001.***************%200%2001.045.061%2018266.184%2018266.184%200%20003.92%209.51c.218.53.438%201.059.654%**************.**************.6-.178%201.2-.352%201.8-.531.075-.023.102-.008.126.064.204.62.412%201.239.62%201.858l.02.073c-.043.015-.083.032-.124.043l-4.085%201.25c-.065.02-.085%200-.106-.054l-1.25-3.048-1.226-2.984-.183-.449c-.01-.026-.023-.048-.043-.087z%22%2F%3E%3C%2Fsvg%3E", "layout": [{"h": 6, "i": "877bb5c8-331c-492f-b666-2054c2ae39bd", "moved": false, "static": false, "w": 6, "x": 0, "y": 0}, {"h": 6, "i": "b038520d-0756-4e46-a915-12a2f19a0254", "moved": false, "static": false, "w": 6, "x": 6, "y": 0}, {"h": 6, "i": "2516c785-b025-49b3-aeb4-a4735ccb2709", "moved": false, "static": false, "w": 6, "x": 0, "y": 6}, {"h": 6, "i": "6354ea62-e82b-4323-a33d-eef92519e843", "moved": false, "static": false, "w": 6, "x": 6, "y": 6}, {"h": 6, "i": "853d3a92-b396-4064-8762-18d7487989e0", "moved": false, "static": false, "w": 6, "x": 0, "y": 12}, {"h": 6, "i": "ae6d7c81-d921-4d4c-95ec-6b42d900ea45", "moved": false, "static": false, "w": 6, "x": 6, "y": 12}, {"h": 6, "i": "4119a1e5-32a8-4859-96e9-a5451114782b", "moved": false, "static": false, "w": 6, "x": 0, "y": 18}], "panelMap": {}, "tags": [], "title": "AWS Lambda Overview", "uploadedGrafana": false, "variables": {"5c57a94c-e7a1-4c20-83a3-3a779b9da48e": {"allSelected": false, "customValue": "", "description": "AWS Account", "id": "5c57a94c-e7a1-4c20-83a3-3a779b9da48e", "key": "5c57a94c-e7a1-4c20-83a3-3a779b9da48e", "modificationUUID": "7113f462-0b71-459e-8226-f6292350b34a", "multiSelect": false, "name": "Account", "order": 0, "queryValue": "SELECT JSONExtractString(labels, 'cloud_account_id') as cloud_account_id\nFROM signoz_metrics.distributed_time_series_v4_1day\nWHERE \n    metric_name like 'aws_Lambda_Invocations_sum'\nGROUP BY cloud_account_id\n\n", "showALLOption": false, "sort": "DISABLED", "textboxValue": "", "type": "QUERY"}, "8e6124f0-077e-4a45-8a34-c5d5ce1c26c7": {"allSelected": false, "customValue": "", "description": "AWS Region", "id": "8e6124f0-077e-4a45-8a34-c5d5ce1c26c7", "modificationUUID": "d090f070-1d58-4f55-87d2-91778e3fde00", "multiSelect": false, "name": "Region", "order": 0, "queryValue": "SELECT JSONExtractString(labels, 'cloud_region') as cloud_region\nFROM signoz_metrics.distributed_time_series_v4_1day\nWHERE \n    metric_name like 'aws_Lambda_Invocations_sum'\n    and JSONExtractString(labels, 'cloud_account_id') IN {{.Account}}\nGROUP BY cloud_region\n", "showALLOption": false, "sort": "DISABLED", "textboxValue": "", "type": "QUERY"}}, "version": "v4", "widgets": [{"bucketCount": 30, "bucketWidth": 0, "columnUnits": {}, "description": "The number of times that your function code is invoked, including successful invocations and invocations that result in a function error. Invocations aren't recorded if the invocation request is throttled or otherwise results in an invocation error. The value of Invocations equals the number of requests billed.\n\nSee more at https://docs.aws.amazon.com/lambda/latest/dg/monitoring-metrics-types.html", "fillSpans": false, "id": "877bb5c8-331c-492f-b666-2054c2ae39bd", "isStacked": false, "mergeAllActiveQueries": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "aws_Lambda_Invocations_sum--float64--Gauge--true", "isColumn": true, "isJSON": false, "key": "aws_Lambda_Invocations_sum", "type": "Gauge"}, "aggregateOperator": "sum", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "49d33567", "key": {"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, "op": "=", "value": "$Account"}, {"id": "b9dfa1c9", "key": {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, "op": "=", "value": "$Region"}, {"id": "86defeb5", "key": {"dataType": "", "isColumn": false, "key": "FunctionName", "type": ""}, "op": "exists", "value": ""}, {"id": "f89a10c4", "key": {"dataType": "", "isColumn": false, "key": "Resource", "type": ""}, "op": "nexists", "value": ""}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}], "having": [], "legend": "{{FunctionName}}", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "sum"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "a0e81905-5968-4dbc-b601-29bd491dec11", "promql": [{"disabled": false, "legend": "", "name": "A", "query": "aws_Lambda_Invocations_sum{cloud_region=\"us-east-2\"}"}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "stackedBarChart": false, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Invocations", "yAxisUnit": "none"}, {"bucketCount": 30, "bucketWidth": 0, "columnUnits": {}, "description": "The amount of time that your function code spends processing an event. The billed duration for an invocation is the value of Duration rounded up to the nearest millisecond. Duration does not include cold start time.\n\nSee more at https://docs.aws.amazon.com/lambda/latest/dg/monitoring-metrics-types.html", "fillSpans": false, "id": "b038520d-0756-4e46-a915-12a2f19a0254", "isStacked": false, "mergeAllActiveQueries": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "aws_Lambda_Duration_max--float64--Gauge--true", "isColumn": true, "isJSON": false, "key": "aws_Lambda_Duration_max", "type": "Gauge"}, "aggregateOperator": "max", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "af05252d", "key": {"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, "op": "=", "value": "$Account"}, {"id": "983efea5", "key": {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, "op": "=", "value": "$Region"}, {"id": "88eb3092", "key": {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}, "op": "exists", "value": ""}, {"id": "a35c6406", "key": {"dataType": "string", "id": "Resource--string--tag--false", "isColumn": false, "isJSON": false, "key": "Resource", "type": "tag"}, "op": "nexists", "value": ""}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}], "having": [], "legend": "{{FunctionName}}", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "max", "stepInterval": 60, "timeAggregation": "max"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "cfde56b0-9052-4f97-aa31-63b03653f73e", "promql": [{"disabled": false, "legend": "", "name": "A", "query": "aws_Lambda_Invocations_sum{cloud_region=\"us-east-2\"}"}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "stackedBarChart": false, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Max Duration", "yAxisUnit": "ms"}, {"bucketCount": 30, "bucketWidth": 0, "columnUnits": {}, "description": "The number of invocations that result in a function error. Function errors include exceptions that your code throws and exceptions that the Lambda runtime throws. The runtime returns errors for issues such as timeouts and configuration errors. To calculate the error rate, divide the value of Errors by the value of Invocations. Note that the timestamp on an error metric reflects when the function was invoked, not when the error occurred.\n\nSee more at https://docs.aws.amazon.com/lambda/latest/dg/monitoring-metrics-types.html", "fillSpans": false, "id": "2516c785-b025-49b3-aeb4-a4735ccb2709", "isStacked": false, "mergeAllActiveQueries": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "aws_Lambda_Errors_sum--float64--Gauge--true", "isColumn": true, "isJSON": false, "key": "aws_Lambda_Errors_sum", "type": "Gauge"}, "aggregateOperator": "sum", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "c67262c9", "key": {"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, "op": "=", "value": "$Account"}, {"id": "c5ccbbf4", "key": {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, "op": "=", "value": "$Region"}, {"id": "c1b278d1", "key": {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}, "op": "exists", "value": ""}, {"id": "a45d80e1", "key": {"dataType": "string", "id": "Resource--string--tag--false", "isColumn": false, "isJSON": false, "key": "Resource", "type": "tag"}, "op": "nexists", "value": ""}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}], "having": [], "legend": "{{FunctionName}}", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "sum"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "edd02708-38ea-48ec-856e-25dade25acae", "promql": [{"disabled": false, "legend": "", "name": "A", "query": "aws_Lambda_Invocations_sum{cloud_region=\"us-east-2\"}"}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "stackedBarChart": false, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Errors", "yAxisUnit": "none"}, {"bucketCount": 30, "bucketWidth": 0, "columnUnits": {}, "description": "The number of invocation requests that are throttled. When all function instances are processing requests and no concurrency is available to scale up, Lambda rejects additional requests with a TooManyRequestsException error. Throttled requests and other invocation errors don't count as either Invocations or Errors.\n\nSee more at https://docs.aws.amazon.com/lambda/latest/dg/monitoring-metrics-types.html", "fillSpans": false, "id": "6354ea62-e82b-4323-a33d-eef92519e843", "isStacked": false, "mergeAllActiveQueries": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "aws_Lambda_Throttles_sum--float64--Gauge--true", "isColumn": true, "isJSON": false, "key": "aws_Lambda_Throttles_sum", "type": "Gauge"}, "aggregateOperator": "sum", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "6c956b7d", "key": {"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, "op": "=", "value": "$Account"}, {"id": "5fef840b", "key": {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, "op": "=", "value": "$Region"}, {"id": "6f892a9a", "key": {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}, "op": "exists", "value": ""}, {"id": "ce91320c", "key": {"dataType": "string", "id": "Resource--string--tag--false", "isColumn": false, "isJSON": false, "key": "Resource", "type": "tag"}, "op": "nexists", "value": ""}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}], "having": [], "legend": "{{FunctionName}}", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "sum"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "d37ca92b-1ffa-4638-95fb-eece9fd4b1d8", "promql": [{"disabled": false, "legend": "", "name": "A", "query": "aws_Lambda_Invocations_sum{cloud_region=\"us-east-2\"}"}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "stackedBarChart": false, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "<PERSON>hrottles", "yAxisUnit": "none"}, {"bucketCount": 30, "bucketWidth": 0, "columnUnits": {}, "description": "The number of events that Lambda successfully queues for processing. This metric provides insight into the number of events that a Lambda function receives. Monitor this metric and set alarms for thresholds to check for issues. For example, to detect an undesirable number of events sent to Lambda, and to quickly diagnose issues resulting from incorrect trigger or function configurations. Mismatches between AsyncEventsReceived and Invocations can indicate a disparity in processing, events being dropped, or a potential queue backlog.\n\nSee more at https://docs.aws.amazon.com/lambda/latest/dg/monitoring-metrics-types.html", "fillSpans": false, "id": "853d3a92-b396-4064-8762-18d7487989e0", "isStacked": false, "mergeAllActiveQueries": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "aws_Lambda_AsyncEventsReceived_sum--float64--Gauge--true", "isColumn": true, "isJSON": false, "key": "aws_Lambda_AsyncEventsReceived_sum", "type": "Gauge"}, "aggregateOperator": "sum", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "f4c6246b", "key": {"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, "op": "=", "value": "$Account"}, {"id": "5b7a75a1", "key": {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, "op": "=", "value": "$Region"}, {"id": "4e1ba051", "key": {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}, "op": "exists", "value": ""}, {"id": "5848f496", "key": {"dataType": "string", "id": "Resource--string--tag--false", "isColumn": false, "isJSON": false, "key": "Resource", "type": "tag"}, "op": "nexists", "value": ""}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}], "having": [], "legend": "{{FunctionName}}", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "sum"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "34a8224d-d977-46d4-bb10-c0064fcbdfb0", "promql": [{"disabled": false, "legend": "", "name": "A", "query": "aws_Lambda_Invocations_sum{cloud_region=\"us-east-2\"}"}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "stackedBarChart": false, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Async events received", "yAxisUnit": "none"}, {"bucketCount": 30, "bucketWidth": 0, "columnUnits": {}, "description": "The time between when Lamb<PERSON> successfully queues the event and when the function is invoked. The value of this metric increases when events are being retried due to invocation failures or throttling. Monitor this metric and set alarms for thresholds on different statistics for when a queue buildup occurs. To troubleshoot an increase in this metric, look at the Errors metric to identify function errors and the Throttles metric to identify concurrency issues.\n\nSee more at https://docs.aws.amazon.com/lambda/latest/dg/monitoring-metrics-types.html", "fillSpans": false, "id": "ae6d7c81-d921-4d4c-95ec-6b42d900ea45", "isStacked": false, "mergeAllActiveQueries": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "aws_Lambda_AsyncEventAge_max--float64--Gauge--true", "isColumn": true, "isJSON": false, "key": "aws_Lambda_AsyncEventAge_max", "type": "Gauge"}, "aggregateOperator": "max", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "1aee3626", "key": {"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, "op": "=", "value": "$Account"}, {"id": "11631fda", "key": {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, "op": "=", "value": "$Region"}, {"id": "c5ea0a17", "key": {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}, "op": "exists", "value": ""}, {"id": "1952b27e", "key": {"dataType": "string", "id": "Resource--string--tag--false", "isColumn": false, "isJSON": false, "key": "Resource", "type": "tag"}, "op": "nexists", "value": ""}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}], "having": [], "legend": "{{FunctionName}}", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "max", "stepInterval": 60, "timeAggregation": "max"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "6d779378-7013-449e-9eb2-ef85649fa790", "promql": [{"disabled": false, "legend": "", "name": "A", "query": "aws_Lambda_Invocations_sum{cloud_region=\"us-east-2\"}"}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "stackedBarChart": false, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Max Async Event Age", "yAxisUnit": "ms"}, {"bucketCount": 30, "bucketWidth": 0, "columnUnits": {}, "description": "The number of events that are dropped without successfully executing the function. If you configure a dead-letter queue (DLQ) or OnFailure destination, then events are sent there before they're dropped. Events are dropped for various reasons. For example, events can exceed the maximum event age or exhaust the maximum retry attempts, or reserved concurrency might be set to 0. To troubleshoot why events are dropped, look at the Errors metric to identify function errors and the Throttles metric to identify concurrency issues.\n\nSee more at https://docs.aws.amazon.com/lambda/latest/dg/monitoring-metrics-types.html", "fillSpans": false, "id": "4119a1e5-32a8-4859-96e9-a5451114782b", "isStacked": false, "mergeAllActiveQueries": false, "nullZeroValues": "zero", "opacity": "1", "panelTypes": "graph", "query": {"builder": {"queryData": [{"aggregateAttribute": {"dataType": "float64", "id": "aws_Lambda_AsyncEventsDropped_sum--float64--Gauge--true", "isColumn": true, "isJSON": false, "key": "aws_Lambda_AsyncEventsDropped_sum", "type": "Gauge"}, "aggregateOperator": "sum", "dataSource": "metrics", "disabled": false, "expression": "A", "filters": {"items": [{"id": "a8c65389", "key": {"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, "op": "=", "value": "$Account"}, {"id": "2ab205c8", "key": {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, "op": "=", "value": "$Region"}, {"id": "22d3c9b6", "key": {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}, "op": "exists", "value": ""}, {"id": "1e7060a6", "key": {"dataType": "string", "id": "Resource--string--tag--false", "isColumn": false, "isJSON": false, "key": "Resource", "type": "tag"}, "op": "nexists", "value": ""}], "op": "AND"}, "functions": [], "groupBy": [{"dataType": "string", "id": "cloud_account_id--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_account_id", "type": "tag"}, {"dataType": "string", "id": "cloud_region--string--tag--false", "isColumn": false, "isJSON": false, "key": "cloud_region", "type": "tag"}, {"dataType": "string", "id": "FunctionName--string--tag--false", "isColumn": false, "isJSON": false, "key": "FunctionName", "type": "tag"}], "having": [], "legend": "{{FunctionName}}", "limit": null, "orderBy": [], "queryName": "A", "reduceTo": "avg", "spaceAggregation": "sum", "stepInterval": 60, "timeAggregation": "sum"}], "queryFormulas": []}, "clickhouse_sql": [{"disabled": false, "legend": "", "name": "A", "query": ""}], "id": "7866783d-942a-4d92-b390-6ddbcb717bad", "promql": [{"disabled": false, "legend": "", "name": "A", "query": "aws_Lambda_Invocations_sum{cloud_region=\"us-east-2\"}"}], "queryType": "builder"}, "selectedLogFields": [{"dataType": "string", "name": "body", "type": ""}, {"dataType": "string", "name": "timestamp", "type": ""}], "selectedTracesFields": [{"dataType": "string", "id": "serviceName--string--tag--true", "isColumn": true, "isJSON": false, "key": "serviceName", "type": "tag"}, {"dataType": "string", "id": "name--string--tag--true", "isColumn": true, "isJSON": false, "key": "name", "type": "tag"}, {"dataType": "float64", "id": "durationNano--float64--tag--true", "isColumn": true, "isJSON": false, "key": "durationNano", "type": "tag"}, {"dataType": "string", "id": "httpMethod--string--tag--true", "isColumn": true, "isJSON": false, "key": "httpMethod", "type": "tag"}, {"dataType": "string", "id": "responseStatusCode--string--tag--true", "isColumn": true, "isJSON": false, "key": "responseStatusCode", "type": "tag"}], "softMax": 0, "softMin": 0, "stackedBarChart": false, "thresholds": [], "timePreferance": "GLOBAL_TIME", "title": "Async events dropped", "yAxisUnit": "none"}]}