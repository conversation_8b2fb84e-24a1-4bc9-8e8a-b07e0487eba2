#!/bin/bash

# Set the path to Go
export PATH=$PATH:/usr/local/go/bin

# Set environment variables for SigNoz
export SIGNOZ_INSTRUMENTATION_LOGS_LEVEL=debug
export SIGNOZ_SQLSTORE_SQLITE_PATH=signoz.db
export SIGNOZ_WEB_ENABLED=false
export SIGNOZ_JWT_SECRET=secret
export SIGNOZ_ALERTMANAGER_PROVIDER=signoz
export SIGNOZ_TELEMETRYSTORE_PROVIDER=clickhouse
export SIGNOZ_TELEMETRYSTORE_CLICKHOUSE_DSN=tcp://127.0.0.1:9000

# Run the backend with debugging enabled
cd /home/<USER>/Desktop/Signoz
/usr/local/go/bin/go run -race ./pkg/query-service/main.go --config ./conf/prometheus.yml --cluster cluster
